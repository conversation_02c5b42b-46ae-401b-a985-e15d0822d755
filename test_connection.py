#!/usr/bin/env python3
"""
Script de prueba rápida para conexión a base de datos
"""

import sys
import os

# Añadir el directorio del proyecto al path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_connection():
    """Probar conexión básica"""
    try:
        from config.settings import Settings
        from core.database_manager import DatabaseManager
        import asyncio
        
        print("🔍 Probando conexión a base de datos...")
        
        # Crear instancias
        settings = Settings()
        db_manager = DatabaseManager(settings)
        
        # Probar conexión
        async def test():
            try:
                await db_manager.initialize()
                print("✅ Conexión exitosa!")
                
                # Probar consulta básica
                result = await db_manager.execute_query("SELECT COUNT(*) as total FROM streams")
                if result:
                    print(f"📊 Total de streams: {result[0]['total']}")
                
                # Probar consulta de categorías
                result = await db_manager.execute_query("SELECT COUNT(*) as total FROM streams_categories")
                if result:
                    print(f"📂 Total de categorías: {result[0]['total']}")
                
                # Probar consulta de series
                result = await db_manager.execute_query("SELECT COUNT(*) as total FROM streams_series")
                if result:
                    print(f"📺 Total de series: {result[0]['total']}")
                
                return True
                
            except Exception as e:
                print(f"❌ Error: {str(e)}")
                return False
        
        return asyncio.run(test())
        
    except Exception as e:
        print(f"❌ Error al cargar configuración: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 Test de Conexión a Base de Datos XUI One")
    print("=" * 50)
    
    success = test_connection()
    
    if success:
        print("\n🎉 ¡Conexión exitosa! Ya puedes ejecutar la aplicación.")
    else:
        print("\n⚠️  Hay problemas con la conexión.")
        print("💡 Ejecuta 'python setup_database.py' para configurar las credenciales.")
    
    sys.exit(0 if success else 1)
