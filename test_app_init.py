"""
Script para probar la aplicación principal y ver si aparecen las estadísticas
"""

import asyncio
import sys
import os

# Agregar el directorio raíz al path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from main import IPTVManager

async def test_app():
    """Probar la inicialización de la aplicación"""
    try:
        app = IPTVManager()
        
        # Inicializar la aplicación
        success = await app.initialize()
        
        if success:
            print("✓ Aplicación inicializada correctamente")
            
            # Probar algunas consultas
            if app.db_manager:
                stats = await app.db_manager.get_content_stats()
                print(f"✓ Estadísticas obtenidas: {stats}")
                
                # Probar obtener películas
                movies = await app.db_manager.get_movies(limit=5)
                print(f"✓ Películas obtenidas: {len(movies)}")
            else:
                print("✗ DatabaseManager no inicializado")
                
        else:
            print("✗ Error al inicializar la aplicación")
            
    except Exception as e:
        print(f"✗ Error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_app())
