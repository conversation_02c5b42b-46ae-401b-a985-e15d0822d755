<?php
/**
 * Simple Database Connection using MySQLi
 * =======================================
 * 
 * Alternative to PDO for when PDO MySQL is not available
 */

class SimpleDatabaseConnection {
    private $connection;
    private $host;
    private $username;
    private $password;
    private $database;
    private $port;
    
    public function __construct() {
        // Load environment variables
        require_once __DIR__ . '/../config/env.php';
        
        $this->host = env('DB_HOST', '**************');
        $this->port = env('DB_PORT', 3306);
        $this->database = env('DB_NAME', 'xui');
        $this->username = env('DB_USER', 'infest84');
        $this->password = env('DB_PASSWORD', 'GZM6hh12zHKgfg34FghtAQ84WxloPRX]SV$fP');
        
        $this->connect();
    }
    
    private function connect() {
        // Check if MySQLi is available
        if (!extension_loaded('mysqli')) {
            throw new Exception("Neither PDO MySQL nor MySQLi extension is available");
        }
        
        // Create connection
        $this->connection = new mysqli($this->host, $this->username, $this->password, $this->database, $this->port);
        
        // Check connection
        if ($this->connection->connect_error) {
            throw new Exception("Connection failed: " . $this->connection->connect_error);
        }
        
        // Set charset
        $this->connection->set_charset("utf8mb4");
        
        // Set timeout
        $this->connection->options(MYSQLI_OPT_CONNECT_TIMEOUT, 10);
    }
    
    public function query($sql, $params = []) {
        if (empty($params)) {
            $result = $this->connection->query($sql);
            if (!$result) {
                throw new Exception("Query failed: " . $this->connection->error);
            }
            
            if ($result === true) {
                return [];
            }
            
            $data = [];
            while ($row = $result->fetch_assoc()) {
                $data[] = $row;
            }
            $result->free();
            return $data;
        } else {
            // Prepare statement
            $stmt = $this->connection->prepare($sql);
            if (!$stmt) {
                throw new Exception("Prepare failed: " . $this->connection->error);
            }
            
            // Bind parameters
            if (!empty($params)) {
                $types = str_repeat('s', count($params)); // Assume all strings for simplicity
                $stmt->bind_param($types, ...$params);
            }
            
            // Execute
            if (!$stmt->execute()) {
                throw new Exception("Execute failed: " . $stmt->error);
            }
            
            $result = $stmt->get_result();
            if (!$result) {
                $stmt->close();
                return [];
            }
            
            $data = [];
            while ($row = $result->fetch_assoc()) {
                $data[] = $row;
            }
            
            $stmt->close();
            return $data;
        }
    }
    
    public function execute($sql, $params = []) {
        if (empty($params)) {
            $result = $this->connection->query($sql);
            if (!$result) {
                throw new Exception("Execute failed: " . $this->connection->error);
            }
            return $this->connection->affected_rows;
        } else {
            $stmt = $this->connection->prepare($sql);
            if (!$stmt) {
                throw new Exception("Prepare failed: " . $this->connection->error);
            }
            
            if (!empty($params)) {
                $types = str_repeat('s', count($params));
                $stmt->bind_param($types, ...$params);
            }
            
            if (!$stmt->execute()) {
                throw new Exception("Execute failed: " . $stmt->error);
            }
            
            $affected = $stmt->affected_rows;
            $stmt->close();
            return $affected;
        }
    }
    
    public function testConnection() {
        return $this->connection && $this->connection->ping();
    }
    
    public function getError() {
        return $this->connection ? $this->connection->error : "No connection";
    }
    
    public function close() {
        if ($this->connection) {
            $this->connection->close();
        }
    }
    
    public function __destruct() {
        $this->close();
    }
}
?>
