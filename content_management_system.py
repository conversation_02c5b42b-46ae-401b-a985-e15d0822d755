"""
Sistema de Gestión de Contenido
===============================

Sistema que implementa las reglas específicas de gestión:
- Symlink vive (con prioridad de borrado por calidad)
- Direct_source se purga (mantener solo uno por título)
- Protección de versiones HD/FHD/4K/HDR/60fps
- Borrado manual seleccionable
"""

import asyncio
import sys
import os

# Agregar el directorio raíz al path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.settings import Settings
from core.database_manager import DatabaseManager

async def analyze_content_management():
    """Analizar contenido según reglas de gestión específicas"""
    print("🎯 SISTEMA DE GESTIÓN DE CONTENIDO")
    print("=" * 60)
    print("📋 REGLAS:")
    print("   🔗 SYMLINK = VIVE (prioridad borrado por calidad)")
    print("   📁 DIRECT_SOURCE = PURGA (mantener solo uno)")
    print("   🛡️ PROTECCIÓN: Mantener HD/FHD/4K/HDR/60fps")
    print("   ⚠️ BORRADO: Manual seleccionable")
    print("=" * 60)
    
    try:
        # Inicializar componentes
        settings = Settings()
        db_manager = DatabaseManager(settings)
        
        # Probar conexión
        print("1. Probando conexión...")
        if not await db_manager.test_connection():
            print("❌ Error de conexión")
            return
        print("✅ Conexión exitosa\n")
        
        # ========================================
        # ANÁLISIS 1: SYMLINK - CANDIDATOS A BORRADO POR CALIDAD
        # ========================================
        print("🔗 ANÁLISIS 1: SYMLINK - CANDIDATOS A BORRADO POR CALIDAD")
        print("-" * 60)
        
        query_symlink_low_priority = """
        SELECT 
            s1.id,
            s1.stream_display_name,
            s1.tmdb_id,
            s1.year,
            s1.rating,
            CASE 
                WHEN s1.stream_display_name LIKE '%4K%' OR s1.stream_display_name LIKE '%2160p%' THEN '4K'
                WHEN s1.stream_display_name LIKE '%HDR%' THEN 'HDR'
                WHEN s1.stream_display_name LIKE '%60fps%' OR s1.stream_display_name LIKE '%60FPS%' THEN '60fps'
                WHEN s1.stream_display_name LIKE '%1080p%' OR s1.stream_display_name LIKE '%FHD%' THEN '1080p'
                WHEN s1.stream_display_name LIKE '%720p%' OR s1.stream_display_name LIKE '%HD%' THEN '720p'
                WHEN s1.stream_display_name LIKE '%480p%' THEN '480p'
                WHEN s1.stream_display_name LIKE '%CAM%' OR s1.stream_display_name LIKE '%TS%' THEN 'CAM/TS'
                ELSE 'Unknown'
            END as quality,
            (SELECT COUNT(*) FROM streams s2 
             WHERE s2.stream_display_name = s1.stream_display_name 
             AND s2.movie_symlink = 1 
             AND s2.type = 2) as symlink_count,
            (SELECT COUNT(*) FROM streams s3 
             WHERE s3.stream_display_name = s1.stream_display_name 
             AND s3.type = 2
             AND (s3.stream_display_name LIKE '%4K%' OR s3.stream_display_name LIKE '%2160p%' OR 
                  s3.stream_display_name LIKE '%HDR%' OR s3.stream_display_name LIKE '%60fps%' OR
                  s3.stream_display_name LIKE '%1080p%' OR s3.stream_display_name LIKE '%FHD%')) as has_hq_version
        FROM streams s1
        WHERE s1.type = 2 
        AND s1.movie_symlink = 1
        AND (
            s1.stream_display_name LIKE '%CAM%' OR 
            s1.stream_display_name LIKE '%TS%' OR
            s1.stream_display_name LIKE '%480p%' OR
            s1.stream_display_name LIKE '%SCREENER%'
        )
        ORDER BY s1.stream_display_name, quality
        LIMIT 20
        """
        
        result_symlink_candidates = await db_manager.execute_query(query_symlink_low_priority)
        print(f"   🔗 SYMLINK - Candidatos a borrado por baja calidad: {len(result_symlink_candidates)}")
        
        for i, row in enumerate(result_symlink_candidates, 1):
            has_hq = "✅ SÍ" if row['has_hq_version'] > 0 else "❌ NO"
            print(f"   {i:2d}. {row['stream_display_name'][:45]}")
            print(f"       ID: {row['id']} | Calidad: {row['quality']} | Copias symlink: {row['symlink_count']}")
            print(f"       Tiene versión HD+: {has_hq} | TMDB: {row['tmdb_id']} | Año: {row['year']}")
            print()
        
        # ========================================
        # ANÁLISIS 2: DIRECT_SOURCE - CANDIDATOS A PURGA
        # ========================================
        print("📁 ANÁLISIS 2: DIRECT_SOURCE - CANDIDATOS A PURGA")
        print("-" * 60)
        
        query_direct_purge = """
        SELECT 
            stream_display_name,
            COUNT(*) as direct_count,
            GROUP_CONCAT(id ORDER BY 
                CASE 
                    WHEN stream_display_name LIKE '%4K%' OR stream_display_name LIKE '%2160p%' THEN 1
                    WHEN stream_display_name LIKE '%HDR%' THEN 2
                    WHEN stream_display_name LIKE '%60fps%' OR stream_display_name LIKE '%60FPS%' THEN 3
                    WHEN stream_display_name LIKE '%1080p%' OR stream_display_name LIKE '%FHD%' THEN 4
                    WHEN stream_display_name LIKE '%720p%' OR stream_display_name LIKE '%HD%' THEN 5
                    ELSE 6
                END, added DESC
            ) as ids_by_quality,
            GROUP_CONCAT(DISTINCT CASE 
                WHEN stream_display_name LIKE '%4K%' OR stream_display_name LIKE '%2160p%' THEN '4K'
                WHEN stream_display_name LIKE '%HDR%' THEN 'HDR'
                WHEN stream_display_name LIKE '%60fps%' OR stream_display_name LIKE '%60FPS%' THEN '60fps'
                WHEN stream_display_name LIKE '%1080p%' OR stream_display_name LIKE '%FHD%' THEN '1080p'
                WHEN stream_display_name LIKE '%720p%' OR stream_display_name LIKE '%HD%' THEN '720p'
                ELSE 'Other'
            END) as qualities,
            (SELECT COUNT(*) FROM streams s2 
             WHERE s2.stream_display_name = s1.stream_display_name 
             AND s2.movie_symlink = 1 
             AND s2.type = 2) as symlink_versions
        FROM streams s1
        WHERE s1.type = 2 
        AND s1.direct_source = 1
        GROUP BY s1.stream_display_name
        HAVING COUNT(*) > 1
        ORDER BY COUNT(*) DESC, symlink_versions ASC
        LIMIT 15
        """
        
        result_direct_purge = await db_manager.execute_query(query_direct_purge)
        print(f"   📁 DIRECT_SOURCE - Títulos con múltiples copias para purga: {len(result_direct_purge)}")
        
        for i, row in enumerate(result_direct_purge, 1):
            ids = row['ids_by_quality'].split(',')
            keep_id = ids[0]  # El primero es el de mejor calidad
            delete_ids = ids[1:]  # El resto son candidatos a borrado
            
            symlink_protection = "🛡️ PROTEGIDO" if row['symlink_versions'] > 0 else "⚠️ SIN PROTECCIÓN"
            
            print(f"   {i:2d}. {row['stream_display_name'][:45]}")
            print(f"       Copias direct: {row['direct_count']} | Calidades: {row['qualities']}")
            print(f"       {symlink_protection} (symlink: {row['symlink_versions']})")
            print(f"       🟢 MANTENER: ID {keep_id}")
            print(f"       🔴 BORRAR: IDs {', '.join(delete_ids)}")
            print()
        
        # ========================================
        # ANÁLISIS 3: PROTECCIÓN - TÍTULOS SIN VERSIÓN HD+
        # ========================================
        print("🛡️ ANÁLISIS 3: PROTECCIÓN - TÍTULOS SIN VERSIÓN HD+")
        print("-" * 60)
        
        query_protection_needed = """
        SELECT 
            stream_display_name,
            COUNT(*) as total_versions,
            SUM(CASE WHEN movie_symlink = 1 THEN 1 ELSE 0 END) as symlink_versions,
            SUM(CASE WHEN direct_source = 1 THEN 1 ELSE 0 END) as direct_versions,
            MAX(CASE 
                WHEN stream_display_name LIKE '%4K%' OR stream_display_name LIKE '%2160p%' THEN 4
                WHEN stream_display_name LIKE '%HDR%' THEN 4
                WHEN stream_display_name LIKE '%60fps%' OR stream_display_name LIKE '%60FPS%' THEN 4
                WHEN stream_display_name LIKE '%1080p%' OR stream_display_name LIKE '%FHD%' THEN 3
                WHEN stream_display_name LIKE '%720p%' OR stream_display_name LIKE '%HD%' THEN 2
                ELSE 1
            END) as max_quality_level,
            GROUP_CONCAT(id) as all_ids
        FROM streams 
        WHERE type = 2
        GROUP BY stream_display_name
        HAVING MAX(CASE 
            WHEN stream_display_name LIKE '%4K%' OR stream_display_name LIKE '%2160p%' THEN 4
            WHEN stream_display_name LIKE '%HDR%' THEN 4
            WHEN stream_display_name LIKE '%60fps%' OR stream_display_name LIKE '%60FPS%' THEN 4
            WHEN stream_display_name LIKE '%1080p%' OR stream_display_name LIKE '%FHD%' THEN 3
            WHEN stream_display_name LIKE '%720p%' OR stream_display_name LIKE '%HD%' THEN 2
            ELSE 1
        END) < 3
        AND COUNT(*) > 1
        ORDER BY max_quality_level ASC, total_versions DESC
        LIMIT 10
        """
        
        result_protection = await db_manager.execute_query(query_protection_needed)
        print(f"   🛡️ Títulos que necesitan protección (sin HD+): {len(result_protection)}")
        
        for i, row in enumerate(result_protection, 1):
            quality_level = {1: "Baja", 2: "720p", 3: "1080p", 4: "4K/HDR/60fps"}.get(row['max_quality_level'], "Unknown")
            
            print(f"   {i:2d}. {row['stream_display_name'][:45]}")
            print(f"       Versiones: {row['total_versions']} | Symlink: {row['symlink_versions']} | Direct: {row['direct_versions']}")
            print(f"       Calidad máxima: {quality_level} | ⚠️ REQUIERE PROTECCIÓN")
            print()
        
        # ========================================
        # ANÁLISIS 4: RESUMEN DE ACCIONES RECOMENDADAS
        # ========================================
        print("📊 ANÁLISIS 4: RESUMEN DE ACCIONES RECOMENDADAS")
        print("-" * 60)
        
        # Contar totales para resumen
        query_summary = """
        SELECT 
            'Symlink Total' as categoria,
            COUNT(*) as cantidad
        FROM streams WHERE type = 2 AND movie_symlink = 1
        UNION ALL
        SELECT 
            'Direct_source Total' as categoria,
            COUNT(*) as cantidad
        FROM streams WHERE type = 2 AND direct_source = 1
        UNION ALL
        SELECT 
            'Symlink Baja Calidad' as categoria,
            COUNT(*) as cantidad
        FROM streams 
        WHERE type = 2 AND movie_symlink = 1
        AND (stream_display_name LIKE '%CAM%' OR stream_display_name LIKE '%TS%' OR 
             stream_display_name LIKE '%480p%' OR stream_display_name LIKE '%SCREENER%')
        UNION ALL
        SELECT 
            'Direct_source Duplicados' as categoria,
            COUNT(DISTINCT stream_display_name) as cantidad
        FROM streams 
        WHERE type = 2 AND direct_source = 1
        AND stream_display_name IN (
            SELECT stream_display_name 
            FROM streams 
            WHERE type = 2 AND direct_source = 1 
            GROUP BY stream_display_name 
            HAVING COUNT(*) > 1
        )
        """
        
        result_summary = await db_manager.execute_query(query_summary)
        
        print("   📊 RESUMEN EJECUTIVO:")
        for row in result_summary:
            print(f"   {row['categoria']}: {row['cantidad']:,}")
        
        print("\n   💡 ACCIONES RECOMENDADAS:")
        print("   🔗 SYMLINK:")
        print("      - Revisar candidatos de baja calidad para borrado manual")
        print("      - Mantener todas las versiones HD+ de symlink")
        print("      - Priorizar 4K, HDR y 60fps")
        
        print("   📁 DIRECT_SOURCE:")
        print("      - Purgar duplicados manteniendo solo la mejor calidad")
        print("      - Proteger títulos sin versión symlink HD+")
        print("      - Eliminar versiones redundantes")
        
        print("   🛡️ PROTECCIÓN:")
        print("      - Nunca borrar el último ejemplar de un título")
        print("      - Mantener al menos una versión HD/FHD/4K/HDR/60fps")
        print("      - Verificar antes de cada borrado")
        
        print("   ⚠️ SEGURIDAD:")
        print("      - TODO BORRADO DEBE SER MANUAL Y SELECCIONABLE")
        print("      - Generar lista de candidatos, no borrar automáticamente")
        print("      - Confirmar cada acción de borrado")
        
        print("\n✅ Análisis de gestión completado")
        
    except Exception as e:
        print(f"\n❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        if 'db_manager' in locals():
            await db_manager.close()
    
    return True

if __name__ == "__main__":
    success = asyncio.run(analyze_content_management())
    if success:
        print("\n🚀 Sistema de gestión listo")
        print("   - Identificados candidatos de borrado por calidad")
        print("   - Detectados duplicados direct_source para purga")
        print("   - Establecidas reglas de protección")
        print("   - Preparado para borrado manual seleccionable")
    else:
        print("\n💥 Error en el análisis")
        sys.exit(1)
