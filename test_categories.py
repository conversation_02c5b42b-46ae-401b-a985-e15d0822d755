"""
Script para probar las categorías de XUI One
"""

import asyncio
import sys
import os

# Agregar el directorio raíz al path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.settings import Settings
from core.database_manager import DatabaseManager

async def test_categories():
    """Probar las categorías de XUI One"""
    
    # Inicializar configuración
    settings = Settings()
    
    # Inicializar database manager
    db_manager = DatabaseManager(settings)
    
    try:
        # Probar conexión
        if not await db_manager.test_connection():
            print("✗ Error de conexión")
            return
            
        print("✓ Base de datos conectada")
        
        # Obtener todas las categorías
        print("\n1. Obteniendo todas las categorías...")
        categories = await db_manager.get_categories()
        print(f"Total categorías: {len(categories)}")
        
        # Agrupar por tipo
        category_types = {}
        for cat in categories:
            cat_type = cat.get('category_type', 'unknown')
            if cat_type not in category_types:
                category_types[cat_type] = []
            category_types[cat_type].append(cat)
        
        print(f"Tipos de categorías encontrados: {list(category_types.keys())}")
        
        # Mostrar estadísticas por tipo
        for cat_type, cats in category_types.items():
            print(f"\n  {cat_type}: {len(cats)} categorías")
            print(f"    Primeras 3 categorías:")
            for cat in cats[:3]:
                adult_flag = " (ADULTO)" if cat.get('is_adult', 0) == 1 else ""
                print(f"      - ID {cat.get('id')}: {cat.get('category_name')}{adult_flag}")
        
        # Probar obtener categorías por tipo específico
        print("\n2. Obteniendo categorías de películas...")
        movie_categories = await db_manager.get_categories_by_type('movie')
        print(f"Categorías de películas: {len(movie_categories)}")
        for cat in movie_categories[:5]:
            print(f"  - ID {cat.get('id')}: {cat.get('category_name')} (orden: {cat.get('cat_order')})")
        
        print("\n3. Obteniendo categorías de series...")
        series_categories = await db_manager.get_categories_by_type('series')
        print(f"Categorías de series: {len(series_categories)}")
        for cat in series_categories[:5]:
            print(f"  - ID {cat.get('id')}: {cat.get('category_name')} (orden: {cat.get('cat_order')})")
        
        print("\n4. Obteniendo categorías de live TV...")
        live_categories = await db_manager.get_categories_by_type('live')
        print(f"Categorías de live TV: {len(live_categories)}")
        for cat in live_categories[:5]:
            print(f"  - ID {cat.get('id')}: {cat.get('category_name')} (orden: {cat.get('cat_order')})")
        
        # Buscar categorías para adultos
        print("\n5. Categorías para adultos...")
        adult_categories = [cat for cat in categories if cat.get('is_adult', 0) == 1]
        print(f"Categorías para adultos: {len(adult_categories)}")
        for cat in adult_categories[:3]:
            print(f"  - ID {cat.get('id')}: {cat.get('category_name')} (tipo: {cat.get('category_type')})")
        
        print("\n✓ Prueba de categorías completada")
        
    except Exception as e:
        print(f"✗ Error: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Cerrar conexiones
        await db_manager.close()

if __name__ == "__main__":
    asyncio.run(test_categories())
