<?php
/**
 * Fast Dashboard
 * =============
 * 
 * Optimized dashboard for quick content overview
 */

// Set reasonable timeouts
set_time_limit(30);
ini_set('max_execution_time', 30);
ini_set('memory_limit', '256M');

// Include database manager
require_once __DIR__ . '/../../core/SimpleDatabaseManager.php';

// Initialize
$startTime = microtime(true);
$maxLoadTime = 15; // 15 seconds MAX

try {
    $db = new SimpleDatabaseManager();
    
    // Test connection with timeout
    $connectionStart = microtime(true);
    if (!$db->testConnection()) {
        throw new Exception("DB connection failed");
    }
    $connectionTime = microtime(true) - $connectionStart;
    
    if ($connectionTime > 2) {
        throw new Exception("Connection too slow: {$connectionTime}s");
    }
    
    // Get data with individual timeouts
    $contentStats = $db->getContentStats();
    $qualityStats = $db->getQualityStats();
    $recentContent = $db->getRecentAdditions(3); // Reduced to 3 for speed
    $popularContent = $db->getPopularContent(3); // Reduced to 3 for speed
    
    $totalLoadTime = microtime(true) - $startTime;
    if ($totalLoadTime > $maxLoadTime) {
        throw new Exception("Dashboard load too slow: {$totalLoadTime}s");
    }
    
} catch (Exception $e) {
    $error = $e->getMessage();
    $contentStats = [
        'total_streams' => 0,
        'live_tv' => 0,
        'movies' => 0,
        'series' => 0,
        'symlink_movies' => 0,
        'direct_movies' => 0
    ];
    $qualityStats = [
        'content_4k' => 0,
        'content_60fps' => 0,
        'content_hdr' => 0,
        'content_fhd' => 0,
        'content_hd' => 0
    ];
    $recentContent = [];
    $popularContent = [];
}

$loadTime = microtime(true) - $startTime;
?>

<style>
/* FAST LOADING STYLES */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: linear-gradient(135deg, var(--dark-surface), var(--dark-bg));
    border: 1px solid var(--dark-border);
    border-radius: 1rem;
    padding: 1.5rem;
    position: relative;
    overflow: hidden;
    transition: var(--transition);
    min-height: 140px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
    border-color: var(--primary-color);
}

.stat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.stat-title {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    flex-shrink: 0;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    line-height: 1;
}

.stat-trend {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.quick-actions {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.action-card {
    background: var(--dark-surface);
    border: 1px solid var(--dark-border);
    border-radius: 1rem;
    padding: 1.5rem;
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
    text-decoration: none;
    color: inherit;
    min-height: 120px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.action-card:hover {
    transform: translateY(-2px);
    border-color: var(--primary-color);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
}

.action-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: white;
    margin: 0 auto 0.75rem;
    flex-shrink: 0;
}

.action-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
    font-size: 0.95rem;
}

.action-description {
    font-size: 0.8rem;
    color: var(--text-secondary);
    line-height: 1.3;
}

.performance-info {
    background: var(--dark-surface);
    border: 1px solid var(--dark-border);
    border-radius: 1rem;
    padding: 1rem;
    margin-bottom: 2rem;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.performance-good {
    border-color: var(--success-color);
    background: rgba(16, 185, 129, 0.1);
}

.performance-warning {
    border-color: var(--warning-color);
    background: rgba(245, 158, 11, 0.1);
}

.performance-error {
    border-color: #ef4444;
    background: rgba(239, 68, 68, 0.1);
}

@media (max-width: 1200px) {
    .dashboard-grid,
    .quick-actions {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .dashboard-grid,
    .quick-actions {
        grid-template-columns: 1fr;
    }
}
</style>

<!-- Performance Monitor -->
<div class="performance-info <?= $loadTime < 2 ? 'performance-good' : ($loadTime < 5 ? 'performance-warning' : 'performance-error') ?>">
    <strong>⚡ Performance:</strong> 
    Dashboard loaded in <?= number_format($loadTime, 3) ?>s
    <?php if (isset($error)): ?>
    | <strong>⚠️ Error:</strong> <?= htmlspecialchars($error) ?>
    <?php endif; ?>
    | <strong>🔄 Auto-refresh:</strong> <span id="refresh-countdown">30</span>s
</div>

<?php if (isset($error)): ?>
<div style="background: rgba(239, 68, 68, 0.1); border: 1px solid #ef4444; border-radius: 1rem; padding: 2rem; margin-bottom: 2rem; text-align: center;">
    <h3 style="color: #ef4444; margin-top: 0;">⚠️ Database Connection Issue</h3>
    <p><strong>Error:</strong> <?= htmlspecialchars($error) ?></p>
    <p>Load time: <?= number_format($loadTime, 3) ?>s</p>
    <br><small>The system is using fallback data to prevent long threads</small>
</div>
<?php endif; ?>

<!-- Quick Actions -->
<div class="quick-actions">
    <a href="?page=m3u-manager" class="action-card">
        <div class="action-icon">
            <i class="fas fa-upload"></i>
        </div>
        <div class="action-title">Upload M3U</div>
        <div class="action-description">Import new content from M3U files</div>
    </a>
    
    <a href="?page=content-browser" class="action-card">
        <div class="action-icon">
            <i class="fas fa-search"></i>
        </div>
        <div class="action-title">Browse Content</div>
        <div class="action-description">Explore and manage your content</div>
    </a>
    
    <a href="?page=analytics" class="action-card">
        <div class="action-icon">
            <i class="fas fa-chart-bar"></i>
        </div>
        <div class="action-title">View Analytics</div>
        <div class="action-description">Detailed content statistics</div>
    </a>
    
    <a href="#" class="action-card" onclick="refreshDashboard()">
        <div class="action-icon">
            <i class="fas fa-sync-alt"></i>
        </div>
        <div class="action-title">Force Refresh</div>
        <div class="action-description">Reload dashboard data</div>
    </a>
</div>

<!-- Statistics Cards -->
<div class="dashboard-grid">
    <div class="stat-card">
        <div class="stat-header">
            <div class="stat-title">Total Streams</div>
            <div class="stat-icon" style="background: rgba(59, 130, 246, 0.1); color: var(--primary-color);">
                <i class="fas fa-film"></i>
            </div>
        </div>
        <div class="stat-value"><?= number_format($contentStats['total_streams']) ?></div>
        <div class="stat-trend">
            <i class="fas fa-database"></i>
            <span>All content types</span>
        </div>
    </div>
    
    <div class="stat-card">
        <div class="stat-header">
            <div class="stat-title">Movies</div>
            <div class="stat-icon" style="background: rgba(16, 185, 129, 0.1); color: var(--success-color);">
                <i class="fas fa-video"></i>
            </div>
        </div>
        <div class="stat-value"><?= number_format($contentStats['movies']) ?></div>
        <div class="stat-trend">
            <i class="fas fa-link"></i>
            <span><?= number_format($contentStats['symlink_movies']) ?> symlink</span>
        </div>
    </div>
    
    <div class="stat-card">
        <div class="stat-header">
            <div class="stat-title">4K Content</div>
            <div class="stat-icon" style="background: rgba(245, 158, 11, 0.1); color: var(--warning-color);">
                <i class="fas fa-crown"></i>
            </div>
        </div>
        <div class="stat-value"><?= number_format($qualityStats['content_4k']) ?></div>
        <div class="stat-trend">
            <i class="fas fa-shield-alt"></i>
            <span>Ultra HD content</span>
        </div>
    </div>
    
    <div class="stat-card">
        <div class="stat-header">
            <div class="stat-title">60fps Content</div>
            <div class="stat-icon" style="background: rgba(139, 92, 246, 0.1); color: #8b5cf6;">
                <i class="fas fa-tachometer-alt"></i>
            </div>
        </div>
        <div class="stat-value"><?= number_format($qualityStats['content_60fps']) ?></div>
        <div class="stat-trend">
            <i class="fas fa-bolt"></i>
            <span>High framerate</span>
        </div>
    </div>
</div>

<script>
// Optimized JavaScript
let refreshTimer = 30;
let refreshInterval;

// Auto-refresh to prevent stale connections
function startRefreshCountdown() {
    refreshInterval = setInterval(() => {
        refreshTimer--;
        document.getElementById('refresh-countdown').textContent = refreshTimer;
        
        if (refreshTimer <= 0) {
            refreshDashboard();
        }
    }, 1000);
}

// Force refresh dashboard
function refreshDashboard() {
    clearInterval(refreshInterval);
    window.location.reload();
}

// Timeout protection for page load
setTimeout(() => {
    if (document.readyState !== 'complete') {
        console.warn('Page load timeout - forcing refresh');
        window.location.reload();
    }
}, 10000); // 10 second timeout

// Start countdown when page loads
document.addEventListener('DOMContentLoaded', () => {
    startRefreshCountdown();
});

// Kill any hanging requests on page unload
window.addEventListener('beforeunload', () => {
    clearInterval(refreshInterval);
});
</script>
