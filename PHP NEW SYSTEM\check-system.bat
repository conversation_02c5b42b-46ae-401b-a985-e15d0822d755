@echo off
cls
echo.
echo ==========================================
echo   IPTV XUI One - Verificación de Sistema
echo ==========================================
echo.

cd /d "%~dp0"

echo 🔧 Verificando requisitos del sistema...
echo.

REM Verificar PHP
echo 📋 Verificando PHP...
php -v >nul 2>&1
if errorlevel 1 (
    echo ❌ PHP no encontrado
    echo 💡 Necesitas instalar PHP. Ver INSTALACION.md
    echo.
) else (
    echo ✅ PHP encontrado
    php -v | findstr "PHP"
    echo.
)

REM Verificar archivos de configuración
echo 📁 Verificando archivos de configuración...
if exist ".env" (
    echo ✅ .env encontrado
) else (
    echo ❌ .env no encontrado
    echo 💡 Copia .env.example a .env y configúralo
)

if exist "config\database.php" (
    echo ✅ config\database.php encontrado
) else (
    echo ❌ config\database.php no encontrado
)

if exist "config\env.php" (
    echo ✅ config\env.php encontrado
) else (
    echo ❌ config\env.php no encontrado
)
echo.

REM Verificar directorios
echo 📂 Verificando directorios...
if exist "logs" (
    echo ✅ logs\ existe
) else (
    echo ⚠️  logs\ no existe, creando...
    mkdir logs
    echo ✅ logs\ creado
)

if exist "uploads" (
    echo ✅ uploads\ existe
) else (
    echo ⚠️  uploads\ no existe, creando...
    mkdir uploads
    echo ✅ uploads\ creado
)

if exist "public" (
    echo ✅ public\ existe
) else (
    echo ❌ public\ no existe
)

if exist "api" (
    echo ✅ api\ existe
) else (
    echo ❌ api\ no existe
)
echo.

REM Verificar archivos principales
echo 📄 Verificando archivos principales...
if exist "start.php" (
    echo ✅ start.php existe
) else (
    echo ❌ start.php no existe
)

if exist "system-test.php" (
    echo ✅ system-test.php existe
) else (
    echo ❌ system-test.php no existe
)

if exist "public\index.php" (
    echo ✅ public\index.php existe
) else (
    echo ❌ public\index.php no existe
)
echo.

echo 🌐 URLs que deberían funcionar cuando inicies el servidor:
echo    🏠 http://localhost:8000/start.php
echo    🔬 http://localhost:8000/system-test.php
echo    📊 http://localhost:8000/public/
echo.

echo 📋 Para iniciar el servidor:
echo    1. Ejecuta: start-server.bat
echo    2. O manualmente: php -S localhost:8000
echo.

echo 📚 Documentación:
echo    📖 INSTALACION.md - Guía de instalación completa
echo    📖 GUIA_INICIO.md - Guía de uso
echo    📖 README_PHP.md - Documentación técnica
echo.

pause
