"""
Diálogo de carga
===============

Ventana modal para mostrar progreso de operaciones largas.
"""

import tkinter as tk
import customtkinter as ctk
import threading
import time
from typing import Optional, Callable

class LoadingDialog:
    """Diálogo de carga con animación"""
    
    def __init__(self, parent, title: str = "Cargando..."):
        self.parent = parent
        self.title = title
        self.is_open = False
        self.dialog = None
        
        # Crear ventana
        self._create_dialog()
    
    def _create_dialog(self):
        """Crear ventana de diálogo"""
        self.dialog = ctk.CTkToplevel(self.parent)
        self.dialog.title(self.title)
        self.dialog.geometry("400x200")
        self.dialog.resizable(False, False)
        
        # Centrar ventana
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # Centrar en pantalla
        x = (self.dialog.winfo_screenwidth() // 2) - 200
        y = (self.dialog.winfo_screenheight() // 2) - 100
        self.dialog.geometry(f"400x200+{x}+{y}")
        
        # Contenido
        self._setup_content()
        
        self.is_open = True
    
    def _setup_content(self):
        """Configurar contenido del diálogo"""
        # Frame principal
        main_frame = ctk.CTkFrame(self.dialog, corner_radius=0)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Título
        title_label = ctk.CTkLabel(
            main_frame,
            text=self.title,
            font=("Arial", 16, "bold")
        )
        title_label.pack(pady=(20, 10))
        
        # Spinner animado
        self.spinner_frame = ctk.CTkFrame(main_frame, width=60, height=60)
        self.spinner_frame.pack(pady=10)
        self.spinner_frame.pack_propagate(False)
        
        # Mensaje
        self.message_label = ctk.CTkLabel(
            main_frame,
            text="Por favor espere...",
            font=("Arial", 12)
        )
        self.message_label.pack(pady=(10, 20))
        
        # Barra de progreso
        self.progress_bar = ctk.CTkProgressBar(
            main_frame,
            width=300,
            height=20
        )
        self.progress_bar.pack(pady=10)
        self.progress_bar.set(0.0)
        
        # Botón cancelar (opcional)
        self.cancel_button = ctk.CTkButton(
            main_frame,
            text="Cancelar",
            width=100,
            command=self.close
        )
        self.cancel_button.pack(pady=10)
        
        # Iniciar animación
        self._start_spinner()
    
    def _start_spinner(self):
        """Iniciar animación de spinner"""
        def animate():
            chars = "⠋⠙⠹⠸⠼⠴⠦⠧⠇⠏"
            i = 0
            while self.is_open:
                if self.dialog and self.dialog.winfo_exists():
                    spinner_label = ctk.CTkLabel(
                        self.spinner_frame,
                        text=chars[i % len(chars)],
                        font=("Arial", 24)
                    )
                    spinner_label.pack(expand=True)
                    
                    # Limpiar después de un frame
                    self.dialog.after(100, lambda: spinner_label.destroy() if spinner_label.winfo_exists() else None)
                    
                    i += 1
                    time.sleep(0.1)
                else:
                    break
        
        threading.Thread(target=animate, daemon=True).start()
    
    def update_progress(self, value: float, message: str = ""):
        """Actualizar progreso"""
        if self.dialog and self.dialog.winfo_exists():
            self.progress_bar.set(value)
            if message:
                self.message_label.configure(text=message)
    
    def update_message(self, message: str):
        """Actualizar mensaje"""
        if self.dialog and self.dialog.winfo_exists():
            self.message_label.configure(text=message)
    
    def close(self):
        """Cerrar diálogo"""
        self.is_open = False
        if self.dialog and self.dialog.winfo_exists():
            self.dialog.destroy()
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()
