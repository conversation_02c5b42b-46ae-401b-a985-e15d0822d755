"""
Ventana principal de la aplicación
=================================

Interfaz gráfica moderna usando CustomTkinter con navegación,
paneles de contenido y componentes interactivos.
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import customtkinter as ctk
import asyncio
import logging
from typing import Dict, List, Optional, Callable, Any
from pathlib import Path
import threading
from PIL import Image, ImageTk

from ui.components.sidebar import Sidebar
from ui.components.dashboard import Dashboard
from ui.components.content_browser import ContentBrowser
from ui.components.m3u_manager import M3UManager
from ui.components.m3u_tmdb_manager import M3UTMDBManager
from ui.components.tmdb_browser import TMDBBrowser
from ui.components.analytics_panel import AnalyticsPanel
from ui.components.settings_panel import SettingsPanel
from ui.components.loading_dialog import LoadingDialog
from ui.components.progress_bar import AnimatedProgressBar

# Configurar CustomTkinter
ctk.set_appearance_mode("dark")  # "system", "dark", "light"
ctk.set_default_color_theme("blue")  # "blue", "green", "dark-blue"

class MainWindow:
    """Ventana principal de la aplicación"""
    
    def __init__(self, root: tk.Tk, settings, db_manager):
        self.root = root
        self.settings = settings
        self.db_manager = db_manager
        self.logger = logging.getLogger("iptv_manager.main_window")
        
        # Estado de la aplicación
        self.current_panel = "dashboard"
        self.session_data = {}
        self.is_fullscreen = False
        
        # Componentes
        self.sidebar = None
        self.content_frame = None
        self.status_bar = None
        self.current_content = None
        
        # Paneles
        self.dashboard = None
        self.m3u_manager = None
        self.tmdb_browser = None
        self.analytics_panel = None
        self.settings_panel = None
        
        # Configurar ventana
        self._setup_window()
        self._setup_layout()
        self._setup_components()
        self._setup_bindings()
        
        # Cargar sesión
        asyncio.create_task(self._load_session())
        
        self.logger.info("Ventana principal inicializada")
    
    def _setup_window(self):
        """Configurar ventana principal"""
        self.root.title(f"{self.settings.app_name} v{self.settings.app_version}")
        self.root.geometry(f"{self.settings.window_width}x{self.settings.window_height}")
        self.root.minsize(800, 600)
        
        # Centrar ventana
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (self.settings.window_width // 2)
        y = (self.root.winfo_screenheight() // 2) - (self.settings.window_height // 2)
        self.root.geometry(f"{self.settings.window_width}x{self.settings.window_height}+{x}+{y}")
        
        # Configurar colores
        self.root.configure(bg=self.settings.colors["bg_primary"])
        
        # Icono de la aplicación (si existe)
        icon_path = self.settings.assets_dir / "icon.ico"
        if icon_path.exists():
            self.root.iconbitmap(str(icon_path))
    
    def _setup_layout(self):
        """Configurar layout principal"""
        # Frame principal
        self.main_frame = ctk.CTkFrame(self.root, corner_radius=0)
        self.main_frame.pack(fill="both", expand=True, padx=0, pady=0)
        
        # Sidebar
        self.sidebar_frame = ctk.CTkFrame(self.main_frame, width=250, corner_radius=0)
        self.sidebar_frame.pack(side="left", fill="y", padx=0, pady=0)
        self.sidebar_frame.pack_propagate(False)
        
        # Contenido principal
        self.content_frame = ctk.CTkFrame(self.main_frame, corner_radius=0)
        self.content_frame.pack(side="right", fill="both", expand=True, padx=0, pady=0)
        
        # Barra de estado
        self.status_frame = ctk.CTkFrame(self.main_frame, height=30, corner_radius=0)
        self.status_frame.pack(side="bottom", fill="x", padx=0, pady=0)
        
        # Configurar grid
        self.content_frame.grid_rowconfigure(0, weight=1)
        self.content_frame.grid_columnconfigure(0, weight=1)
    
    def _setup_components(self):
        """Configurar componentes de la interfaz"""
        # Sidebar
        self.sidebar = Sidebar(
            self.sidebar_frame,
            self.settings,
            self._on_panel_change
        )
        
        # Barra de estado
        self._setup_status_bar()
        
        # Cargar panel inicial
        self._load_panel("dashboard")
    
    def _setup_status_bar(self):
        """Configurar barra de estado"""
        # Label de estado
        self.status_label = ctk.CTkLabel(
            self.status_frame,
            text="Listo",
            font=("Arial", 11)
        )
        self.status_label.pack(side="left", padx=10, pady=5)
        
        # Barra de progreso
        self.progress_bar = AnimatedProgressBar(
            self.status_frame,
            width=200,
            height=20
        )
        self.progress_bar.pack(side="right", padx=10, pady=5)
        
        # Información de conexión
        self.connection_label = ctk.CTkLabel(
            self.status_frame,
            text="● Conectado",
            font=("Arial", 11),
            text_color=self.settings.colors["success"]
        )
        self.connection_label.pack(side="right", padx=10, pady=5)
    
    def _setup_bindings(self):
        """Configurar eventos y atajos de teclado"""
        # Atajos de teclado
        self.root.bind('<Control-q>', lambda e: self.quit_application())
        self.root.bind('<Control-o>', lambda e: self._open_m3u_file())
        self.root.bind('<Control-s>', lambda e: self._save_session())
        self.root.bind('<F11>', lambda e: self._toggle_fullscreen())
        self.root.bind('<Control-r>', lambda e: self._refresh_current_panel())
        
        # Eventos de ventana
        self.root.protocol("WM_DELETE_WINDOW", self.quit_application)
        self.root.bind('<Configure>', self._on_window_resize)
    
    def _on_panel_change(self, panel_name: str):
        """Cambiar panel activo"""
        if panel_name != self.current_panel:
            self.current_panel = panel_name
            self._load_panel(panel_name)
    
    def _load_panel(self, panel_name: str):
        """Cargar panel específico"""
        # Limpiar contenido actual
        if self.current_content:
            try:
                self.current_content.destroy()
            except:
                try:
                    self.current_content.pack_forget()
                except:
                    pass
        
        # Actualizar estado
        self.update_status(f"Cargando {panel_name}...")
        
        try:
            # Cargar panel correspondiente
            if panel_name == "dashboard":
                self.current_content = Dashboard(
                    self.content_frame,
                    self.settings,
                    self.db_manager
                )
            elif panel_name == "content_browser":
                self.current_content = ContentBrowser(
                    self.content_frame,
                    self.settings,
                    self.db_manager
                )
            elif panel_name == "m3u_tmdb_manager":
                self.current_content = M3UTMDBManager(
                    self.content_frame,
                    self.settings,
                    self.db_manager
                )
            elif panel_name == "m3u_manager":
                self.current_content = M3UManager(
                    self.content_frame,
                    self.settings,
                    self.db_manager
                )
            elif panel_name == "tmdb_browser":
                self.current_content = TMDBBrowser(
                    self.content_frame,
                    self.settings,
                    self.db_manager
                )
            elif panel_name == "analytics":
                self.current_content = AnalyticsPanel(
                    self.content_frame,
                    self.settings,
                    self.db_manager
                )
            elif panel_name == "settings":
                self.current_content = SettingsPanel(
                    self.content_frame,
                    self.settings,
                    self._on_settings_change
                )
            else:
                # Panel por defecto
                self.current_content = ctk.CTkLabel(
                    self.content_frame,
                    text=f"Panel '{panel_name}' no implementado",
                    font=("Arial", 16)
                )
                self.current_content.pack(expand=True)
            
            # Actualizar sidebar
            if self.sidebar and hasattr(self.sidebar, 'set_active_panel'):
                self.sidebar.set_active_panel(panel_name)
            
            # Actualizar estado
            self.update_status("Listo")
            
        except Exception as e:
            self.logger.error(f"Error al cargar panel {panel_name}: {str(e)}")
            self.show_error(f"Error al cargar panel: {str(e)}")
    
    def _on_settings_change(self, settings: Dict[str, Any]):
        """Manejar cambios en configuración"""
        try:
            # Actualizar configuración
            for key, value in settings.items():
                if hasattr(self.settings, key):
                    setattr(self.settings, key, value)
            
            # Aplicar cambios
            self._apply_theme_changes()
            
            self.logger.info("Configuración actualizada")
            
        except Exception as e:
            self.logger.error(f"Error al aplicar configuración: {str(e)}")
            self.show_error(f"Error al aplicar configuración: {str(e)}")
    
    def _apply_theme_changes(self):
        """Aplicar cambios de tema"""
        # Actualizar tema de CustomTkinter
        ctk.set_appearance_mode(self.settings.theme)
        
        # Actualizar colores
        self.settings.colors = self.settings._get_theme_colors()
        
        # Recargar panel actual
        self._load_panel(self.current_panel)
    
    def _toggle_fullscreen(self):
        """Alternar modo pantalla completa"""
        self.is_fullscreen = not self.is_fullscreen
        self.root.attributes('-fullscreen', self.is_fullscreen)
        
        if self.is_fullscreen:
            self.root.bind('<Escape>', lambda e: self._toggle_fullscreen())
    
    def _on_window_resize(self, event):
        """Manejar redimensionado de ventana"""
        if event.widget == self.root:
            # Actualizar configuración
            self.settings.window_width = self.root.winfo_width()
            self.settings.window_height = self.root.winfo_height()
    
    def _open_m3u_file(self):
        """Abrir archivo M3U"""
        file_path = filedialog.askopenfilename(
            title="Seleccionar archivo M3U",
            filetypes=[
                ("Archivos M3U", "*.m3u"),
                ("Archivos M3U8", "*.m3u8"),
                ("Todos los archivos", "*.*")
            ]
        )
        
        if file_path:
            # Cambiar a panel M3U y cargar archivo
            self._on_panel_change("m3u_manager")
            # Dar tiempo para que el panel se cargue
            self.root.after(100, lambda: self._load_file_in_current_panel(file_path))
    
    def _load_file_in_current_panel(self, file_path: str):
        """Cargar archivo en el panel actual"""
        try:
            if self.current_content and hasattr(self.current_content, 'load_file'):
                self.current_content.load_file(file_path)
            else:
                self.show_error("El panel actual no soporta carga de archivos")
        except Exception as e:
            self.logger.error(f"Error al cargar archivo: {str(e)}")
            self.show_error(f"Error al cargar archivo: {str(e)}")
    
    def _refresh_current_panel(self):
        """Refrescar panel actual"""
        self._load_panel(self.current_panel)
    
    async def _load_session(self):
        """Cargar sesión guardada"""
        try:
            # Generar ID de sesión único
            import uuid
            session_id = str(uuid.uuid4())
            
            # Intentar cargar sesión existente
            session = await self.db_manager.get_session(session_id)
            
            if session:
                self.session_data = session.get('user_data', {})
                preferences = session.get('preferences', {})
                
                # Aplicar preferencias
                if 'theme' in preferences:
                    self.settings.theme = preferences['theme']
                    self._apply_theme_changes()
                
                self.logger.info("Sesión cargada correctamente")
            else:
                # Crear nueva sesión
                self.session_data = {
                    'session_id': session_id,
                    'created_at': str(asyncio.get_event_loop().time())
                }
                
                await self.db_manager.save_session(
                    session_id,
                    self.session_data,
                    {}
                )
                
                self.logger.info("Nueva sesión creada")
                
        except Exception as e:
            self.logger.error(f"Error al cargar sesión: {str(e)}")
    
    async def _save_session(self):
        """Guardar sesión actual"""
        try:
            if 'session_id' in self.session_data:
                preferences = {
                    'theme': self.settings.theme,
                    'window_width': self.settings.window_width,
                    'window_height': self.settings.window_height,
                    'current_panel': self.current_panel
                }
                
                await self.db_manager.save_session(
                    self.session_data['session_id'],
                    self.session_data,
                    preferences
                )
                
                self.logger.info("Sesión guardada correctamente")
                
        except Exception as e:
            self.logger.error(f"Error al guardar sesión: {str(e)}")
    
    def update_status(self, message: str):
        """Actualizar mensaje de estado"""
        self.status_label.configure(text=message)
        self.root.update_idletasks()
    
    def update_progress(self, value: float, message: str = ""):
        """Actualizar barra de progreso"""
        self.progress_bar.set_progress(value)
        if message:
            self.update_status(message)
    
    def show_error(self, message: str):
        """Mostrar mensaje de error"""
        messagebox.showerror("Error", message)
        self.update_status(f"Error: {message}")
    
    def show_info(self, message: str):
        """Mostrar mensaje informativo"""
        messagebox.showinfo("Información", message)
        self.update_status(message)
    
    def show_warning(self, message: str):
        """Mostrar mensaje de advertencia"""
        messagebox.showwarning("Advertencia", message)
        self.update_status(f"Advertencia: {message}")
    
    def show_loading_dialog(self, title: str = "Cargando...") -> LoadingDialog:
        """Mostrar diálogo de carga"""
        return LoadingDialog(self.root, title)
    
    def quit_application(self):
        """Cerrar aplicación"""
        try:
            # Guardar sesión
            asyncio.create_task(self._save_session())
            
            # Cerrar conexiones
            if self.db_manager:
                asyncio.create_task(self.db_manager.close())
            
            # Cerrar ventana
            self.root.quit()
            self.root.destroy()
            
            self.logger.info("Aplicación cerrada correctamente")
            
        except Exception as e:
            self.logger.error(f"Error al cerrar aplicación: {str(e)}")
            self.root.quit()
            self.root.destroy()
    
    def run(self):
        """Ejecutar aplicación"""
        try:
            self.root.mainloop()
        except Exception as e:
            self.logger.error(f"Error en loop principal: {str(e)}")
            raise
