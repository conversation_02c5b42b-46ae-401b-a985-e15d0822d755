# 🔧 Guía de Instalación - IPTV XUI One PHP

## 📦 Instalación de PHP en Windows

### Opción 1: XAMPP (Recomendado para principiantes)

1. **<PERSON><PERSON>gar XAMPP**
   - Ve a: https://www.apachefriends.org/download.html
   - Descarga la versión más reciente para Windows
   - Ejecuta el instalador

2. **Configurar XAMPP**
   - Instala Apache y MySQL
   - Inicia los servicios desde el panel de control
   - El directorio de trabajo será: `C:\xampp\htdocs`

3. **Copiar el proyecto**
   ```bash
   # Copia toda la carpeta "PHP NEW SYSTEM" a:
   C:\xampp\htdocs\iptv-manager\
   ```

4. **Acceder al sistema**
   - URL: http://localhost/iptv-manager/start.php
   - Test: http://localhost/iptv-manager/system-test.php

### Opción 2: PHP Standalone

1. **<PERSON>cargar PHP**
   - Ve a: https://windows.php.net/download/
   - <PERSON>car<PERSON> "Thread Safe" para tu arquitectura (x64)
   - Extrae en `C:\php`

2. **Configurar PHP**
   ```bash
   # Añade C:\php al PATH del sistema:
   # Panel de Control > Sistema > Variables de entorno
   # Añade C:\php a la variable PATH
   ```

3. **Habilitar extensiones**
   - Copia `php.ini-development` a `php.ini`
   - Descomenta estas líneas:
   ```ini
   extension=pdo_mysql
   extension=mysqli
   extension=curl
   extension=mbstring
   extension=fileinfo
   ```

4. **Verificar instalación**
   ```bash
   php -v
   php -m | findstr pdo_mysql
   ```

### Opción 3: Laravel Valet (Para desarrolladores)

```bash
# Instalar Chocolatey
# Luego instalar PHP
choco install php
choco install composer

# Configurar Valet
composer global require laravel/valet
valet install
```

## 🗄️ Configuración de Base de Datos

### Si usas XAMPP

1. **Acceder a phpMyAdmin**
   - URL: http://localhost/phpmyadmin
   - Usuario: root (sin contraseña por defecto)

2. **Configurar conexión remota** (opcional)
   - Edita `C:\xampp\mysql\bin\my.ini`
   - Comenta la línea: `bind-address = 127.0.0.1`
   - Reinicia MySQL

### Configuración del .env

```env
# Para XAMPP local
DB_HOST=localhost
DB_PORT=3306
DB_NAME=xui
DB_USER=root
DB_PASSWORD=

# Para servidor remoto (actual)
DB_HOST=**************
DB_PORT=3306
DB_NAME=xui
DB_USER=infest84
DB_PASSWORD=GZM6hh12zHKgfg34FghtAQ84WxloPRX]SV$fP
```

## 🚀 Inicio Rápido

### Con XAMPP

1. Copia el proyecto a `C:\xampp\htdocs\iptv-manager\`
2. Inicia Apache desde XAMPP
3. Ve a: http://localhost/iptv-manager/start.php

### Con PHP Standalone

1. Ejecuta: `start-server.bat`
2. O manualmente: `php -S localhost:8000`
3. Ve a: http://localhost:8000/start.php

### En Servidor Web

1. Sube la carpeta al servidor
2. Configura el .env con los datos del servidor
3. Ve a: http://tu-dominio.com/ruta/start.php

## 🔧 Resolución de Problemas

### Error: "could not find driver"

**Causa**: Extensión PDO MySQL no habilitada

**Solución**:
```ini
# En php.ini, descomenta:
extension=pdo_mysql
```

### Error: "Permission denied"

**Causa**: Permisos de archivos

**Solución**:
```bash
# En el directorio del proyecto:
mkdir logs uploads
chmod 755 logs uploads
```

### Error: "Call to undefined function"

**Causa**: Extensiones PHP faltantes

**Solución**:
```ini
# En php.ini, habilita:
extension=curl
extension=mbstring
extension=fileinfo
extension=json
```

### Timeout de conexión

**Causa**: Firewall o configuración de red

**Solución**:
- Verificar que el puerto 3306 esté abierto
- Verificar credenciales de base de datos
- Revisar configuración de firewall

## 📱 Testing en Diferentes Dispositivos

### Desktop
- Chrome/Firefox: http://localhost:8000/start.php
- Edge: http://localhost:8000/start.php

### Mobile (en red local)
1. Encuentra tu IP: `ipconfig`
2. Usa: http://TU-IP:8000/start.php

### Tablet
- Usa la URL con la IP de tu ordenador
- El diseño es completamente responsivo

## 🌐 Despliegue en Producción

### Hosting Compartido

1. **Subir archivos**
   - Sube toda la carpeta via FTP/cPanel
   - Mantén la estructura de directorios

2. **Configurar .env**
   ```env
   SHARED_HOSTING=true
   APP_URL=http://tu-dominio.com/ruta
   APP_DEBUG=false
   ```

3. **Verificar permisos**
   - logs/ debe ser escribible (755)
   - uploads/ debe ser escribible (755)

### VPS/Servidor Dedicado

1. **Configurar Apache/Nginx**
   ```apache
   <VirtualHost *:80>
       DocumentRoot /var/www/html/iptv-manager
       ServerName tu-dominio.com
   </VirtualHost>
   ```

2. **Configurar SSL** (recomendado)
   ```bash
   certbot --apache -d tu-dominio.com
   ```

3. **Configurar permisos**
   ```bash
   chown -R www-data:www-data /var/www/html/iptv-manager
   chmod -R 755 /var/www/html/iptv-manager
   chmod -R 777 /var/www/html/iptv-manager/logs
   chmod -R 777 /var/www/html/iptv-manager/uploads
   ```

## ✅ Verificación de Instalación

### Checklist Post-Instalación

- [ ] PHP 8.0+ instalado
- [ ] Extensiones PDO MySQL habilitadas
- [ ] Base de datos accesible
- [ ] Permisos de escritura en logs/
- [ ] Permisos de escritura en uploads/
- [ ] .env configurado correctamente
- [ ] system-test.php muestra todo en verde
- [ ] start.php carga correctamente

### URLs de Verificación

1. **Test del Sistema**: `/system-test.php`
2. **Panel Principal**: `/start.php`
3. **Dashboard**: `/public/index.php`
4. **API Test**: `/api/export-data.php`

Si todos estos enlaces funcionan, la instalación está completa! 🎉
