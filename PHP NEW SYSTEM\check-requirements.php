<?php
/**
 * System Requirements Checker - IPTV XUI One Content Manager
 * ==========================================================
 * 
 * Checks if all required PHP extensions and configurations are available
 */

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Requirements Check - IPTV XUI Manager</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #0f172a;
            color: #f1f5f9;
            margin: 0;
            padding: 2rem;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: #1e293b;
            border-radius: 1rem;
            padding: 2rem;
            border: 1px solid #334155;
        }
        h1 {
            color: #3b82f6;
            text-align: center;
            margin-bottom: 2rem;
            font-size: 2rem;
        }
        .requirement {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            margin-bottom: 0.5rem;
            background: #0f172a;
            border-radius: 0.5rem;
            border: 1px solid #334155;
        }
        .requirement-name {
            font-weight: 500;
        }
        .status {
            padding: 0.25rem 0.75rem;
            border-radius: 0.25rem;
            font-weight: 600;
            font-size: 0.875rem;
        }
        .status.ok {
            background: rgba(16, 185, 129, 0.2);
            color: #10b981;
            border: 1px solid #10b981;
        }
        .status.error {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
            border: 1px solid #ef4444;
        }
        .status.warning {
            background: rgba(245, 158, 11, 0.2);
            color: #f59e0b;
            border: 1px solid #f59e0b;
        }
        .section {
            margin-bottom: 2rem;
        }
        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #94a3b8;
            border-bottom: 1px solid #334155;
            padding-bottom: 0.5rem;
        }
        .info {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid #3b82f6;
            border-radius: 0.5rem;
            padding: 1rem;
            margin-bottom: 2rem;
            color: #3b82f6;
        }
        .summary {
            text-align: center;
            padding: 1.5rem;
            border-radius: 0.5rem;
            margin-top: 2rem;
            font-weight: 600;
        }
        .summary.ok {
            background: rgba(16, 185, 129, 0.2);
            color: #10b981;
            border: 1px solid #10b981;
        }
        .summary.error {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
            border: 1px solid #ef4444;
        }
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #3b82f6;
            color: white;
            text-decoration: none;
            border-radius: 0.5rem;
            margin-top: 1rem;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #1e40af;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 System Requirements Check</h1>
        
        <div class="info">
            <strong>IPTV XUI One Content Manager</strong><br>
            This tool checks if your system meets all requirements to run the application.
        </div>

        <?php
        $requirements = [];
        $errors = 0;
        $warnings = 0;

        // PHP Version Check
        $phpVersion = PHP_VERSION;
        $minPhpVersion = '7.4.0';
        $requirements[] = [
            'name' => 'PHP Version (>= 7.4.0)',
            'current' => $phpVersion,
            'status' => version_compare($phpVersion, $minPhpVersion, '>=') ? 'ok' : 'error',
            'required' => true
        ];
        if (!version_compare($phpVersion, $minPhpVersion, '>=')) $errors++;

        // PDO Extension
        $requirements[] = [
            'name' => 'PDO Extension',
            'current' => extension_loaded('pdo') ? 'Installed' : 'Not installed',
            'status' => extension_loaded('pdo') ? 'ok' : 'error',
            'required' => true
        ];
        if (!extension_loaded('pdo')) $errors++;

        // PDO MySQL Driver
        $requirements[] = [
            'name' => 'PDO MySQL Driver',
            'current' => extension_loaded('pdo_mysql') ? 'Installed' : 'Not installed',
            'status' => extension_loaded('pdo_mysql') ? 'ok' : 'error',
            'required' => true
        ];
        if (!extension_loaded('pdo_mysql')) $errors++;

        // cURL Extension
        $requirements[] = [
            'name' => 'cURL Extension',
            'current' => extension_loaded('curl') ? 'Installed' : 'Not installed',
            'status' => extension_loaded('curl') ? 'ok' : 'warning',
            'required' => false
        ];
        if (!extension_loaded('curl')) $warnings++;

        // JSON Extension
        $requirements[] = [
            'name' => 'JSON Extension',
            'current' => extension_loaded('json') ? 'Installed' : 'Not installed',
            'status' => extension_loaded('json') ? 'ok' : 'error',
            'required' => true
        ];
        if (!extension_loaded('json')) $errors++;

        // mbstring Extension
        $requirements[] = [
            'name' => 'mbstring Extension',
            'current' => extension_loaded('mbstring') ? 'Installed' : 'Not installed',
            'status' => extension_loaded('mbstring') ? 'ok' : 'warning',
            'required' => false
        ];
        if (!extension_loaded('mbstring')) $warnings++;

        // OpenSSL Extension
        $requirements[] = [
            'name' => 'OpenSSL Extension',
            'current' => extension_loaded('openssl') ? 'Installed' : 'Not installed',
            'status' => extension_loaded('openssl') ? 'ok' : 'warning',
            'required' => false
        ];
        if (!extension_loaded('openssl')) $warnings++;

        // File Permissions
        $writableDir = __DIR__ . '/logs';
        $isWritable = is_dir($writableDir) ? is_writable($writableDir) : is_writable(__DIR__);
        $requirements[] = [
            'name' => 'Write Permissions',
            'current' => $isWritable ? 'Writable' : 'Not writable',
            'status' => $isWritable ? 'ok' : 'warning',
            'required' => false
        ];
        if (!$isWritable) $warnings++;

        // Memory Limit
        $memoryLimit = ini_get('memory_limit');
        $memoryBytes = $memoryLimit === '-1' ? PHP_INT_MAX : (int)$memoryLimit * 1024 * 1024;
        $minMemory = 128 * 1024 * 1024; // 128MB
        $requirements[] = [
            'name' => 'Memory Limit (>= 128MB)',
            'current' => $memoryLimit,
            'status' => $memoryBytes >= $minMemory ? 'ok' : 'warning',
            'required' => false
        ];
        if ($memoryBytes < $minMemory) $warnings++;

        // Max Execution Time
        $maxExecTime = ini_get('max_execution_time');
        $requirements[] = [
            'name' => 'Max Execution Time',
            'current' => $maxExecTime . ' seconds',
            'status' => $maxExecTime >= 30 || $maxExecTime == 0 ? 'ok' : 'warning',
            'required' => false
        ];
        if ($maxExecTime < 30 && $maxExecTime != 0) $warnings++;
        ?>

        <div class="section">
            <div class="section-title">📋 Requirements Check</div>
            <?php foreach ($requirements as $req): ?>
                <div class="requirement">
                    <div class="requirement-name">
                        <?= htmlspecialchars($req['name']) ?>
                        <?= $req['required'] ? ' <strong>(Required)</strong>' : ' (Optional)' ?>
                    </div>
                    <div>
                        <span style="margin-right: 1rem; color: #94a3b8;">
                            <?= htmlspecialchars($req['current']) ?>
                        </span>
                        <span class="status <?= $req['status'] ?>">
                            <?= $req['status'] === 'ok' ? '✓ OK' : ($req['status'] === 'error' ? '✗ ERROR' : '⚠ WARNING') ?>
                        </span>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <div class="section">
            <div class="section-title">🔍 System Information</div>
            <div class="requirement">
                <div class="requirement-name">PHP Version</div>
                <div><?= PHP_VERSION ?></div>
            </div>
            <div class="requirement">
                <div class="requirement-name">Operating System</div>
                <div><?= PHP_OS ?></div>
            </div>
            <div class="requirement">
                <div class="requirement-name">Server Software</div>
                <div><?= $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown' ?></div>
            </div>
            <div class="requirement">
                <div class="requirement-name">Document Root</div>
                <div><?= $_SERVER['DOCUMENT_ROOT'] ?? __DIR__ ?></div>
            </div>
        </div>

        <?php if ($errors > 0): ?>
            <div class="summary error">
                ❌ <strong>System Not Ready</strong><br>
                Found <?= $errors ?> critical error(s) and <?= $warnings ?> warning(s).<br>
                Please install missing PHP extensions before proceeding.
                
                <?php if (!extension_loaded('pdo_mysql')): ?>
                    <br><br>
                    <strong>To install PDO MySQL on Windows:</strong><br>
                    1. Uncomment <code>extension=pdo_mysql</code> in your php.ini file<br>
                    2. Restart your web server<br><br>
                    
                    <strong>To install PDO MySQL on Linux:</strong><br>
                    <code>sudo apt-get install php-mysql</code> (Ubuntu/Debian)<br>
                    <code>sudo yum install php-mysql</code> (CentOS/RHEL)
                <?php endif; ?>
            </div>
        <?php else: ?>
            <div class="summary ok">
                ✅ <strong>System Ready!</strong><br>
                All critical requirements are met. 
                <?php if ($warnings > 0): ?>
                    Found <?= $warnings ?> warning(s) that should be addressed for optimal performance.
                <?php endif; ?>
                <br>
                <a href="public/index.php" class="btn">🚀 Launch Application</a>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>
