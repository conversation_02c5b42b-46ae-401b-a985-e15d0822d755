<?php
/**
 * Simple Test Script
 * =================
 * 
 * Basic test to verify PHP functionality and database connection
 */

echo "🔧 IPTV XUI One Content Manager - Simple Test\n";
echo "============================================\n\n";

// 1. Test PHP version
echo "📋 PHP Version: " . PHP_VERSION . "\n";

// 2. Test file access
echo "📁 Testing file access...\n";
if (file_exists('.env')) {
    echo "✅ .env file found\n";
} else {
    echo "❌ .env file not found\n";
}

if (file_exists('config/database.php')) {
    echo "✅ database.php found\n";
} else {
    echo "❌ database.php not found\n";
}

// 3. Test environment loading
echo "\n🔧 Testing environment loading...\n";
try {
    require_once 'config/env.php';
    
    if (loadEnvironmentVariables()) {
        echo "✅ Environment variables loaded\n";
        
        // Show database config (without password)
        echo "🔌 Database Configuration:\n";
        echo "   Host: " . ($_ENV['DB_HOST'] ?? 'not set') . "\n";
        echo "   Port: " . ($_ENV['DB_PORT'] ?? 'not set') . "\n";
        echo "   Database: " . ($_ENV['DB_NAME'] ?? 'not set') . "\n";
        echo "   User: " . ($_ENV['DB_USER'] ?? 'not set') . "\n";
        echo "   Password: " . (isset($_ENV['DB_PASSWORD']) ? '[SET]' : '[NOT SET]') . "\n";
        
    } else {
        echo "❌ Failed to load environment variables\n";
    }
} catch (Exception $e) {
    echo "❌ Error loading environment: " . $e->getMessage() . "\n";
}

// 4. Test database connection
echo "\n🗄️ Testing database connection...\n";
try {
    require_once 'config/database.php';
    
    $dsn = "mysql:host=" . ($_ENV['DB_HOST'] ?? 'localhost') . 
           ";port=" . ($_ENV['DB_PORT'] ?? '3306') . 
           ";dbname=" . ($_ENV['DB_NAME'] ?? 'xui') . 
           ";charset=utf8mb4";
    
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_TIMEOUT => 5
    ];
    
    $pdo = new PDO($dsn, $_ENV['DB_USER'], $_ENV['DB_PASSWORD'], $options);
    echo "✅ Database connection successful\n";
    
    // Test basic query
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM streams LIMIT 1");
    $result = $stmt->fetch();
    echo "📊 Total streams in database: " . number_format($result['total']) . "\n";
    
    // Test tables
    echo "\n📋 Checking required tables:\n";
    $tables = ['streams', 'streams_categories', 'streams_series', 'streams_episodes'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "   ✅ $table\n";
        } else {
            echo "   ❌ $table (missing)\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "\n";
}

echo "\n✨ Test completed!\n";
