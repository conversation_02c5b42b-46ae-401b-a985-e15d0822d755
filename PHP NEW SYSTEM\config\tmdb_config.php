<?php
/**
 * TMDB API Configuration for IPTV XUI One Content Manager
 * ======================================================
 *
 * Configuration for The Movie Database (TMDB) API integration
 */

// Load environment variables
require_once __DIR__ . '/env.php';

// TMDB API Configuration
define('TMDB_CONFIG', [
    'api_key' => env('TMDB_API_KEY', '201066b4b17391d478e55247f43eed64'),
    'base_url' => 'https://api.themoviedb.org/3',
    'image_base_url' => 'https://image.tmdb.org/t/p',
    'secure_base_url' => 'https://image.tmdb.org/t/p',
    'language' => 'es-ES',
    'region' => 'ES',
    'include_adult' => false,
    'timeout' => 30,
    'retry_attempts' => 3,
    'retry_delay' => 1
]);

// Image size configurations
define('TMDB_IMAGE_SIZES', [
    'poster' => [
        'w92', 'w154', 'w185', 'w342', 'w500', 'w780', 'original'
    ],
    'backdrop' => [
        'w300', 'w780', 'w1280', 'original'
    ],
    'profile' => [
        'w45', 'w185', 'h632', 'original'
    ],
    'logo' => [
        'w45', 'w92', 'w154', 'w185', 'w300', 'w500', 'original'
    ],
    'still' => [
        'w92', 'w185', 'w300', 'original'
    ]
]);

// Default image sizes for different contexts
define('TMDB_DEFAULT_SIZES', [
    'poster_small' => 'w185',
    'poster_medium' => 'w342',
    'poster_large' => 'w500',
    'backdrop_small' => 'w300',
    'backdrop_medium' => 'w780',
    'backdrop_large' => 'w1280',
    'profile_small' => 'w45',
    'profile_medium' => 'w185'
]);

// API endpoints
define('TMDB_ENDPOINTS', [
    // Search endpoints
    'search_movie' => '/search/movie',
    'search_tv' => '/search/tv',
    'search_multi' => '/search/multi',
    'search_person' => '/search/person',
    'search_collection' => '/search/collection',
    'search_company' => '/search/company',
    'search_keyword' => '/search/keyword',
    
    // Movie endpoints
    'movie_details' => '/movie/{movie_id}',
    'movie_credits' => '/movie/{movie_id}/credits',
    'movie_images' => '/movie/{movie_id}/images',
    'movie_videos' => '/movie/{movie_id}/videos',
    'movie_recommendations' => '/movie/{movie_id}/recommendations',
    'movie_similar' => '/movie/{movie_id}/similar',
    'movie_reviews' => '/movie/{movie_id}/reviews',
    'movie_keywords' => '/movie/{movie_id}/keywords',
    'movie_external_ids' => '/movie/{movie_id}/external_ids',
    
    // TV Show endpoints
    'tv_details' => '/tv/{tv_id}',
    'tv_credits' => '/tv/{tv_id}/credits',
    'tv_images' => '/tv/{tv_id}/images',
    'tv_videos' => '/tv/{tv_id}/videos',
    'tv_recommendations' => '/tv/{tv_id}/recommendations',
    'tv_similar' => '/tv/{tv_id}/similar',
    'tv_reviews' => '/tv/{tv_id}/reviews',
    'tv_keywords' => '/tv/{tv_id}/keywords',
    'tv_external_ids' => '/tv/{tv_id}/external_ids',
    'tv_season' => '/tv/{tv_id}/season/{season_number}',
    'tv_episode' => '/tv/{tv_id}/season/{season_number}/episode/{episode_number}',
    
    // Discovery endpoints
    'discover_movie' => '/discover/movie',
    'discover_tv' => '/discover/tv',
    
    // Trending endpoints
    'trending_all' => '/trending/all/{time_window}',
    'trending_movie' => '/trending/movie/{time_window}',
    'trending_tv' => '/trending/tv/{time_window}',
    'trending_person' => '/trending/person/{time_window}',
    
    // Popular/Top Rated endpoints
    'popular_movies' => '/movie/popular',
    'top_rated_movies' => '/movie/top_rated',
    'upcoming_movies' => '/movie/upcoming',
    'now_playing_movies' => '/movie/now_playing',
    'popular_tv' => '/tv/popular',
    'top_rated_tv' => '/tv/top_rated',
    'on_the_air_tv' => '/tv/on_the_air',
    'airing_today_tv' => '/tv/airing_today',
    
    // Configuration endpoints
    'configuration' => '/configuration',
    'countries' => '/configuration/countries',
    'jobs' => '/configuration/jobs',
    'languages' => '/configuration/languages',
    'timezones' => '/configuration/timezones',
    
    // Genre endpoints
    'movie_genres' => '/genre/movie/list',
    'tv_genres' => '/genre/tv/list'
]);

// Cache configuration for TMDB data
define('TMDB_CACHE_CONFIG', [
    'enabled' => true,
    'ttl' => [
        'search_results' => 3600,      // 1 hour
        'movie_details' => 86400,      // 24 hours
        'tv_details' => 86400,         // 24 hours
        'person_details' => 86400,     // 24 hours
        'images' => 604800,            // 1 week
        'configuration' => 604800,     // 1 week
        'genres' => 604800,            // 1 week
        'trending' => 3600,            // 1 hour
        'popular' => 3600              // 1 hour
    ],
    'prefix' => 'tmdb_',
    'compression' => true
]);

// Rate limiting configuration
define('TMDB_RATE_LIMIT', [
    'requests_per_second' => 4,
    'requests_per_minute' => 40,
    'burst_limit' => 10,
    'backoff_strategy' => 'exponential',
    'max_backoff_time' => 60
]);

// Content matching configuration
define('TMDB_MATCHING', [
    'similarity_threshold' => 0.7,
    'year_tolerance' => 1,
    'title_cleaning_patterns' => [
        '/\[.*?\]/',           // Remove [brackets]
        '/\(.*?\)/',           // Remove (parentheses)
        '/\d{4}/',             // Remove years
        '/\bHD\b/i',           // Remove HD
        '/\bFHD\b/i',          // Remove FHD
        '/\b4K\b/i',           // Remove 4K
        '/\b1080p\b/i',        // Remove 1080p
        '/\b720p\b/i',         // Remove 720p
        '/\b480p\b/i',         // Remove 480p
        '/\bHDR\b/i',          // Remove HDR
        '/\b60fps\b/i',        // Remove 60fps
        '/\bBluRay\b/i',       // Remove BluRay
        '/\bDVDRip\b/i',       // Remove DVDRip
        '/\bWEBRip\b/i',       // Remove WEBRip
        '/\bx264\b/i',         // Remove x264
        '/\bx265\b/i',         // Remove x265
        '/\bHEVC\b/i',         // Remove HEVC
        '/\s+/',               // Multiple spaces to single
    ],
    'language_detection' => [
        'spanish' => ['es', 'spa', 'spanish', 'español', 'castellano'],
        'english' => ['en', 'eng', 'english', 'inglés'],
        'french' => ['fr', 'fra', 'french', 'français'],
        'german' => ['de', 'deu', 'german', 'deutsch'],
        'italian' => ['it', 'ita', 'italian', 'italiano'],
        'portuguese' => ['pt', 'por', 'portuguese', 'português']
    ]
]);

// Quality detection patterns
define('TMDB_QUALITY_PATTERNS', [
    '4K' => [
        'patterns' => ['4K', '2160p', 'UHD', 'Ultra HD'],
        'priority' => 1
    ],
    'FHD' => [
        'patterns' => ['1080p', 'FHD', 'Full HD'],
        'priority' => 2
    ],
    'HD' => [
        'patterns' => ['720p', 'HD'],
        'priority' => 3
    ],
    'SD' => [
        'patterns' => ['480p', 'SD'],
        'priority' => 4
    ],
    'HDR' => [
        'patterns' => ['HDR', 'HDR10', 'Dolby Vision'],
        'priority' => 1
    ],
    '60FPS' => [
        'patterns' => ['60fps', '60FPS', 'HFR'],
        'priority' => 1
    ]
]);

// Error handling configuration
define('TMDB_ERROR_CONFIG', [
    'log_errors' => true,
    'fallback_enabled' => true,
    'fallback_data' => [
        'poster_path' => '/assets/images/no-poster.jpg',
        'backdrop_path' => '/assets/images/no-backdrop.jpg',
        'overview' => 'No description available',
        'vote_average' => 0,
        'vote_count' => 0,
        'popularity' => 0
    ],
    'retry_on_errors' => [401, 429, 500, 502, 503, 504],
    'ignore_errors' => [404] // Don't retry on not found
]);

// Batch processing configuration
define('TMDB_BATCH_CONFIG', [
    'enabled' => true,
    'batch_size' => 20,
    'delay_between_batches' => 1, // seconds
    'max_concurrent_requests' => 5,
    'timeout_per_request' => 10,
    'progress_callback' => true
]);

// Helper function to get TMDB image URL
function get_tmdb_image_url($path, $size = 'w500') {
    if (empty($path)) {
        return TMDB_ERROR_CONFIG['fallback_data']['poster_path'];
    }
    
    if (strpos($path, 'http') === 0) {
        return $path;
    }
    
    return TMDB_CONFIG['image_base_url'] . '/' . $size . $path;
}

// Helper function to clean title for TMDB search
function clean_title_for_tmdb($title) {
    $cleaned = $title;
    
    foreach (TMDB_MATCHING['title_cleaning_patterns'] as $pattern) {
        $cleaned = preg_replace($pattern, ' ', $cleaned);
    }
    
    return trim($cleaned);
}

// Helper function to detect quality from title
function detect_quality_from_title($title) {
    $qualities = [];
    
    foreach (TMDB_QUALITY_PATTERNS as $quality => $config) {
        foreach ($config['patterns'] as $pattern) {
            if (stripos($title, $pattern) !== false) {
                $qualities[] = [
                    'quality' => $quality,
                    'priority' => $config['priority']
                ];
                break;
            }
        }
    }
    
    // Sort by priority
    usort($qualities, function($a, $b) {
        return $a['priority'] - $b['priority'];
    });
    
    return array_column($qualities, 'quality');
}
