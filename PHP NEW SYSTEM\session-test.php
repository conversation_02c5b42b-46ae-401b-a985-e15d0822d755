<?php
/**
 * Session Test - IPTV XUI One Content Manager
 * ==========================================
 * 
 * Test session functionality and debug session issues
 */

// Include path management first
require_once __DIR__ . '/config/paths.php';

// Initialize session management
require_once getConfigPath('session.php');

// Test session functionality
setSession('test_key', 'test_value_' . time());
setSession('page_visits', getSession('page_visits', 0) + 1);

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Session Test - IPTV Manager</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #0f172a;
            color: #f1f5f9;
            margin: 0;
            padding: 2rem;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .card {
            background: #1e293b;
            border: 1px solid #334155;
            border-radius: 1rem;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .success {
            background: rgba(16, 185, 129, 0.1);
            border-color: #10b981;
            color: #10b981;
        }
        .error {
            background: rgba(239, 68, 68, 0.1);
            border-color: #ef4444;
            color: #ef4444;
        }
        .info {
            background: rgba(59, 130, 246, 0.1);
            border-color: #3b82f6;
            color: #3b82f6;
        }
        pre {
            background: #0f172a;
            padding: 1rem;
            border-radius: 0.5rem;
            overflow-x: auto;
            border: 1px solid #334155;
        }
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #3b82f6;
            color: white;
            text-decoration: none;
            border-radius: 0.5rem;
            margin: 0.5rem 0.5rem 0.5rem 0;
        }
        .btn:hover {
            background: #1e40af;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Session Test - IPTV Manager</h1>
        
        <!-- Session Status -->
        <div class="card <?= session_status() === PHP_SESSION_ACTIVE ? 'success' : 'error' ?>">
            <h3>Session Status</h3>
            <p><strong>Status:</strong> 
                <?php
                switch (session_status()) {
                    case PHP_SESSION_DISABLED:
                        echo "❌ Sessions are disabled";
                        break;
                    case PHP_SESSION_NONE:
                        echo "⚠️ No session started";
                        break;
                    case PHP_SESSION_ACTIVE:
                        echo "✅ Session is active";
                        break;
                }
                ?>
            </p>
            <p><strong>Session ID:</strong> <?= session_id() ?></p>
            <p><strong>Session Name:</strong> <?= session_name() ?></p>
        </div>

        <!-- Session Data -->
        <div class="card info">
            <h3>Session Data</h3>
            <p><strong>Test Value:</strong> <?= getSession('test_key', 'Not set') ?></p>
            <p><strong>Page Visits:</strong> <?= getSession('page_visits', 0) ?></p>
            <p><strong>Last Regeneration:</strong> 
                <?= getSession('last_regeneration') ? date('Y-m-d H:i:s', getSession('last_regeneration')) : 'Not set' ?>
            </p>
        </div>

        <!-- Full Session Info -->
        <div class="card">
            <h3>Complete Session Information</h3>
            <pre><?= htmlspecialchars(print_r(getSessionInfo(), true)) ?></pre>
        </div>

        <!-- PHP Session Configuration -->
        <div class="card">
            <h3>PHP Session Configuration</h3>
            <pre><?php
$sessionConfig = [
    'session.save_handler' => ini_get('session.save_handler'),
    'session.save_path' => ini_get('session.save_path'),
    'session.use_cookies' => ini_get('session.use_cookies'),
    'session.use_only_cookies' => ini_get('session.use_only_cookies'),
    'session.cookie_lifetime' => ini_get('session.cookie_lifetime'),
    'session.cookie_path' => ini_get('session.cookie_path'),
    'session.cookie_domain' => ini_get('session.cookie_domain'),
    'session.cookie_secure' => ini_get('session.cookie_secure'),
    'session.cookie_httponly' => ini_get('session.cookie_httponly'),
    'session.cookie_samesite' => ini_get('session.cookie_samesite'),
    'session.gc_maxlifetime' => ini_get('session.gc_maxlifetime'),
];
echo htmlspecialchars(print_r($sessionConfig, true));
?></pre>
        </div>

        <!-- Actions -->
        <div class="card">
            <h3>Test Actions</h3>
            <a href="?action=refresh" class="btn">🔄 Refresh Page</a>
            <a href="?action=clear" class="btn">🗑️ Clear Session</a>
            <a href="index.php" class="btn">🏠 Go to Main App</a>
            <a href="system-check.php" class="btn">🔍 System Check</a>
        </div>

        <?php
        // Handle actions
        if (isset($_GET['action'])) {
            switch ($_GET['action']) {
                case 'clear':
                    destroySession();
                    echo '<div class="card success"><h3>✅ Session Cleared</h3><p>Session has been destroyed. <a href="session-test.php">Refresh</a> to start a new session.</p></div>';
                    break;
                case 'refresh':
                    echo '<div class="card info"><h3>🔄 Page Refreshed</h3><p>Page visit counter should have increased.</p></div>';
                    break;
            }
        }
        ?>

        <!-- Troubleshooting -->
        <div class="card">
            <h3>🔧 Troubleshooting</h3>
            <ul>
                <li><strong>If session is not active:</strong> Check PHP session configuration</li>
                <li><strong>If session data is lost:</strong> Check session save path permissions</li>
                <li><strong>If getting session errors:</strong> Check for multiple session_start() calls</li>
                <li><strong>If cookies not working:</strong> Check HTTPS/HTTP settings</li>
            </ul>
        </div>
    </div>
</body>
</html>
