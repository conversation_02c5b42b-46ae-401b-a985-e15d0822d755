<?php
/**
 * Test Multiple Ports for XUI Database
 * ====================================
 * 
 * Prueba diferentes puertos para encontrar el correcto
 */

echo "<h1>Prueba de Puertos para Base de Datos XUI</h1>\n";

$host = '**************';
$username = 'infest84';
$password = 'GZM6hh12zHKgfg34FghtAQ84WxloPRX]SV$fP';
$database = 'xui';

// Puertos comunes para MySQL/MariaDB en servidores XUI
$ports_to_test = [
    3306 => 'MySQL estándar',
    3307 => 'MySQL alternativo',
    3308 => 'MySQL secundario', 
    33060 => 'MySQL X Protocol',
    33061 => 'MySQL X alternativo',
    25000 => 'XUI común 1',
    25001 => 'XUI común 2',
    25002 => 'XUI común 3',
    7999 => 'XUI alternativo 1',
    8000 => 'XUI alternativo 2',
    8001 => 'XUI alternativo 3'
];

echo "<h2>Probando conectividad a puertos...</h2>\n";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
echo "<tr style='background: #1e293b; color: white;'>\n";
echo "<th style='padding: 0.5rem;'>Puerto</th>\n";
echo "<th style='padding: 0.5rem;'>Descripción</th>\n";
echo "<th style='padding: 0.5rem;'>Conectividad</th>\n";
echo "<th style='padding: 0.5rem;'>Base de Datos</th>\n";
echo "</tr>\n";

$successful_ports = [];

foreach ($ports_to_test as $port => $description) {
    echo "<tr>\n";
    echo "<td style='padding: 0.5rem; text-align: center;'><strong>$port</strong></td>\n";
    echo "<td style='padding: 0.5rem;'>$description</td>\n";
    
    // Test network connectivity
    $connection = @fsockopen($host, $port, $errno, $errstr, 5);
    
    if ($connection) {
        fclose($connection);
        echo "<td style='padding: 0.5rem; color: green;'>✅ Abierto</td>\n";
        
        // Test database connection
        try {
            $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
            $pdo = new PDO($dsn, $username, $password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_TIMEOUT => 5
            ]);
            
            // Test basic query
            $stmt = $pdo->query("SELECT VERSION() as version");
            $result = $stmt->fetch();
            
            echo "<td style='padding: 0.5rem; color: green;'><strong>✅ CONECTADO!</strong><br>";
            echo "<small>MySQL: " . $result['version'] . "</small></td>\n";
            
            $successful_ports[] = [
                'port' => $port,
                'description' => $description,
                'version' => $result['version']
            ];
            
        } catch (Exception $e) {
            echo "<td style='padding: 0.5rem; color: orange;'>⚠️ Puerto abierto pero error DB<br>";
            echo "<small>" . htmlspecialchars($e->getMessage()) . "</small></td>\n";
        }
        
    } else {
        echo "<td style='padding: 0.5rem; color: red;'>❌ Cerrado</td>\n";
        echo "<td style='padding: 0.5rem; color: gray;'>-</td>\n";
    }
    
    echo "</tr>\n";
    
    // Small delay to avoid overwhelming the server
    usleep(100000); // 0.1 seconds
}

echo "</table>\n";

if (!empty($successful_ports)) {
    echo "<h2 style='color: green;'>🎉 ¡Conexiones Exitosas Encontradas!</h2>\n";
    
    foreach ($successful_ports as $success) {
        echo "<div style='background: rgba(16, 185, 129, 0.1); border: 1px solid #10b981; padding: 1rem; margin: 1rem 0; border-radius: 0.5rem;'>\n";
        echo "<h3 style='color: #10b981; margin: 0;'>Puerto {$success['port']} - {$success['description']}</h3>\n";
        echo "<p><strong>MySQL Version:</strong> {$success['version']}</p>\n";
        echo "<p><strong>Configuración para usar:</strong></p>\n";
        echo "<pre style='background: #0f172a; color: #f1f5f9; padding: 1rem; border-radius: 0.25rem;'>";
        echo "DB_HOST=**************\n";
        echo "DB_PORT={$success['port']}\n";
        echo "DB_NAME=xui\n";
        echo "DB_USER=infest84\n";
        echo "DB_PASSWORD=GZM6hh12zHKgfg34FghtAQ84WxloPRX]SV\$fP";
        echo "</pre>\n";
        echo "</div>\n";
    }
    
    // Test XUI tables on successful connections
    echo "<h2>Verificando Tablas XUI</h2>\n";
    
    foreach ($successful_ports as $success) {
        echo "<h3>Puerto {$success['port']}:</h3>\n";
        
        try {
            $dsn = "mysql:host=$host;port={$success['port']};dbname=$database;charset=utf8mb4";
            $pdo = new PDO($dsn, $username, $password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_TIMEOUT => 5
            ]);
            
            $xui_tables = ['streams', 'streams_categories', 'streams_series', 'streams_episodes'];
            
            echo "<ul>\n";
            foreach ($xui_tables as $table) {
                $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
                if ($stmt->fetch()) {
                    $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
                    $count = $stmt->fetch()['count'];
                    echo "<li style='color: green;'>✅ <strong>$table</strong>: $count registros</li>\n";
                } else {
                    echo "<li style='color: red;'>❌ <strong>$table</strong>: No encontrada</li>\n";
                }
            }
            echo "</ul>\n";
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>Error verificando tablas: " . htmlspecialchars($e->getMessage()) . "</p>\n";
        }
    }
    
    echo "<h2>🚀 Próximos Pasos</h2>\n";
    echo "<ol>\n";
    echo "<li>Usar la configuración del puerto que funciona</li>\n";
    echo "<li><a href='update-config.php?port=" . $successful_ports[0]['port'] . "'>Actualizar configuración automáticamente</a></li>\n";
    echo "<li><a href='sistema-real.php'>Lanzar sistema con configuración correcta</a></li>\n";
    echo "</ol>\n";
    
} else {
    echo "<h2 style='color: red;'>❌ No se encontraron puertos accesibles</h2>\n";
    echo "<h3>Posibles soluciones:</h3>\n";
    echo "<ul>\n";
    echo "<li><strong>Firewall:</strong> Tu firewall o router puede estar bloqueando conexiones MySQL</li>\n";
    echo "<li><strong>ISP:</strong> Tu proveedor de internet puede bloquear puertos de base de datos</li>\n";
    echo "<li><strong>VPN:</strong> Intenta usar una VPN para cambiar tu IP</li>\n";
    echo "<li><strong>Puerto personalizado:</strong> El servidor puede usar un puerto no estándar</li>\n";
    echo "<li><strong>Acceso remoto:</strong> El servidor puede no permitir conexiones remotas</li>\n";
    echo "</ul>\n";
    
    echo "<h3>Alternativas:</h3>\n";
    echo "<ul>\n";
    echo "<li><strong>Túnel SSH:</strong> Si tienes acceso SSH al servidor</li>\n";
    echo "<li><strong>API REST:</strong> Crear una API en el servidor para acceder a los datos</li>\n";
    echo "<li><strong>Hosting web:</strong> Subir el sistema a un hosting que tenga mejor conectividad</li>\n";
    echo "</ul>\n";
}

echo "<h2>📊 Información del Sistema</h2>\n";
echo "<ul>\n";
echo "<li><strong>Tu IP:</strong> " . ($_SERVER['HTTP_CLIENT_IP'] ?? $_SERVER['HTTP_X_FORWARDED_FOR'] ?? $_SERVER['REMOTE_ADDR'] ?? 'Desconocida') . "</li>\n";
echo "<li><strong>Servidor objetivo:</strong> **************</li>\n";
echo "<li><strong>Latencia:</strong> ~58ms (según ping)</li>\n";
echo "<li><strong>PHP Version:</strong> " . PHP_VERSION . "</li>\n";
echo "</ul>\n";
?>
