# 🚀 IPTV XUI Manager - Deployment Guide

## 📋 Quick Setup

### 🌐 For Hostinger Hosting:

1. **Upload Files:**
   - Upload entire `PHP NEW SYSTEM` folder to your hosting
   - Place contents in `public_html` or subdirectory

2. **Configure Database:**
   - Copy `.env.hostinger` to `.env`
   - Edit `.env` with your Hostinger database details:
   ```
   DB_HOST=localhost
   DB_NAME=your_hostinger_db_name
   DB_USER=your_hostinger_db_user
   DB_PASSWORD=your_hostinger_db_password
   ```

3. **Set Permissions:**
   - Ensure `logs/` folder is writable (755)
   - Ensure `.env` file is readable (644)

4. **Access:**
   - Visit: `https://yourdomain.com/public/`
   - Or: `https://yourdomain.com/` (if uploaded to root)

### 💻 For Local Development:

1. **Install XAMPP/WAMP:**
   - Download and install XAMPP or WAMP
   - Start Apache and MySQL services

2. **Setup Project:**
   - Copy project to `htdocs/` (XAMPP) or `www/` (WAMP)
   - Copy `.env.local` to `.env`

3. **Configure Database:**
   - Create database named `xui` in phpMyAdmin
   - Update `.env` if needed (usually defaults work)

4. **Access:**
   - Visit: `http://localhost/PHP NEW SYSTEM/public/`

## 🔧 Troubleshooting

### Common Issues:

1. **Database Connection Failed:**
   - Check credentials in `.env`
   - Verify database exists
   - Ensure PHP MySQL extension is installed

2. **File Not Found Errors:**
   - Check file permissions
   - Verify upload completed successfully
   - Ensure index.php exists in public folder

3. **Blank Page:**
   - Enable error reporting in PHP
   - Check server error logs
   - Verify PHP version (7.4+ required)

### Hostinger Specific:

1. **Database Info:**
   - Find in Hostinger control panel > Databases
   - Use provided hostname, username, password

2. **File Manager:**
   - Use Hostinger File Manager or FTP
   - Upload to `public_html` directory

3. **PHP Version:**
   - Set to PHP 8.0+ in control panel
   - Enable required extensions

## 📱 Features Working:

- ✅ Responsive dashboard
- ✅ M3U file management
- ✅ Analytics and reporting
- ✅ Settings configuration
- ✅ Demo mode (if database fails)
- ✅ Auto-detection of environment

## 🆘 Support:

If you encounter issues:
1. Check `/check-requirements.php` for system status
2. Use `/test-database.php` for connection testing
3. Review error logs in hosting control panel
