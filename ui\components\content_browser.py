"""
Explorador de contenido XUI One
==============================

Panel para navegar y visualizar el contenido real de la base de datos XUI One.
Muestra películas, series, canales de TV en vivo y permite búsquedas.
"""

import asyncio
import tkinter as tk
from tkinter import ttk, messagebox
import customtkinter as ctk
import logging
from typing import Dict, List, Optional
import threading
import json

class ContentBrowser:
    """Explorador de contenido XUI One"""
    
    def __init__(self, parent, settings, db_manager):
        self.parent = parent
        self.settings = settings
        self.db_manager = db_manager
        self.logger = logging.getLogger("iptv_manager.content_browser")
        
        # Estado
        self.current_content = []
        self.current_filter = "all"
        self.current_page = 0
        self.items_per_page = 50
        
        # Crear interfaz
        self._create_layout()
        self._create_filters()
        self._create_content_area()
        self._create_pagination()
        
        # Cargar contenido inicial
        asyncio.create_task(self._load_content())
    
    def _create_layout(self):
        """Crear layout principal"""
        self.main_frame = ctk.CTkFrame(self.parent)
        self.main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Título
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="📺 Explorador de Contenido XUI One",
            font=("Arial", 24, "bold")
        )
        title_label.pack(pady=(20, 10))
    
    def _create_filters(self):
        """Crear filtros y búsqueda"""
        filters_frame = ctk.CTkFrame(self.main_frame)
        filters_frame.pack(fill="x", padx=20, pady=10)
        
        # Filtros por tipo
        filter_label = ctk.CTkLabel(filters_frame, text="Filtrar por tipo:")
        filter_label.pack(side="left", padx=(10, 5))
        
        self.filter_var = tk.StringVar(value="all")
        filter_options = ["Todos", "Películas", "Series", "TV en Vivo"]
        filter_menu = ctk.CTkOptionMenu(
            filters_frame,
            variable=self.filter_var,
            values=filter_options,
            command=self._on_filter_change
        )
        filter_menu.pack(side="left", padx=5)
        
        # Búsqueda
        search_label = ctk.CTkLabel(filters_frame, text="Buscar:")
        search_label.pack(side="left", padx=(20, 5))
        
        self.search_var = tk.StringVar()
        self.search_entry = ctk.CTkEntry(
            filters_frame,
            textvariable=self.search_var,
            placeholder_text="Buscar contenido...",
            width=200
        )
        self.search_entry.pack(side="left", padx=5)
        self.search_entry.bind("<Return>", lambda e: self._perform_search())
        
        search_btn = ctk.CTkButton(
            filters_frame,
            text="🔍",
            width=40,
            command=self._perform_search
        )
        search_btn.pack(side="left", padx=5)
        
        # Botón refrescar
        refresh_btn = ctk.CTkButton(
            filters_frame,
            text="🔄 Refrescar",
            command=self._refresh_content
        )
        refresh_btn.pack(side="right", padx=10)
    
    def _create_content_area(self):
        """Crear área de contenido"""
        content_frame = ctk.CTkFrame(self.main_frame)
        content_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # Crear Treeview para mostrar contenido
        columns = ("ID", "Nombre", "Tipo", "Categoría", "TMDB", "Año", "Rating")
        self.tree = ttk.Treeview(content_frame, columns=columns, show="headings", height=15)
        
        # Configurar columnas
        self.tree.heading("ID", text="ID")
        self.tree.heading("Nombre", text="Nombre")
        self.tree.heading("Tipo", text="Tipo")
        self.tree.heading("Categoría", text="Categoría")
        self.tree.heading("TMDB", text="TMDB ID")
        self.tree.heading("Año", text="Año")
        self.tree.heading("Rating", text="Rating")
        
        # Configurar anchos de columna
        self.tree.column("ID", width=80)
        self.tree.column("Nombre", width=300)
        self.tree.column("Tipo", width=100)
        self.tree.column("Categoría", width=150)
        self.tree.column("TMDB", width=80)
        self.tree.column("Año", width=60)
        self.tree.column("Rating", width=60)
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(content_frame, orient="vertical", command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # Pack
        self.tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Bind doble click
        self.tree.bind("<Double-1>", self._on_item_double_click)
    
    def _create_pagination(self):
        """Crear controles de paginación"""
        pagination_frame = ctk.CTkFrame(self.main_frame)
        pagination_frame.pack(fill="x", padx=20, pady=10)
        
        # Info de página
        self.page_info_label = ctk.CTkLabel(pagination_frame, text="Página 1 de 1")
        self.page_info_label.pack(side="left", padx=10)
        
        # Botones de navegación
        self.prev_btn = ctk.CTkButton(
            pagination_frame,
            text="◀ Anterior",
            command=self._prev_page,
            state="disabled"
        )
        self.prev_btn.pack(side="right", padx=5)
        
        self.next_btn = ctk.CTkButton(
            pagination_frame,
            text="Siguiente ▶",
            command=self._next_page
        )
        self.next_btn.pack(side="right", padx=5)
        
        # Status
        self.status_label = ctk.CTkLabel(pagination_frame, text="Cargando...")
        self.status_label.pack(side="left", padx=(50, 10))
    
    def _on_filter_change(self, value):
        """Manejar cambio de filtro"""
        filter_map = {
            "Todos": "all",
            "Películas": "movies",
            "Series": "series", 
            "TV en Vivo": "live"
        }
        self.current_filter = filter_map.get(value, "all")
        self.current_page = 0
        asyncio.create_task(self._load_content())
    
    def _perform_search(self):
        """Realizar búsqueda"""
        search_term = self.search_var.get().strip()
        if search_term:
            self.current_page = 0
            asyncio.create_task(self._search_content(search_term))
        else:
            asyncio.create_task(self._load_content())
    
    def _refresh_content(self):
        """Refrescar contenido"""
        self.current_page = 0
        asyncio.create_task(self._load_content())
    
    def _prev_page(self):
        """Página anterior"""
        if self.current_page > 0:
            self.current_page -= 1
            asyncio.create_task(self._load_content())
    
    def _next_page(self):
        """Página siguiente"""
        self.current_page += 1
        asyncio.create_task(self._load_content())
    
    async def _load_content(self):
        """Cargar contenido según filtros"""
        try:
            self.status_label.configure(text="Cargando...")
            
            offset = self.current_page * self.items_per_page
            
            if self.current_filter == "movies":
                content = await self.db_manager.get_movies(self.items_per_page, offset)
            elif self.current_filter == "series":
                content = await self.db_manager.get_tv_shows(self.items_per_page, offset)
            elif self.current_filter == "live":
                content = await self.db_manager.get_live_streams(self.items_per_page, offset)
            else:  # all
                content = await self.db_manager.get_streams(self.items_per_page, offset)
            
            self.current_content = content
            self._update_content_display()
            self._update_pagination_controls()
            
            self.status_label.configure(text=f"Mostrando {len(content)} elementos")
            
        except Exception as e:
            self.logger.error(f"Error al cargar contenido: {str(e)}")
            self.status_label.configure(text=f"Error: {str(e)}")
    
    async def _search_content(self, search_term: str):
        """Buscar contenido"""
        try:
            self.status_label.configure(text="Buscando...")
            
            content = await self.db_manager.search_streams(search_term, 100)
            
            self.current_content = content
            self._update_content_display()
            
            self.status_label.configure(text=f"Encontrados {len(content)} resultados")
            
        except Exception as e:
            self.logger.error(f"Error en búsqueda: {str(e)}")
            self.status_label.configure(text=f"Error: {str(e)}")
    
    def _update_content_display(self):
        """Actualizar visualización del contenido"""
        # Limpiar tree
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # Agregar contenido
        for item in self.current_content:
            try:
                # Determinar tipo
                item_type = item.get('type', 0) if item.get('type') is not None else 0
                type_map = {1: "TV en Vivo", 2: "Película", 5: "Serie"}
                content_type = type_map.get(item_type, f"Tipo {item_type}")
                
                # Extraer información con validación
                item_id = str(item.get('id', '')) if item.get('id') is not None else ''
                name = item.get('stream_display_name') or item.get('title') or 'Sin nombre'
                category = item.get('category_name') or 'Sin categoría'
                tmdb_id = str(item.get('tmdb_id', '')) if item.get('tmdb_id') is not None else ''
                year = str(item.get('year', '')) if item.get('year') is not None else ''
                rating = str(item.get('rating', '')) if item.get('rating') is not None else ''
                
                # Truncar nombre si es muy largo
                display_name = name[:50] if name else 'Sin nombre'
                display_category = category[:20] if category else 'Sin categoría'
                
                # Agregar al tree
                self.tree.insert("", "end", values=(
                    item_id, display_name, content_type, display_category, 
                    tmdb_id, year, rating
                ))
                
            except Exception as e:
                self.logger.error(f"Error al procesar elemento {item}: {str(e)}")
                # Agregar elemento con datos mínimos
                self.tree.insert("", "end", values=(
                    "Error", "Error al cargar", "Desconocido", "Error", "", "", ""
                ))
    
    def _update_pagination_controls(self):
        """Actualizar controles de paginación"""
        # Habilitar/deshabilitar botones
        self.prev_btn.configure(state="normal" if self.current_page > 0 else "disabled")
        self.next_btn.configure(state="normal" if len(self.current_content) == self.items_per_page else "disabled")
        
        # Actualizar info de página
        self.page_info_label.configure(text=f"Página {self.current_page + 1}")
    
    def _on_item_double_click(self, event):
        """Manejar doble click en elemento"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            values = item['values']
            
            # Mostrar detalles del elemento
            self._show_item_details(values[0])  # ID del elemento
    
    def _show_item_details(self, item_id):
        """Mostrar detalles de un elemento"""
        try:
            # Buscar el elemento en el contenido actual
            item_data = None
            for item in self.current_content:
                if str(item.get('id', '')) == str(item_id):
                    item_data = item
                    break
            
            if item_data:
                # Crear ventana de detalles
                name = item_data.get('stream_display_name') or item_data.get('title') or 'Sin nombre'
                details_window = tk.Toplevel(self.parent)
                details_window.title(f"Detalles - {name}")
                details_window.geometry("600x400")
                
                # Mostrar información
                text_widget = tk.Text(details_window, wrap=tk.WORD, padx=10, pady=10)
                text_widget.pack(fill="both", expand=True)
                
                # Formatear información con validación
                details = f"ID: {item_data.get('id') or 'N/A'}\n"
                details += f"Nombre: {name}\n"
                details += f"Tipo: {item_data.get('type') or 'N/A'}\n"
                details += f"Categoría: {item_data.get('category_name') or 'N/A'}\n"
                details += f"TMDB ID: {item_data.get('tmdb_id') or 'N/A'}\n"
                details += f"Año: {item_data.get('year') or 'N/A'}\n"
                details += f"Rating: {item_data.get('rating') or 'N/A'}\n"
                details += f"URL: {item_data.get('stream_source') or 'N/A'}\n"
                
                # Propiedades adicionales
                movie_props = item_data.get('movie_properties')
                if movie_props:
                    try:
                        if isinstance(movie_props, str):
                            props = json.loads(movie_props)
                            details += f"\nPropiedades:\n{json.dumps(props, indent=2, ensure_ascii=False)}"
                        else:
                            details += f"\nPropiedades:\n{json.dumps(movie_props, indent=2, ensure_ascii=False)}"
                    except Exception as e:
                        details += f"\nPropiedades (raw): {movie_props}"
                
                text_widget.insert("1.0", details)
                text_widget.configure(state="disabled")
            else:
                messagebox.showwarning("Advertencia", "No se encontró el elemento seleccionado.")
                
        except Exception as e:
            self.logger.error(f"Error al mostrar detalles: {str(e)}")
            messagebox.showerror("Error", f"Error al mostrar detalles: {str(e)}")
    
    def destroy(self):
        """Destruir componente"""
        if hasattr(self, 'main_frame'):
            self.main_frame.destroy()
