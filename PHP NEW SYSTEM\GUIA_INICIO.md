# 🎬 IPTV XUI One Content Manager - PHP Sistema Completo

## 🚀 Inicio Rápido

### Archivos Principales de Inicio

1. **`start.php`** - Panel principal con acceso a todas las funciones
2. **`system-test.php`** - Diagnóstico completo del sistema
3. **`public/index.php`** - Aplicación principal

### 🌐 Acceso Web

Para usar el sistema en un servidor web:

```
http://tu-dominio.com/PHP NEW SYSTEM/start.php
```

### 💻 Uso Local

Para testing local con PHP:

```bash
cd "PHP NEW SYSTEM"
php -S localhost:8000
```

Luego accede a: `http://localhost:8000/start.php`

## 📁 Estructura del Proyecto

```
PHP NEW SYSTEM/
├── 🏠 start.php              # Panel de inicio principal
├── 🔬 system-test.php        # Diagnóstico del sistema
├── 📝 index.php              # Redireccionador principal
├── 
├── 📁 config/                # Configuración
│   ├── database.php          # Configuración de base de datos
│   ├── env.php               # Variables de entorno
│   ├── paths.php             # Gestión de rutas
│   └── settings.php          # Configuración general
├── 
├── 📁 core/                  # Núcleo del sistema
│   ├── SimpleDatabaseManager.php  # Gestor de DB optimizado
│   ├── DatabaseManager.php        # Gestor completo
│   ├── M3UParser.php              # Parser de archivos M3U
│   └── TMDBClient.php             # Cliente TMDB
├── 
├── 📁 public/                # Interfaz web principal
│   ├── index.php             # App principal
│   ├── pages/                # Páginas de la aplicación
│   └── assets/               # CSS, JS, imágenes
├── 
├── 📁 api/                   # Endpoints REST
│   ├── upload-m3u.php        # Subida de M3U
│   ├── export-data.php       # Exportación
│   └── detect-missing.php    # Detección de faltantes
└── 
└── 📁 logs/                  # Logs del sistema
```

## ⚙️ Configuración

### 1. Base de Datos

Edita el archivo `.env`:

```env
DB_HOST=**************
DB_PORT=3306
DB_NAME=xui
DB_USER=tu_usuario
DB_PASSWORD=tu_contraseña
```

### 2. TMDB API

Añade tu clave de API de TMDB:

```env
TMDB_API_KEY=tu_api_key_aqui
```

### 3. Configuración de Hosting

Para hosting compartido:

```env
SHARED_HOSTING=true
APP_URL=http://tu-dominio.com/PHP NEW SYSTEM
```

## 🔧 Funcionalidades Principales

### 🎬 Gestión de Contenido

- ✅ **Navegador de Contenido**: Explora películas, series y TV en vivo
- ✅ **Búsqueda Avanzada**: Filtros por tipo, calidad, género
- ✅ **Detalles TMDB**: Información automática de películas/series
- ✅ **Gestión de Categorías**: Organización del contenido

### 📺 Soporte de Streams

- ✅ **Películas (type=2)**: Gestión completa de películas
- ✅ **TV en Vivo (type=1)**: Canales de televisión
- ✅ **Series (type=5)**: Series con episodios
- ✅ **Symlinks**: Detección de enlaces simbólicos
- ✅ **Direct Source**: Archivos directos

### 📊 Analytics y Estadísticas

- ✅ **Dashboard**: Estadísticas en tiempo real
- ✅ **Calidad de Contenido**: Análisis de 4K, HDR, FPS
- ✅ **Duplicados**: Detección y gestión
- ✅ **Contenido Roto**: Identificación de streams problemáticos

### 🔄 Importación/Exportación

- ✅ **Upload M3U**: Procesamiento de listas M3U
- ✅ **Export M3U**: Generación de listas
- ✅ **Export JSON/CSV**: Múltiples formatos
- ✅ **Batch Processing**: Procesamiento en lotes

## 🛠️ Herramientas de Desarrollo

### Diagnóstico del Sistema

```bash
php system-test.php
```

o accede vía web: `system-test.php`

### Test de Base de Datos

```bash
php database-test.php
```

### Test de Rutas

```bash
php path-test.php
```

## 🔒 Seguridad

### Características de Seguridad

- ✅ **Prepared Statements**: Protección contra SQL injection
- ✅ **Timeout Controls**: Prevención de threads largos
- ✅ **Input Validation**: Validación de entrada
- ✅ **CSRF Protection**: Protección CSRF (configurable)
- ✅ **File Upload Security**: Validación de archivos M3U

### Configuración de Seguridad

```env
CSRF_PROTECTION=true
MAX_UPLOAD_SIZE=50M
SESSION_LIFETIME=3600
```

## 📱 Interfaz Responsiva

### Características UI/UX

- ✅ **Dark/Light Mode**: Modo oscuro por defecto
- ✅ **Responsive Design**: Compatible con móviles
- ✅ **Animaciones Smooth**: Transiciones fluidas
- ✅ **Progress Bars**: Progreso en tiempo real
- ✅ **Drag & Drop**: Subida de archivos intuitiva

### Tecnologías Frontend

- ✅ **CSS Grid/Flexbox**: Layout moderno
- ✅ **CSS Variables**: Theming dinámico
- ✅ **Font Awesome**: Iconografía completa
- ✅ **Chart.js**: Gráficos interactivos
- ✅ **Vanilla JS**: Sin dependencias pesadas

## 🚀 Optimizaciones de Performance

### Base de Datos

- ✅ **Connection Pooling**: Pool de conexiones
- ✅ **Query Optimization**: Consultas optimizadas
- ✅ **Timeout Management**: Gestión de timeouts
- ✅ **Batch Operations**: Operaciones en lote

### Memoria y CPU

- ✅ **Memory Management**: Gestión eficiente de memoria
- ✅ **Large File Support**: Soporte para archivos grandes (50MB+)
- ✅ **Chunked Processing**: Procesamiento por chunks
- ✅ **Smart Caching**: Cache inteligente

## 🔧 Resolución de Problemas

### Problemas Comunes

1. **Error "could not find driver"**
   - Instalar extensión php-pdo-mysql
   - Verificar php.ini

2. **Timeout en conexión**
   - Verificar configuración de red
   - Ajustar timeout en .env

3. **Archivos M3U no procesan**
   - Verificar permisos de uploads/
   - Revisar MAX_UPLOAD_SIZE

4. **Error de permisos**
   - Verificar permisos de logs/
   - Verificar permisos de uploads/

### Logs de Debugging

Los logs se guardan en:
- `logs/app.log` - Log general
- `logs/database.log` - Log de base de datos
- `logs/upload.log` - Log de subidas

## 📧 Soporte

Para problemas técnicos, revisa:
1. `system-test.php` - Diagnóstico automático
2. `logs/` - Archivos de log
3. Configuración `.env`

## 🆕 Nuevas Características

### Últimas Mejoras

- ✅ **Anti-Long Thread**: Prevención de hilos largos en MySQL
- ✅ **Smart Categories**: Categorización inteligente
- ✅ **Quality Detection**: Detección automática de calidad
- ✅ **Missing Content**: Detección de contenido faltante
- ✅ **Real-time Progress**: Progreso en tiempo real
- ✅ **Modern UI**: Interfaz completamente rediseñada

### Próximas Características

- 🔄 **Auto-update TMDB**: Actualización automática
- 🔄 **Playlist Sync**: Sincronización de listas
- 🔄 **Advanced Filtering**: Filtros avanzados
- 🔄 **Content Recommendations**: Recomendaciones
- 🔄 **API Rate Limiting**: Limitación de API
