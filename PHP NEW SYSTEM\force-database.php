<?php
/**
 * Force Database Connection - IPTV XUI One Content Manager
 * ========================================================
 * 
 * Forces connection to real database and bypasses demo mode
 */

header('Content-Type: text/html; charset=utf-8');

// Load environment
require_once __DIR__ . '/config/env.php';

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Force Database Connection - IPTV XUI Manager</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #0f172a;
            color: #f1f5f9;
            margin: 0;
            padding: 2rem;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: #1e293b;
            border-radius: 1rem;
            padding: 2rem;
            border: 1px solid #334155;
        }
        h1 {
            color: #3b82f6;
            text-align: center;
            margin-bottom: 2rem;
        }
        .status {
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
            font-weight: 600;
        }
        .status.success {
            background: rgba(16, 185, 129, 0.2);
            color: #10b981;
            border: 1px solid #10b981;
        }
        .status.error {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
            border: 1px solid #ef4444;
        }
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #3b82f6;
            color: white;
            text-decoration: none;
            border-radius: 0.5rem;
            margin: 0.5rem;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #1e40af;
        }
        .btn.success {
            background: #10b981;
        }
        .btn.success:hover {
            background: #059669;
        }
        pre {
            background: #0f172a;
            border: 1px solid #334155;
            border-radius: 0.5rem;
            padding: 1rem;
            overflow-x: auto;
            font-size: 0.875rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Force Database Connection</h1>

        <?php
        $success = false;
        $error = null;
        $pdo = null;

        // Check if PDO MySQL is available
        if (!extension_loaded('pdo_mysql')) {
            echo '<div class="status error">';
            echo '❌ <strong>PDO MySQL Extension Missing</strong><br>';
            echo 'Cannot connect to database without PDO MySQL extension.<br>';
            echo 'Please install php-mysql extension first.';
            echo '</div>';
        } else {
            echo '<div class="status success">';
            echo '✅ <strong>PDO MySQL Extension Available</strong>';
            echo '</div>';

            // Attempt direct connection
            try {
                $host = env('DB_HOST', '**************');
                $port = (int)env('DB_PORT', 3306);
                $database = env('DB_NAME', 'xui');
                $username = env('DB_USER', 'infest84');
                $password = env('DB_PASSWORD', 'GZM6hh12zHKgfg34FghtAQ84WxloPRX]SV$fP');

                echo '<h2>🔍 Connection Details</h2>';
                echo '<pre>';
                echo "Host: {$host}\n";
                echo "Port: {$port}\n";
                echo "Database: {$database}\n";
                echo "Username: {$username}\n";
                echo "Password: " . (strlen($password) > 0 ? '***' . substr($password, -4) : 'NOT SET') . "\n";
                echo '</pre>';

                $dsn = "mysql:host={$host};port={$port};dbname={$database};charset=utf8mb4";
                
                $options = [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false,
                    PDO::ATTR_TIMEOUT => 15
                ];

                echo '<h2>🚀 Attempting Connection...</h2>';
                $startTime = microtime(true);
                
                $pdo = new PDO($dsn, $username, $password, $options);
                
                $connectionTime = round((microtime(true) - $startTime) * 1000, 2);
                $success = true;

                echo '<div class="status success">';
                echo "✅ <strong>Connection Successful!</strong><br>";
                echo "Connection time: {$connectionTime}ms";
                echo '</div>';

                // Test basic queries
                echo '<h2>📊 Database Information</h2>';
                
                // Server version
                $stmt = $pdo->query("SELECT VERSION() as version");
                $version = $stmt->fetch()['version'];
                echo "<p><strong>MySQL Version:</strong> {$version}</p>";

                // Check tables
                $stmt = $pdo->query("SHOW TABLES");
                $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
                echo "<p><strong>Available Tables:</strong> " . count($tables) . "</p>";
                
                if (in_array('streams', $tables)) {
                    $stmt = $pdo->query("SELECT COUNT(*) as count FROM streams");
                    $count = $stmt->fetch()['count'];
                    echo "<p><strong>Total Streams:</strong> {$count}</p>";
                    
                    if ($count > 0) {
                        echo '<div class="status success">';
                        echo '🎉 <strong>Database has content!</strong><br>';
                        echo 'The application can now use real data instead of demo mode.';
                        echo '</div>';
                    }
                } else {
                    echo '<div class="status error">';
                    echo '⚠️ <strong>Streams table not found</strong><br>';
                    echo 'The database exists but may not be a valid XUI One database.';
                    echo '</div>';
                }

            } catch (Exception $e) {
                $error = $e->getMessage();
                echo '<div class="status error">';
                echo "❌ <strong>Connection Failed</strong><br>";
                echo htmlspecialchars($error);
                echo '</div>';

                // Provide specific troubleshooting
                if (strpos($error, 'Access denied') !== false) {
                    echo '<h2>🔧 Troubleshooting: Access Denied</h2>';
                    echo '<ul>';
                    echo '<li>Check username and password in .env file</li>';
                    echo '<li>Verify user has permission to access the database</li>';
                    echo '<li>Confirm user can connect from this IP address</li>';
                    echo '</ul>';
                } elseif (strpos($error, 'Connection refused') !== false || strpos($error, 'timed out') !== false) {
                    echo '<h2>🔧 Troubleshooting: Connection Issues</h2>';
                    echo '<ul>';
                    echo '<li>Check if MySQL server is running</li>';
                    echo '<li>Verify host and port are correct</li>';
                    echo '<li>Check firewall settings for port 3306</li>';
                    echo '<li>Confirm server allows remote connections</li>';
                    echo '</ul>';
                } elseif (strpos($error, 'Unknown database') !== false) {
                    echo '<h2>🔧 Troubleshooting: Database Not Found</h2>';
                    echo '<ul>';
                    echo '<li>Check database name in .env file</li>';
                    echo '<li>Verify database exists on the server</li>';
                    echo '<li>Confirm user has access to this database</li>';
                    echo '</ul>';
                }
            }
        }

        // Action buttons
        echo '<h2>🎯 Next Steps</h2>';
        
        if ($success) {
            echo '<a href="public/index.php" class="btn success">🚀 Launch with Real Data</a>';
            echo '<a href="test-database.php" class="btn">🔍 Run Full Database Test</a>';
        } else {
            echo '<a href="public/index.php" class="btn">📱 Continue with Demo Mode</a>';
            echo '<a href="check-requirements.php" class="btn">🔧 Check System Requirements</a>';
            echo '<a href="test-database.php" class="btn">🔍 Run Database Test</a>';
        }
        ?>

        <h2>📝 Manual Configuration</h2>
        <p>If automatic connection fails, you can manually edit the database configuration:</p>
        <ol>
            <li>Edit <code>.env</code> file in the root directory</li>
            <li>Update database credentials:
                <pre>DB_HOST=your_host
DB_PORT=3306
DB_NAME=your_database
DB_USER=your_username
DB_PASSWORD=your_password</pre>
            </li>
            <li>Save the file and refresh this page</li>
        </ol>
    </div>
</body>
</html>
