"""
Sidebar de navegación
====================

Componente de navegación lateral con iconos y animaciones.
"""

import tkinter as tk
import customtkinter as ctk
from typing import Callable, Dict, Any, Optional
from PIL import Image, ImageTk
import logging

class Sidebar:
    """Sidebar de navegación con iconos y animaciones"""
    
    def __init__(self, parent, settings, on_panel_change: Callable[[str], None]):
        self.parent = parent
        self.settings = settings
        self.on_panel_change = on_panel_change
        self.logger = logging.getLogger("iptv_manager.sidebar")
        
        # Estado
        self.active_panel = "dashboard"
        self.buttons = {}
        
        # Configurar componentes
        self._setup_header()
        self._setup_navigation()
        self._setup_footer()
    
    def _setup_header(self):
        """Configurar header del sidebar"""
        # Logo/título
        self.header_frame = ctk.CTkFrame(self.parent, height=80, corner_radius=0)
        self.header_frame.pack(fill="x", padx=0, pady=0)
        self.header_frame.pack_propagate(False)
        
        # Logo
        self.logo_label = ctk.CTkLabel(
            self.header_frame,
            text="IPTV Manager",
            font=("Arial", 18, "bold"),
            text_color=self.settings.colors["accent"]
        )
        self.logo_label.pack(pady=20)
        
        # Separador
        separator = ctk.CTkFrame(self.parent, height=1, corner_radius=0)
        separator.pack(fill="x", padx=10, pady=5)
    
    def _setup_navigation(self):
        """Configurar botones de navegación"""
        # Frame de navegación
        self.nav_frame = ctk.CTkFrame(self.parent, corner_radius=0)
        self.nav_frame.pack(fill="both", expand=True, padx=0, pady=0)
        
        # Configurar botones
        nav_items = [
            ("dashboard", "📊", "Panel Principal"),
            ("content_browser", "📺", "Contenido XUI"),
            ("m3u_tmdb_manager", "🎯", "M3U → TMDB"),
            ("m3u_manager", "📁", "Gestor M3U"),
            ("tmdb_browser", "🎬", "Explorador TMDB"),
            ("analytics", "📈", "Análisis"),
            ("settings", "⚙️", "Configuración")
        ]
        
        for panel_id, icon, title in nav_items:
            self._create_nav_button(panel_id, icon, title)
    
    def _create_nav_button(self, panel_id: str, icon: str, title: str):
        """Crear botón de navegación"""
        # Frame del botón
        button_frame = ctk.CTkFrame(self.nav_frame, corner_radius=0)
        button_frame.pack(fill="x", padx=5, pady=2)
        
        # Botón
        button = ctk.CTkButton(
            button_frame,
            text=f"{icon}  {title}",
            font=("Arial", 12),
            height=40,
            corner_radius=8,
            anchor="w",
            command=lambda: self._on_button_click(panel_id)
        )
        button.pack(fill="x", padx=5, pady=2)
        
        # Guardar referencia
        self.buttons[panel_id] = button
        
        # Configurar estilo inicial
        self._update_button_style(panel_id)
    
    def _on_button_click(self, panel_id: str):
        """Manejar clic en botón"""
        if panel_id != self.active_panel:
            self.set_active_panel(panel_id)
            self.on_panel_change(panel_id)
    
    def set_active_panel(self, panel_id: str):
        """Establecer panel activo"""
        old_panel = self.active_panel
        self.active_panel = panel_id
        
        # Actualizar estilos
        self._update_button_style(old_panel)
        self._update_button_style(panel_id)
    
    def _update_button_style(self, panel_id: str):
        """Actualizar estilo del botón"""
        if panel_id in self.buttons:
            button = self.buttons[panel_id]
            
            if panel_id == self.active_panel:
                # Botón activo
                button.configure(
                    fg_color=self.settings.colors["accent"],
                    hover_color=self.settings.colors["accent"],
                    text_color=self.settings.colors["text_primary"]
                )
            else:
                # Botón inactivo
                button.configure(
                    fg_color="transparent",
                    hover_color=self.settings.colors["bg_tertiary"],
                    text_color=self.settings.colors["text_secondary"]
                )
    
    def _setup_footer(self):
        """Configurar footer del sidebar"""
        # Frame del footer
        self.footer_frame = ctk.CTkFrame(self.parent, height=60, corner_radius=0)
        self.footer_frame.pack(side="bottom", fill="x", padx=0, pady=0)
        self.footer_frame.pack_propagate(False)
        
        # Información de versión
        version_label = ctk.CTkLabel(
            self.footer_frame,
            text=f"v{self.settings.app_version}",
            font=("Arial", 10),
            text_color=self.settings.colors["text_secondary"]
        )
        version_label.pack(pady=10)
        
        # Estado de conexión
        self.connection_status = ctk.CTkLabel(
            self.footer_frame,
            text="● Conectado",
            font=("Arial", 10),
            text_color=self.settings.colors["success"]
        )
        self.connection_status.pack(pady=5)
    
    def update_connection_status(self, connected: bool):
        """Actualizar estado de conexión"""
        if connected:
            self.connection_status.configure(
                text="● Conectado",
                text_color=self.settings.colors["success"]
            )
        else:
            self.connection_status.configure(
                text="● Desconectado",
                text_color=self.settings.colors["error"]
            )
