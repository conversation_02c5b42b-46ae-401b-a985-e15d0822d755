<?php
/**
 * Sistema IPTV XUI Manager - VERSIÓN LOCAL
 * ========================================
 * 
 * Sistema completo funcionando con base de datos SQLite local
 */

// Incluir el gestor de base de datos local
require_once __DIR__ . '/core/LocalDatabaseManager.php';

// Inicializar base de datos local
$db_error = null;
$db_info = null;

try {
    $db = new LocalDatabaseManager();
    
    if (!$db->testConnection()) {
        throw new Exception("No se pudo conectar a la base de datos local");
    }
    
    $db_info = $db->getDatabaseInfo();
    
    // Obtener estadísticas reales
    $contentStats = $db->getContentStats();
    $qualityStats = $db->getQualityStats();
    $recentContent = $db->getRecentAdditions(5);
    $popularContent = $db->getPopularContent(5);
    $categories = $db->getCategories();
    
} catch (Exception $e) {
    $db_error = $e->getMessage();
    $contentStats = [];
    $qualityStats = [];
    $recentContent = [];
    $popularContent = [];
    $categories = [];
}

// Manejar acciones AJAX
if (isset($_GET['action']) && $_GET['action'] === 'api') {
    header('Content-Type: application/json');
    
    try {
        switch ($_GET['endpoint'] ?? '') {
            case 'stats':
                echo json_encode([
                    'success' => true,
                    'data' => [
                        'content' => $contentStats,
                        'quality' => $qualityStats
                    ]
                ]);
                break;
                
            case 'recent':
                echo json_encode([
                    'success' => true,
                    'data' => $recentContent
                ]);
                break;
                
            case 'search':
                $term = $_GET['term'] ?? '';
                $results = $db->searchContent($term, 20);
                echo json_encode([
                    'success' => true,
                    'data' => $results
                ]);
                break;
                
            case 'categories':
                echo json_encode([
                    'success' => true,
                    'data' => $categories
                ]);
                break;
                
            case '4k-content':
                $content = $db->get4KContent(10);
                echo json_encode([
                    'success' => true,
                    'data' => $content
                ]);
                break;
                
            default:
                echo json_encode(['success' => false, 'message' => 'Endpoint no encontrado']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
    exit;
}
?>
<!DOCTYPE html>
<html lang="es" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IPTV XUI Manager - Sistema Local</title>
    
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        :root {
            --primary-color: #3b82f6;
            --secondary-color: #1e40af;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --dark-bg: #0f172a;
            --dark-surface: #1e293b;
            --dark-border: #334155;
            --text-primary: #f1f5f9;
            --text-secondary: #94a3b8;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * { margin: 0; padding: 0; box-sizing: border-box; }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--dark-bg);
            color: var(--text-primary);
            line-height: 1.6;
        }

        .app-container {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            background: var(--dark-surface);
            border-right: 1px solid var(--dark-border);
            padding: 2rem 0;
            width: 280px;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 0 2rem 2rem;
            border-bottom: 1px solid var(--dark-border);
            margin-bottom: 2rem;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 1rem;
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .logo i { font-size: 2rem; }

        .nav-menu {
            list-style: none;
            padding: 0 1rem;
        }

        .nav-item { margin-bottom: 0.5rem; }

        .nav-link {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: 0.75rem;
            transition: var(--transition);
            cursor: pointer;
        }

        .nav-link:hover, .nav-link.active {
            background: var(--primary-color);
            color: white;
            transform: translateX(4px);
        }

        .main-content {
            flex: 1;
            margin-left: 280px;
            padding: 2rem;
            min-height: 100vh;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding: 1.5rem 2rem;
            background: var(--dark-surface);
            border-radius: 1rem;
            border: 1px solid var(--dark-border);
        }

        .header-title {
            font-size: 2rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            font-weight: 600;
            font-size: 0.875rem;
        }

        .status-success {
            background: rgba(16, 185, 129, 0.2);
            color: var(--success-color);
            border: 1px solid var(--success-color);
        }

        .status-error {
            background: rgba(239, 68, 68, 0.2);
            color: var(--error-color);
            border: 1px solid var(--error-color);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--dark-surface);
            border: 1px solid var(--dark-border);
            border-radius: 1rem;
            padding: 2rem;
            text-align: center;
            transition: var(--transition);
        }

        .stat-card:hover {
            border-color: var(--primary-color);
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
        }

        .stat-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: var(--primary-color);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 1rem;
            font-weight: 500;
        }

        .content-section {
            background: var(--dark-surface);
            border: 1px solid var(--dark-border);
            border-radius: 1rem;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            color: var(--primary-color);
        }

        .content-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .content-list {
            list-style: none;
        }

        .content-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid var(--dark-border);
            transition: var(--transition);
        }

        .content-item:hover {
            background: rgba(59, 130, 246, 0.1);
        }

        .content-item:last-child {
            border-bottom: none;
        }

        .content-name {
            font-weight: 500;
            color: var(--text-primary);
        }

        .content-meta {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .search-box {
            width: 100%;
            padding: 1rem;
            background: var(--dark-bg);
            border: 1px solid var(--dark-border);
            border-radius: 0.5rem;
            color: var(--text-primary);
            font-size: 1rem;
            margin-bottom: 1rem;
        }

        .search-box:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 0.5rem;
            font-weight: 500;
            text-decoration: none;
            transition: var(--transition);
            cursor: pointer;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: var(--secondary-color);
            transform: translateY(-2px);
        }

        .chart-container {
            position: relative;
            height: 300px;
            margin-top: 1rem;
        }

        .tab-container {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .tab {
            padding: 0.75rem 1.5rem;
            background: var(--dark-bg);
            border: 1px solid var(--dark-border);
            border-radius: 0.5rem;
            color: var(--text-secondary);
            cursor: pointer;
            transition: var(--transition);
        }

        .tab.active, .tab:hover {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-tv"></i>
                    <span>IPTV Manager Local</span>
                </div>
            </div>
            
            <nav>
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="#" class="nav-link active" data-tab="dashboard">
                            <i class="fas fa-chart-line"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" data-tab="content">
                            <i class="fas fa-film"></i>
                            <span>Contenido</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" data-tab="quality">
                            <i class="fas fa-star"></i>
                            <span>Calidad</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" data-tab="search">
                            <i class="fas fa-search"></i>
                            <span>Buscar</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" data-tab="database">
                            <i class="fas fa-database"></i>
                            <span>Base de Datos</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="header">
                <div>
                    <h1 class="header-title">IPTV XUI Manager</h1>
                </div>
                <div>
                    <?php if (!$db_error): ?>
                        <div class="status-badge status-success">
                            <i class="fas fa-check-circle"></i>
                            Base de Datos Local Activa
                        </div>
                    <?php else: ?>
                        <div class="status-badge status-error">
                            <i class="fas fa-exclamation-triangle"></i>
                            Error de Base de Datos
                        </div>
                    <?php endif; ?>
                </div>
            </header>

            <?php if ($db_error): ?>
            <div class="content-section">
                <div class="section-title">
                    <i class="fas fa-exclamation-triangle"></i>
                    Error de Base de Datos
                </div>
                <p style="color: var(--error-color); margin-bottom: 1rem;">
                    <strong>Error:</strong> <?= htmlspecialchars($db_error) ?>
                </p>
                <a href="create-local-database.php" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    Crear Base de Datos Local
                </a>
            </div>
            <?php else: ?>

            <!-- Dashboard Tab -->
            <div id="dashboard" class="tab-content active">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-film"></i></div>
                        <div class="stat-number"><?= number_format($contentStats['movies'] ?? 0) ?></div>
                        <div class="stat-label">Películas</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-tv"></i></div>
                        <div class="stat-number"><?= number_format($contentStats['series'] ?? 0) ?></div>
                        <div class="stat-label">Series</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-broadcast-tower"></i></div>
                        <div class="stat-number"><?= number_format($contentStats['live_tv'] ?? 0) ?></div>
                        <div class="stat-label">TV en Vivo</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-hdd"></i></div>
                        <div class="stat-number"><?= number_format($qualityStats['content_4k'] ?? 0) ?></div>
                        <div class="stat-label">Contenido 4K</div>
                    </div>
                </div>

                <div class="content-grid">
                    <div class="content-section">
                        <div class="section-title">
                            <i class="fas fa-clock"></i>
                            Contenido Reciente
                        </div>
                        <ul class="content-list">
                            <?php foreach ($recentContent as $item): ?>
                            <li class="content-item">
                                <div>
                                    <div class="content-name"><?= htmlspecialchars($item['stream_display_name']) ?></div>
                                    <div class="content-meta">Agregado: <?= date('d/m/Y', $item['added']) ?></div>
                                </div>
                            </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>

                    <div class="content-section">
                        <div class="section-title">
                            <i class="fas fa-star"></i>
                            Contenido Popular
                        </div>
                        <ul class="content-list">
                            <?php foreach ($popularContent as $item): ?>
                            <li class="content-item">
                                <div>
                                    <div class="content-name"><?= htmlspecialchars($item['stream_display_name']) ?></div>
                                    <div class="content-meta">Rating: <?= $item['rating'] ?>/10</div>
                                </div>
                            </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                </div>

                <div class="content-section">
                    <div class="section-title">
                        <i class="fas fa-chart-pie"></i>
                        Distribución de Calidad
                    </div>
                    <div class="chart-container">
                        <canvas id="qualityChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Content Tab -->
            <div id="content" class="tab-content">
                <div class="content-section">
                    <div class="section-title">
                        <i class="fas fa-film"></i>
                        Gestión de Contenido
                    </div>
                    <p>Funcionalidad de gestión de contenido en desarrollo...</p>
                </div>
            </div>

            <!-- Quality Tab -->
            <div id="quality" class="tab-content">
                <div class="content-section">
                    <div class="section-title">
                        <i class="fas fa-star"></i>
                        Análisis de Calidad
                    </div>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon"><i class="fas fa-gem"></i></div>
                            <div class="stat-number"><?= number_format($qualityStats['content_4k'] ?? 0) ?></div>
                            <div class="stat-label">4K Ultra HD</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon"><i class="fas fa-tachometer-alt"></i></div>
                            <div class="stat-number"><?= number_format($qualityStats['content_60fps'] ?? 0) ?></div>
                            <div class="stat-label">60fps</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon"><i class="fas fa-sun"></i></div>
                            <div class="stat-number"><?= number_format($qualityStats['content_hdr'] ?? 0) ?></div>
                            <div class="stat-label">HDR</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon"><i class="fas fa-tv"></i></div>
                            <div class="stat-number"><?= number_format($qualityStats['content_fhd'] ?? 0) ?></div>
                            <div class="stat-label">Full HD</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Search Tab -->
            <div id="search" class="tab-content">
                <div class="content-section">
                    <div class="section-title">
                        <i class="fas fa-search"></i>
                        Buscar Contenido
                    </div>
                    <input type="text" class="search-box" id="searchInput" placeholder="Buscar películas, series, canales...">
                    <div id="searchResults"></div>
                </div>
            </div>

            <!-- Database Tab -->
            <div id="database" class="tab-content">
                <div class="content-section">
                    <div class="section-title">
                        <i class="fas fa-database"></i>
                        Información de Base de Datos
                    </div>
                    <?php if ($db_info): ?>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon"><i class="fas fa-server"></i></div>
                            <div class="stat-number"><?= $db_info['type'] ?></div>
                            <div class="stat-label">Tipo de BD</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon"><i class="fas fa-hdd"></i></div>
                            <div class="stat-number"><?= round($db_info['size'] / 1024, 2) ?> KB</div>
                            <div class="stat-label">Tamaño</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon"><i class="fas fa-check-circle"></i></div>
                            <div class="stat-number"><?= $db_info['writable'] ? 'Sí' : 'No' ?></div>
                            <div class="stat-label">Escribible</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon"><i class="fas fa-link"></i></div>
                            <div class="stat-number">Local</div>
                            <div class="stat-label">Conexión</div>
                        </div>
                    </div>
                    <p style="margin-top: 1rem; color: var(--text-secondary);">
                        <strong>Ubicación:</strong> <?= htmlspecialchars($db_info['path']) ?>
                    </p>
                    <?php endif; ?>
                </div>
            </div>

            <?php endif; ?>
        </main>
    </div>

    <script>
        // Tab functionality
        document.addEventListener('DOMContentLoaded', function() {
            const navLinks = document.querySelectorAll('.nav-link');
            const tabContents = document.querySelectorAll('.tab-content');

            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // Remove active class from all links and contents
                    navLinks.forEach(l => l.classList.remove('active'));
                    tabContents.forEach(t => t.classList.remove('active'));
                    
                    // Add active class to clicked link
                    this.classList.add('active');
                    
                    // Show corresponding tab content
                    const tabId = this.getAttribute('data-tab');
                    document.getElementById(tabId).classList.add('active');
                });
            });

            // Initialize quality chart
            <?php if (!$db_error && !empty($qualityStats)): ?>
            const ctx = document.getElementById('qualityChart').getContext('2d');
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['4K', '60fps', 'HDR', 'FHD', 'HD'],
                    datasets: [{
                        data: [
                            <?= $qualityStats['content_4k'] ?? 0 ?>,
                            <?= $qualityStats['content_60fps'] ?? 0 ?>,
                            <?= $qualityStats['content_hdr'] ?? 0 ?>,
                            <?= $qualityStats['content_fhd'] ?? 0 ?>,
                            <?= $qualityStats['content_hd'] ?? 0 ?>
                        ],
                        backgroundColor: [
                            '#3b82f6',
                            '#10b981',
                            '#f59e0b',
                            '#8b5cf6',
                            '#ef4444'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: {
                                color: '#f1f5f9'
                            }
                        }
                    }
                }
            });
            <?php endif; ?>

            // Search functionality
            const searchInput = document.getElementById('searchInput');
            const searchResults = document.getElementById('searchResults');

            if (searchInput) {
                searchInput.addEventListener('input', function() {
                    const term = this.value.trim();
                    
                    if (term.length < 2) {
                        searchResults.innerHTML = '';
                        return;
                    }

                    fetch(`?action=api&endpoint=search&term=${encodeURIComponent(term)}`)
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                displaySearchResults(data.data);
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                        });
                });
            }

            function displaySearchResults(results) {
                if (results.length === 0) {
                    searchResults.innerHTML = '<p style="color: var(--text-secondary);">No se encontraron resultados.</p>';
                    return;
                }

                const html = results.map(item => `
                    <div class="content-item">
                        <div>
                            <div class="content-name">${item.stream_display_name}</div>
                            <div class="content-meta">Año: ${item.year || 'N/A'} | Rating: ${item.rating || 'N/A'}</div>
                        </div>
                    </div>
                `).join('');

                searchResults.innerHTML = `<ul class="content-list">${html}</ul>`;
            }
        });
    </script>
</body>
</html>
