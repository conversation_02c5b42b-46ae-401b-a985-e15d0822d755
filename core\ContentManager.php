<?php
/**
 * Content Manager for IPTV XUI One Content Manager
 * ===============================================
 * 
 * Orchestrates content management operations including M3U processing,
 * TMDB enrichment, duplicate detection, and export functionality.
 */

require_once __DIR__ . '/DatabaseManager.php';
require_once __DIR__ . '/M3UParser.php';
require_once __DIR__ . '/TMDBClient.php';
require_once __DIR__ . '/../config/settings.php';

class ContentManager {
    private $db;
    private $m3uParser;
    private $tmdbClient;
    private $logger;
    private $stats = [
        'processed_items' => 0,
        'enriched_items' => 0,
        'duplicates_found' => 0,
        'errors' => 0
    ];

    public function __construct() {
        $this->db = new DatabaseManager();
        $this->m3uParser = new M3UParser();
        $this->tmdbClient = new TMDBClient();
        $this->logger = $this->initializeLogger();
    }

    /**
     * Process M3U file and import content
     */
    public function processM3UFile($filePath, $options = []) {
        $this->resetStats();
        $startTime = microtime(true);

        try {
            $this->log('info', "Starting M3U processing", ['file' => basename($filePath)]);

            // Parse M3U file
            $parseResult = $this->m3uParser->parseFile($filePath, function($progress, $current, $total) use ($options) {
                if (isset($options['progress_callback'])) {
                    call_user_func($options['progress_callback'], 'parsing', $progress, $current, $total);
                }
            });

            $entries = $parseResult['entries'];
            $this->log('info', "M3U parsing completed", ['entries' => count($entries)]);

            // Process entries in batches
            $batchSize = $options['batch_size'] ?? M3U_CONFIG['batch_size'];
            $batches = array_chunk($entries, $batchSize);
            $totalBatches = count($batches);

            $this->db->beginTransaction();

            try {
                foreach ($batches as $batchIndex => $batch) {
                    $this->processBatch($batch, $options);
                    
                    // Progress callback
                    if (isset($options['progress_callback'])) {
                        $progress = (($batchIndex + 1) / $totalBatches) * 100;
                        call_user_func($options['progress_callback'], 'importing', $progress, $batchIndex + 1, $totalBatches);
                    }
                }

                $this->db->commit();
                
            } catch (Exception $e) {
                $this->db->rollback();
                throw $e;
            }

            $processingTime = microtime(true) - $startTime;
            
            $result = [
                'success' => true,
                'stats' => array_merge($this->stats, [
                    'processing_time' => $processingTime,
                    'parse_stats' => $parseResult['stats']
                ]),
                'message' => "Successfully processed {$this->stats['processed_items']} items"
            ];

            $this->log('info', "M3U processing completed successfully", $result['stats']);
            return $result;

        } catch (Exception $e) {
            $this->log('error', "M3U processing failed: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'stats' => $this->stats
            ];
        }
    }

    /**
     * Process a batch of entries
     */
    private function processBatch($entries, $options = []) {
        foreach ($entries as $entry) {
            try {
                // Skip if content already exists (optional)
                if ($options['skip_duplicates'] ?? false) {
                    if ($this->contentExists($entry)) {
                        continue;
                    }
                }

                // Enrich with TMDB data if enabled
                if ($options['enrich_tmdb'] ?? true) {
                    $entry = $this->tmdbClient->enrichContent($entry);
                    if (isset($entry['tmdb_id'])) {
                        $this->stats['enriched_items']++;
                    }
                }

                // Convert to database format
                $streamData = $this->convertToStreamData($entry);

                // Insert into database
                $this->db->insertStreamFromM3U($streamData);
                $this->stats['processed_items']++;

            } catch (Exception $e) {
                $this->stats['errors']++;
                $this->log('warning', "Failed to process entry: " . $e->getMessage(), [
                    'title' => $entry['title'] ?? 'unknown'
                ]);
            }
        }
    }

    /**
     * Check if content already exists
     */
    private function contentExists($entry) {
        $existing = $this->db->searchContent($entry['clean_title'] ?? $entry['title'], 1, 0);
        return !empty($existing);
    }

    /**
     * Convert M3U entry to stream data format
     */
    private function convertToStreamData($entry) {
        return [
            'type' => $this->getStreamType($entry['content_type']),
            'category_id' => json_encode([]), // Will be set based on content type
            'stream_display_name' => $entry['title'],
            'stream_source' => json_encode([$entry['url']]),
            'stream_icon' => $entry['logo'] ?? '',
            'tmdb_id' => $entry['tmdb_id'] ?? null,
            'year' => $entry['year'] ?? null,
            'rating' => $entry['rating'] ?? 0,
            'movie_properties' => json_encode($this->createMovieProperties($entry)),
            'added' => time(),
            'updated' => date('Y-m-d H:i:s'),
            'movie_symlink' => 0, // New content is direct_source by default
            'direct_source' => 1
        ];
    }

    /**
     * Get stream type ID
     */
    private function getStreamType($contentType) {
        switch ($contentType) {
            case 'live_tv': return 1;
            case 'movie': return 2;
            case 'tv_show': return 3;
            default: return 2;
        }
    }

    /**
     * Create movie properties JSON
     */
    private function createMovieProperties($entry) {
        $properties = [];

        if (isset($entry['tmdb_details'])) {
            $tmdb = $entry['tmdb_details'];
            $properties = [
                'tmdb_id' => $tmdb['id'] ?? null,
                'overview' => $tmdb['overview'] ?? '',
                'poster_path' => $tmdb['poster_path'] ?? '',
                'backdrop_path' => $tmdb['backdrop_path'] ?? '',
                'genres' => $tmdb['genres'] ?? [],
                'runtime' => $tmdb['runtime'] ?? null,
                'budget' => $tmdb['budget'] ?? 0,
                'revenue' => $tmdb['revenue'] ?? 0,
                'production_companies' => $tmdb['production_companies'] ?? [],
                'production_countries' => $tmdb['production_countries'] ?? [],
                'spoken_languages' => $tmdb['spoken_languages'] ?? []
            ];
        }

        // Add quality information
        if (!empty($entry['quality'])) {
            $properties['quality'] = $entry['quality'];
        }

        // Add language
        if (!empty($entry['language'])) {
            $properties['language'] = $entry['language'];
        }

        return $properties;
    }

    /**
     * Detect missing TMDB content and enrich
     */
    public function enrichMissingTMDBContent($limit = 100, $progressCallback = null) {
        $this->resetStats();

        try {
            $missingContent = $this->db->getMissingTMDBContent($limit, 0);
            $total = count($missingContent);

            $this->log('info', "Starting TMDB enrichment", ['items' => $total]);

            foreach ($missingContent as $index => $content) {
                try {
                    // Clean title for better matching
                    $cleanTitle = clean_title_for_tmdb($content['stream_display_name']);
                    
                    // Find TMDB match
                    $match = $this->tmdbClient->findBestMatch($cleanTitle, null, 'movie');
                    
                    if ($match) {
                        // Get detailed information
                        $details = $this->tmdbClient->getMovieDetails($match['id']);
                        
                        if ($details) {
                            // Update database
                            $tmdbData = [
                                'tmdb_id' => $details['id'],
                                'year' => substr($details['release_date'] ?? '', 0, 4),
                                'rating' => $details['vote_average'] ?? 0,
                                'movie_properties' => $details
                            ];
                            
                            $this->db->updateStreamTMDB($content['id'], $tmdbData);
                            $this->stats['enriched_items']++;
                        }
                    }

                    $this->stats['processed_items']++;

                    // Progress callback
                    if ($progressCallback) {
                        $progress = (($index + 1) / $total) * 100;
                        call_user_func($progressCallback, $progress, $index + 1, $total);
                    }

                } catch (Exception $e) {
                    $this->stats['errors']++;
                    $this->log('warning', "Failed to enrich content: " . $e->getMessage(), [
                        'id' => $content['id'],
                        'title' => $content['stream_display_name']
                    ]);
                }

                // Rate limiting
                usleep(250000); // 250ms delay between requests
            }

            $result = [
                'success' => true,
                'stats' => $this->stats,
                'message' => "Enriched {$this->stats['enriched_items']} out of {$this->stats['processed_items']} items"
            ];

            $this->log('info', "TMDB enrichment completed", $result['stats']);
            return $result;

        } catch (Exception $e) {
            $this->log('error', "TMDB enrichment failed: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'stats' => $this->stats
            ];
        }
    }

    /**
     * Analyze and detect duplicates
     */
    public function analyzeDuplicates($limit = 100, $offset = 0) {
        try {
            $duplicates = $this->db->getDuplicateAnalysis($limit, $offset);
            
            // Enhance duplicate analysis
            foreach ($duplicates as &$duplicate) {
                $streamIds = explode(',', $duplicate['stream_ids']);
                $duplicate['streams'] = [];
                
                foreach ($streamIds as $streamId) {
                    $stream = $this->db->query("SELECT * FROM streams WHERE id = ?", [$streamId]);
                    if (!empty($stream)) {
                        $duplicate['streams'][] = $stream[0];
                    }
                }
                
                // Analyze quality and recommend action
                $duplicate['recommendation'] = $this->analyzeDuplicateRecommendation($duplicate['streams']);
            }

            return [
                'success' => true,
                'duplicates' => $duplicates,
                'total_groups' => count($duplicates)
            ];

        } catch (Exception $e) {
            $this->log('error', "Duplicate analysis failed: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Analyze duplicate recommendation
     */
    private function analyzeDuplicateRecommendation($streams) {
        $recommendation = [
            'action' => 'manual_review',
            'keep_stream_id' => null,
            'delete_stream_ids' => [],
            'reason' => ''
        ];

        // Priority: 4K > 60fps > HDR > FHD > HD > SD
        $priorities = [];
        
        foreach ($streams as $stream) {
            $title = $stream['stream_display_name'];
            $priority = 100; // Base priority
            
            // Quality bonuses
            if (stripos($title, '4K') !== false || stripos($title, '2160p') !== false) {
                $priority += 50;
            } elseif (stripos($title, '60fps') !== false) {
                $priority += 40;
            } elseif (stripos($title, 'HDR') !== false) {
                $priority += 35;
            } elseif (stripos($title, '1080p') !== false || stripos($title, 'FHD') !== false) {
                $priority += 30;
            } elseif (stripos($title, '720p') !== false || stripos($title, 'HD') !== false) {
                $priority += 20;
            }
            
            // Symlink bonus (protected content)
            if ($stream['movie_symlink'] == 1) {
                $priority += 100;
            }
            
            $priorities[$stream['id']] = $priority;
        }

        // Sort by priority
        arsort($priorities);
        $sortedIds = array_keys($priorities);
        
        $recommendation['keep_stream_id'] = $sortedIds[0];
        $recommendation['delete_stream_ids'] = array_slice($sortedIds, 1);
        $recommendation['reason'] = 'Keep highest quality version';

        return $recommendation;
    }

    /**
     * Export content to various formats
     */
    public function exportContent($format, $filters = [], $options = []) {
        try {
            // Get content based on filters
            $content = $this->getFilteredContent($filters);
            
            switch ($format) {
                case 'm3u':
                    return $this->exportToM3U($content, $options);
                case 'txt':
                    return $this->exportToTXT($content, $options);
                case 'json':
                    return $this->exportToJSON($content, $options);
                case 'csv':
                    return $this->exportToCSV($content, $options);
                default:
                    throw new Exception("Unsupported export format: {$format}");
            }

        } catch (Exception $e) {
            $this->log('error', "Export failed: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get filtered content for export
     */
    private function getFilteredContent($filters) {
        $limit = $filters['limit'] ?? 1000;
        $offset = $filters['offset'] ?? 0;

        if (isset($filters['missing_tmdb']) && $filters['missing_tmdb']) {
            return $this->db->getMissingTMDBContent($limit, $offset);
        } elseif (isset($filters['4k_content']) && $filters['4k_content']) {
            return $this->db->get4KContent($limit, $offset);
        } elseif (isset($filters['60fps_content']) && $filters['60fps_content']) {
            return $this->db->get60FpsContent($limit, $offset);
        } elseif (isset($filters['symlink_content']) && $filters['symlink_content']) {
            return $this->db->getSymlinkContent($limit, $offset);
        } else {
            return $this->db->getMovies($limit, $offset, $filters);
        }
    }

    /**
     * Export to M3U format
     */
    private function exportToM3U($content, $options = []) {
        $m3u = "#EXTM3U\n";
        
        foreach ($content as $item) {
            $title = $item['stream_display_name'];
            $url = json_decode($item['stream_source'], true)[0] ?? '';
            $logo = $item['stream_icon'] ?? '';
            
            $extinf = "#EXTINF:-1";
            if ($logo) {
                $extinf .= " tvg-logo=\"{$logo}\"";
            }
            $extinf .= ",{$title}\n";
            
            $m3u .= $extinf . $url . "\n";
        }

        return [
            'success' => true,
            'content' => $m3u,
            'filename' => 'export_' . date('Y-m-d_H-i-s') . '.m3u',
            'mime_type' => 'audio/x-mpegurl'
        ];
    }

    /**
     * Export to TXT format
     */
    private function exportToTXT($content, $options = []) {
        $txt = '';
        
        foreach ($content as $item) {
            $txt .= $item['stream_display_name'] . "\n";
        }

        return [
            'success' => true,
            'content' => $txt,
            'filename' => 'export_' . date('Y-m-d_H-i-s') . '.txt',
            'mime_type' => 'text/plain'
        ];
    }

    /**
     * Export to JSON format
     */
    private function exportToJSON($content, $options = []) {
        $json = json_encode($content, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

        return [
            'success' => true,
            'content' => $json,
            'filename' => 'export_' . date('Y-m-d_H-i-s') . '.json',
            'mime_type' => 'application/json'
        ];
    }

    /**
     * Export to CSV format
     */
    private function exportToCSV($content, $options = []) {
        $csv = "ID,Title,Type,Category,TMDB ID,Year,Rating,Added\n";
        
        foreach ($content as $item) {
            $csv .= sprintf(
                "%d,\"%s\",%d,\"%s\",%s,%s,%.1f,%s\n",
                $item['id'],
                str_replace('"', '""', $item['stream_display_name']),
                $item['type'],
                $item['category_name'] ?? '',
                $item['tmdb_id'] ?? '',
                $item['year'] ?? '',
                $item['rating'] ?? 0,
                date('Y-m-d', $item['added'] ?? time())
            );
        }

        return [
            'success' => true,
            'content' => $csv,
            'filename' => 'export_' . date('Y-m-d_H-i-s') . '.csv',
            'mime_type' => 'text/csv'
        ];
    }

    /**
     * Reset statistics
     */
    private function resetStats() {
        $this->stats = [
            'processed_items' => 0,
            'enriched_items' => 0,
            'duplicates_found' => 0,
            'errors' => 0
        ];
    }

    /**
     * Get processing statistics
     */
    public function getStats() {
        return $this->stats;
    }

    /**
     * Initialize logger
     */
    private function initializeLogger() {
        return new class {
            public function log($level, $message, $context = []) {
                if (LOGGING_CONFIG['enabled']) {
                    $logEntry = sprintf(
                        "[%s] ContentManager %s: %s %s\n",
                        date('Y-m-d H:i:s'),
                        strtoupper($level),
                        $message,
                        !empty($context) ? json_encode($context) : ''
                    );
                    file_put_contents(LOGGING_CONFIG['file_path'], $logEntry, FILE_APPEND | LOCK_EX);
                }
            }
        };
    }

    private function log($level, $message, $context = []) {
        $this->logger->log($level, $message, $context);
    }
}
