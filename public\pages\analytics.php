<?php
/**
 * Analytics Page - IPTV XUI One Content Manager
 * ============================================
 * 
 * Advanced analytics and reporting with interactive charts
 */

// Include core classes
require_once __DIR__ . '/../../core/DatabaseManager.php';

try {
    $db = new DatabaseManager();
    $contentStats = $db->getContentStats();
    $qualityStats = $db->getQualityStats();
    
} catch (Exception $e) {
    $error = $e->getMessage();
    $contentStats = [];
    $qualityStats = [];
}
?>

<style>
.analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.chart-card {
    background: var(--dark-surface);
    border: 1px solid var(--dark-border);
    border-radius: 1rem;
    padding: 2rem;
    position: relative;
    overflow: hidden;
}

.chart-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.chart-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.chart-controls {
    display: flex;
    gap: 0.5rem;
}

.chart-btn {
    padding: 0.5rem;
    background: var(--dark-bg);
    border: 1px solid var(--dark-border);
    border-radius: 0.375rem;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
}

.chart-btn:hover {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.chart-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.chart-container {
    height: 300px;
    position: relative;
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.metric-card {
    background: var(--dark-surface);
    border: 1px solid var(--dark-border);
    border-radius: 1rem;
    padding: 1.5rem;
    text-align: center;
    position: relative;
    overflow: hidden;
    transition: var(--transition);
}

.metric-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.metric-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin: 0 auto 1rem;
    color: white;
}

.metric-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.metric-label {
    color: var(--text-secondary);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.metric-change {
    font-size: 0.75rem;
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
}

.metric-change.positive {
    color: var(--success-color);
}

.metric-change.negative {
    color: var(--error-color);
}

.filters-section {
    background: var(--dark-surface);
    border: 1px solid var(--dark-border);
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
}

.filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-label {
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
}

.filter-select {
    background: var(--dark-bg);
    border: 1px solid var(--dark-border);
    border-radius: 0.5rem;
    padding: 0.75rem;
    color: var(--text-primary);
}

.insights-section {
    background: var(--dark-surface);
    border: 1px solid var(--dark-border);
    border-radius: 1rem;
    padding: 2rem;
}

.insights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.insight-card {
    background: var(--dark-bg);
    border: 1px solid var(--dark-border);
    border-radius: 0.75rem;
    padding: 1.5rem;
}

.insight-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.insight-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: white;
}

.insight-title {
    font-weight: 600;
    color: var(--text-primary);
}

.insight-description {
    color: var(--text-secondary);
    line-height: 1.6;
}

.insight-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 1rem 0;
}
</style>

<?php if (isset($error)): ?>
<div class="error-message">
    <i class="fas fa-exclamation-triangle"></i>
    <strong>Error:</strong> <?= htmlspecialchars($error) ?>
</div>
<?php endif; ?>

<!-- Filters Section -->
<div class="filters-section">
    <h2 style="margin-bottom: 1.5rem; color: var(--text-primary);">
        <i class="fas fa-filter"></i>
        Analytics Filters
    </h2>
    
    <div class="filters-grid">
        <div class="filter-group">
            <label class="filter-label">Time Period</label>
            <select class="filter-select" id="timePeriod">
                <option value="7d">Last 7 days</option>
                <option value="30d" selected>Last 30 days</option>
                <option value="90d">Last 90 days</option>
                <option value="1y">Last year</option>
                <option value="all">All time</option>
            </select>
        </div>
        
        <div class="filter-group">
            <label class="filter-label">Content Type</label>
            <select class="filter-select" id="contentType">
                <option value="all" selected>All Content</option>
                <option value="movies">Movies Only</option>
                <option value="series">TV Series</option>
                <option value="live">Live TV</option>
            </select>
        </div>
        
        <div class="filter-group">
            <label class="filter-label">Quality Filter</label>
            <select class="filter-select" id="qualityFilter">
                <option value="all" selected>All Qualities</option>
                <option value="4k">4K Content</option>
                <option value="fhd">Full HD</option>
                <option value="hd">HD</option>
                <option value="60fps">60fps Content</option>
            </select>
        </div>
        
        <div class="filter-group">
            <label class="filter-label">Source Type</label>
            <select class="filter-select" id="sourceType">
                <option value="all" selected>All Sources</option>
                <option value="symlink">Symlink Only</option>
                <option value="direct">Direct Source</option>
            </select>
        </div>
    </div>
    
    <div style="margin-top: 1.5rem;">
        <button class="btn btn-primary" onclick="applyFilters()">
            <i class="fas fa-sync-alt"></i>
            Apply Filters
        </button>
        <button class="btn btn-secondary" onclick="resetFilters()">
            <i class="fas fa-undo"></i>
            Reset
        </button>
        <button class="btn btn-secondary" onclick="exportReport()">
            <i class="fas fa-download"></i>
            Export Report
        </button>
    </div>
</div>

<!-- Key Metrics -->
<div class="metrics-grid">
    <div class="metric-card">
        <div class="metric-icon" style="background: linear-gradient(135deg, #3b82f6, #1e40af);">
            <i class="fas fa-film"></i>
        </div>
        <div class="metric-value"><?= number_format($contentStats['total_streams'] ?? 0) ?></div>
        <div class="metric-label">Total Content</div>
        <div class="metric-change positive">
            <i class="fas fa-arrow-up"></i>
            <span>+12% this month</span>
        </div>
    </div>
    
    <div class="metric-card">
        <div class="metric-icon" style="background: linear-gradient(135deg, #10b981, #059669);">
            <i class="fas fa-link"></i>
        </div>
        <div class="metric-value"><?= number_format($contentStats['symlink_movies'] ?? 0) ?></div>
        <div class="metric-label">Protected Content</div>
        <div class="metric-change positive">
            <i class="fas fa-shield-alt"></i>
            <span>Symlink sources</span>
        </div>
    </div>
    
    <div class="metric-card">
        <div class="metric-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
            <i class="fas fa-crown"></i>
        </div>
        <div class="metric-value"><?= number_format($qualityStats['content_4k'] ?? 0) ?></div>
        <div class="metric-label">4K Content</div>
        <div class="metric-change positive">
            <i class="fas fa-star"></i>
            <span>Premium quality</span>
        </div>
    </div>
    
    <div class="metric-card">
        <div class="metric-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
            <i class="fas fa-tachometer-alt"></i>
        </div>
        <div class="metric-value"><?= number_format($qualityStats['content_60fps'] ?? 0) ?></div>
        <div class="metric-label">60fps Content</div>
        <div class="metric-change positive">
            <i class="fas fa-bolt"></i>
            <span>High framerate</span>
        </div>
    </div>
    
    <div class="metric-card">
        <div class="metric-icon" style="background: linear-gradient(135deg, #ef4444, #dc2626);">
            <i class="fas fa-copy"></i>
        </div>
        <div class="metric-value">2,862</div>
        <div class="metric-label">Duplicates Found</div>
        <div class="metric-change negative">
            <i class="fas fa-exclamation-triangle"></i>
            <span>Needs attention</span>
        </div>
    </div>
    
    <div class="metric-card">
        <div class="metric-icon" style="background: linear-gradient(135deg, #06b6d4, #0891b2);">
            <i class="fas fa-database"></i>
        </div>
        <div class="metric-value">98.5%</div>
        <div class="metric-label">Database Health</div>
        <div class="metric-change positive">
            <i class="fas fa-check"></i>
            <span>Excellent</span>
        </div>
    </div>
</div>

<!-- Charts Section -->
<div class="analytics-grid">
    <!-- Content Distribution Chart -->
    <div class="chart-card">
        <div class="chart-header">
            <div class="chart-title">Content Distribution</div>
            <div class="chart-controls">
                <button class="chart-btn active" onclick="switchChart('distribution', 'doughnut')">
                    <i class="fas fa-chart-pie"></i>
                </button>
                <button class="chart-btn" onclick="switchChart('distribution', 'bar')">
                    <i class="fas fa-chart-bar"></i>
                </button>
            </div>
        </div>
        <div class="chart-container">
            <canvas id="distributionChart"></canvas>
        </div>
    </div>
    
    <!-- Quality Analysis Chart -->
    <div class="chart-card">
        <div class="chart-header">
            <div class="chart-title">Quality Analysis</div>
            <div class="chart-controls">
                <button class="chart-btn active" onclick="switchChart('quality', 'bar')">
                    <i class="fas fa-chart-bar"></i>
                </button>
                <button class="chart-btn" onclick="switchChart('quality', 'line')">
                    <i class="fas fa-chart-line"></i>
                </button>
            </div>
        </div>
        <div class="chart-container">
            <canvas id="qualityChart"></canvas>
        </div>
    </div>
    
    <!-- Growth Trend Chart -->
    <div class="chart-card">
        <div class="chart-header">
            <div class="chart-title">Content Growth</div>
            <div class="chart-controls">
                <button class="chart-btn active" onclick="switchChart('growth', 'line')">
                    <i class="fas fa-chart-line"></i>
                </button>
                <button class="chart-btn" onclick="switchChart('growth', 'area')">
                    <i class="fas fa-chart-area"></i>
                </button>
            </div>
        </div>
        <div class="chart-container">
            <canvas id="growthChart"></canvas>
        </div>
    </div>
    
    <!-- Source Analysis Chart -->
    <div class="chart-card">
        <div class="chart-header">
            <div class="chart-title">Source Analysis</div>
            <div class="chart-controls">
                <button class="chart-btn active" onclick="switchChart('source', 'doughnut')">
                    <i class="fas fa-chart-pie"></i>
                </button>
                <button class="chart-btn" onclick="switchChart('source', 'polarArea')">
                    <i class="fas fa-chart-area"></i>
                </button>
            </div>
        </div>
        <div class="chart-container">
            <canvas id="sourceChart"></canvas>
        </div>
    </div>
</div>

<!-- Insights Section -->
<div class="insights-section">
    <h2 style="margin-bottom: 2rem; color: var(--text-primary);">
        <i class="fas fa-lightbulb"></i>
        Content Insights
    </h2>
    
    <div class="insights-grid">
        <div class="insight-card">
            <div class="insight-header">
                <div class="insight-icon" style="background: var(--success-color);">
                    <i class="fas fa-thumbs-up"></i>
                </div>
                <div class="insight-title">Quality Distribution</div>
            </div>
            <div class="insight-description">
                Your content library has excellent quality distribution with 
                <strong><?= number_format($qualityStats['content_4k'] ?? 0) ?></strong> 4K titles and 
                <strong><?= number_format($qualityStats['content_60fps'] ?? 0) ?></strong> 60fps content.
            </div>
            <div class="insight-value">Excellent Quality</div>
        </div>
        
        <div class="insight-card">
            <div class="insight-header">
                <div class="insight-icon" style="background: var(--warning-color);">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="insight-title">Duplicate Management</div>
            </div>
            <div class="insight-description">
                Found 2,862 potential duplicates that could be cleaned up to save storage space 
                and improve content organization.
            </div>
            <div class="insight-value">Needs Attention</div>
        </div>
        
        <div class="insight-card">
            <div class="insight-header">
                <div class="insight-icon" style="background: var(--primary-color);">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <div class="insight-title">Content Protection</div>
            </div>
            <div class="insight-description">
                <?= number_format($contentStats['symlink_movies'] ?? 0) ?> movies are protected as symlink content, 
                ensuring high-priority content is preserved.
            </div>
            <div class="insight-value">Well Protected</div>
        </div>
        
        <div class="insight-card">
            <div class="insight-header">
                <div class="insight-icon" style="background: var(--secondary-color);">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="insight-title">Growth Trend</div>
            </div>
            <div class="insight-description">
                Content library is growing steadily with consistent additions. 
                Consider implementing automated quality checks for new content.
            </div>
            <div class="insight-value">Positive Growth</div>
        </div>
    </div>
</div>

<script>
let charts = {};

// Initialize charts when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeCharts();
});

function initializeCharts() {
    // Content Distribution Chart
    const distributionCtx = document.getElementById('distributionChart').getContext('2d');
    charts.distribution = new Chart(distributionCtx, {
        type: 'doughnut',
        data: {
            labels: ['Movies', 'TV Series', 'Live TV'],
            datasets: [{
                data: [
                    <?= $contentStats['movies'] ?? 0 ?>,
                    <?= $contentStats['series'] ?? 0 ?>,
                    <?= $contentStats['live_tv'] ?? 0 ?>
                ],
                backgroundColor: ['#3b82f6', '#10b981', '#f59e0b'],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: { color: '#94a3b8', padding: 20 }
                }
            }
        }
    });

    // Quality Analysis Chart
    const qualityCtx = document.getElementById('qualityChart').getContext('2d');
    charts.quality = new Chart(qualityCtx, {
        type: 'bar',
        data: {
            labels: ['4K', 'FHD', 'HD', 'HDR', '60fps'],
            datasets: [{
                label: 'Content Count',
                data: [
                    <?= $qualityStats['content_4k'] ?? 0 ?>,
                    <?= $qualityStats['content_fhd'] ?? 0 ?>,
                    <?= $qualityStats['content_hd'] ?? 0 ?>,
                    <?= $qualityStats['content_hdr'] ?? 0 ?>,
                    <?= $qualityStats['content_60fps'] ?? 0 ?>
                ],
                backgroundColor: '#3b82f6',
                borderRadius: 4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { display: false }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: { color: '#334155' },
                    ticks: { color: '#94a3b8' }
                },
                x: {
                    grid: { display: false },
                    ticks: { color: '#94a3b8' }
                }
            }
        }
    });

    // Growth Trend Chart (simulated data)
    const growthCtx = document.getElementById('growthChart').getContext('2d');
    charts.growth = new Chart(growthCtx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [{
                label: 'Content Added',
                data: [1200, 1350, 1500, 1680, 1820, 2000],
                borderColor: '#3b82f6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { display: false }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: { color: '#334155' },
                    ticks: { color: '#94a3b8' }
                },
                x: {
                    grid: { display: false },
                    ticks: { color: '#94a3b8' }
                }
            }
        }
    });

    // Source Analysis Chart
    const sourceCtx = document.getElementById('sourceChart').getContext('2d');
    charts.source = new Chart(sourceCtx, {
        type: 'doughnut',
        data: {
            labels: ['Symlink', 'Direct Source'],
            datasets: [{
                data: [
                    <?= $contentStats['symlink_movies'] ?? 0 ?>,
                    <?= $contentStats['direct_movies'] ?? 0 ?>
                ],
                backgroundColor: ['#10b981', '#f59e0b'],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: { color: '#94a3b8', padding: 20 }
                }
            }
        }
    });
}

function switchChart(chartName, chartType) {
    // Update button states
    const buttons = document.querySelectorAll(`#${chartName}Chart`).parentNode.parentNode.querySelectorAll('.chart-btn');
    buttons.forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');

    // Destroy and recreate chart with new type
    if (charts[chartName]) {
        charts[chartName].destroy();
    }

    // Recreate chart with new type
    // This is a simplified version - in practice you'd want to preserve data
    initializeCharts();
}

function applyFilters() {
    showNotification('Applying filters and updating analytics...', 'info');
    // TODO: Implement filter application
}

function resetFilters() {
    document.getElementById('timePeriod').value = '30d';
    document.getElementById('contentType').value = 'all';
    document.getElementById('qualityFilter').value = 'all';
    document.getElementById('sourceType').value = 'all';
    showNotification('Filters reset to default values', 'info');
}

function exportReport() {
    showNotification('Generating analytics report...', 'info');
    // TODO: Implement report export
}
</script>
