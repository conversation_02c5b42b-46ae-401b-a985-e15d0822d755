"""
Gestor de base de datos
======================

Maneja todas las operaciones de base de datos usando MySQL/MariaDB
con soporte para operaciones asíncronas y pool de conexiones.
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Any, Tuple, Union
from datetime import datetime

import mysql.connector
from mysql.connector import pooling
from contextlib import asynccontextmanager

from config.database import QUERIES, CONNECTION_CONFIG

class DatabaseManager:
    """Gestor de base de datos con soporte asíncrono"""
    
    def __init__(self, settings):
        self.settings = settings
        self.logger = logging.getLogger("iptv_manager.database")
        self.pool = None
        self._initialized = False
        
    async def initialize(self):
        """Inicializar el pool de conexiones"""
        if self._initialized:
            return
            
        try:
            config = self.settings.get_db_config()
            config.update(CONNECTION_CONFIG)
            
            # Crear pool de conexiones
            self.pool = mysql.connector.pooling.MySQLConnectionPool(
                pool_name=config["pool_name"],
                pool_size=config["pool_size"],
                pool_reset_session=config["pool_reset_session"],
                **{k: v for k, v in config.items() if k not in ["pool_name", "pool_size", "pool_reset_session"]}
            )
            
            self._initialized = True
            self.logger.info("Pool de conexiones inicializado correctamente")
            
        except Exception as e:
            self.logger.error(f"Error al inicializar pool de conexiones: {str(e)}")
            raise
    
    @asynccontextmanager
    async def get_connection(self):
        """Obtener conexión del pool"""
        if not self._initialized:
            await self.initialize()
            
        connection = None
        try:
            connection = self.pool.get_connection()
            yield connection
        except Exception as e:
            self.logger.error(f"Error en conexión de base de datos: {str(e)}")
            raise
        finally:
            if connection and connection.is_connected():
                connection.close()
    
    async def test_connection(self) -> bool:
        """Probar conexión a la base de datos"""
        try:
            async with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                cursor.fetchone()
                cursor.close()
                return True
        except Exception as e:
            self.logger.error(f"Error al probar conexión: {str(e)}")
            return False

    async def verify_tables(self) -> bool:
        """Verificar que existan las tablas necesarias de XUI One"""
        try:
            async with self.get_connection() as conn:
                cursor = conn.cursor()

                # Tablas requeridas de XUI One
                required_tables = [
                    "streams", "streams_categories", "streams_series",
                    "streams_episodes", "streams_types", "streams_servers"
                ]

                # Verificar cada tabla
                for table_name in required_tables:
                    cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
                    result = cursor.fetchone()
                    if not result:
                        self.logger.error(f"Tabla requerida '{table_name}' no encontrada")
                        cursor.close()
                        return False

                cursor.close()
                self.logger.info("Todas las tablas requeridas de XUI One están presentes")
                return True

        except Exception as e:
            self.logger.error(f"Error al verificar tablas: {str(e)}")
            return False
    
    async def create_tables(self):
        """
        NOTA: Este método no crea tablas ya que trabajamos con una base de datos XUI One existente.
        Solo verifica que las tablas necesarias estén presentes.
        """
        try:
            if not await self.verify_tables():
                raise Exception("Las tablas requeridas de XUI One no están presentes en la base de datos")

            self.logger.info("Verificación de tablas XUI One completada correctamente")

        except Exception as e:
            self.logger.error(f"Error al verificar tablas XUI One: {str(e)}")
            raise
    

    

    

    
    async def execute_query(self, query: str, params: tuple | None = None) -> List[Dict]:
        """Ejecutar consulta SELECT"""
        try:
            async with self.get_connection() as conn:
                cursor = conn.cursor(dictionary=True)
                cursor.execute(query, params or ())
                result = cursor.fetchall()
                cursor.close()
                return result
        except Exception as e:
            self.logger.error(f"Error en consulta: {str(e)}")
            raise
    
    async def execute_update(self, query: str, params: tuple | None = None) -> int:
        """Ejecutar consulta INSERT/UPDATE/DELETE"""
        try:
            async with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params or ())
                affected_rows = cursor.rowcount
                conn.commit()
                cursor.close()
                return affected_rows
        except Exception as e:
            self.logger.error(f"Error en actualización: {str(e)}")
            raise
    
    async def execute_batch(self, query: str, params_list: List[tuple]) -> int:
        """Ejecutar consultas en lote"""
        try:
            async with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.executemany(query, params_list)
                affected_rows = cursor.rowcount
                conn.commit()
                cursor.close()
                return affected_rows
        except Exception as e:
            self.logger.error(f"Error en lote: {str(e)}")
            raise
    
    # Métodos específicos para películas (usando tablas XUI One)
    async def get_movies(self, limit: int = 50, offset: int = 0) -> List[Dict]:
        """Obtener películas con paginación desde streams (type=2)"""
        return await self.execute_query(QUERIES["get_movies"], (limit, offset))

    async def search_movies(self, search_term: str, limit: int = 100) -> List[Dict]:
        """Buscar películas por título"""
        term = f"%{search_term}%"
        return await self.execute_query(QUERIES["search_movies"], (term, term, limit))

    async def get_movie_by_id(self, movie_id: int) -> Optional[Dict]:
        """Obtener película por ID"""
        result = await self.execute_query(QUERIES["get_movie_by_id"], (movie_id,))
        return result[0] if result else None

    async def update_movie_tmdb(self, stream_id: int, tmdb_data: Dict) -> int:
        """Actualizar información TMDB de una película"""
        movie_properties = json.dumps(tmdb_data.get('movie_properties', {}))
        return await self.execute_update(
            QUERIES["update_stream_tmdb"],
            (tmdb_data.get('tmdb_id'), movie_properties, tmdb_data.get('year'),
             tmdb_data.get('rating'), stream_id)
        )
    
    # Métodos específicos para series de TV (usando tablas XUI One)
    async def get_tv_shows(self, limit: int = 50, offset: int = 0) -> List[Dict]:
        """Obtener series de TV con paginación desde streams_series"""
        return await self.execute_query(QUERIES["get_series"], (limit, offset))

    async def get_series(self, limit: int = 50, offset: int = 0) -> List[Dict]:
        """Alias para get_tv_shows para compatibilidad"""
        return await self.get_tv_shows(limit, offset)

    async def search_tv_shows(self, search_term: str, limit: int = 100) -> List[Dict]:
        """Buscar series por nombre"""
        term = f"%{search_term}%"
        return await self.execute_query(QUERIES["search_series"], (term, term, limit))

    async def get_tv_show_by_id(self, series_id: int) -> Optional[Dict]:
        """Obtener serie por ID"""
        result = await self.execute_query(QUERIES["get_series_by_id"], (series_id,))
        return result[0] if result else None

    async def get_series_episodes(self, series_id: int) -> List[Dict]:
        """Obtener episodios de una serie"""
        return await self.execute_query(QUERIES["get_series_episodes"], (series_id,))

    async def update_series_tmdb(self, series_id: int, tmdb_data: Dict) -> int:
        """Actualizar información TMDB de una serie"""
        return await self.execute_update(
            QUERIES["update_series_tmdb"],
            (tmdb_data.get('tmdb_id'), tmdb_data.get('plot'), tmdb_data.get('cast'),
             tmdb_data.get('rating'), tmdb_data.get('backdrop_path'),
             tmdb_data.get('youtube_trailer'), tmdb_data.get('year'), series_id)
        )
    
    # Métodos para categorías y tipos de streams
    async def get_categories(self) -> List[Dict]:
        """Obtener todas las categorías"""
        return await self.execute_query(QUERIES["get_categories"])

    async def get_categories_by_type(self, category_type: str) -> List[Dict]:
        """Obtener categorías por tipo"""
        return await self.execute_query(QUERIES["get_categories_by_type"], (category_type,))

    async def get_stream_types(self) -> List[Dict]:
        """Obtener tipos de streams"""
        return await self.execute_query(QUERIES["get_stream_types"])

    # Métodos para streams en general
    async def get_streams(self, limit: int = 50, offset: int = 0) -> List[Dict]:
        """Obtener todos los streams con paginación"""
        return await self.execute_query(QUERIES["get_streams"], (limit, offset))

    async def get_stream_by_id(self, stream_id: int) -> Optional[Dict]:
        """Obtener stream por ID"""
        result = await self.execute_query(QUERIES["get_stream_by_id"], (stream_id,))
        return result[0] if result else None

    async def search_streams(self, search_term: str, limit: int = 100) -> List[Dict]:
        """Buscar streams por nombre"""
        term = f"%{search_term}%"
        return await self.execute_query(QUERIES["search_streams"], (term, term, limit))

    async def get_live_streams(self, limit: int = 50, offset: int = 0) -> List[Dict]:
        """Obtener streams de TV en vivo (type=1)"""
        return await self.execute_query(QUERIES["get_live_streams"], (limit, offset))
    
    # Métodos para análisis y estadísticas
    async def get_content_stats(self) -> Dict:
        """Obtener estadísticas de contenido"""
        result = await self.execute_query(QUERIES["get_content_stats"])
        return result[0] if result else {}

    async def get_duplicate_streams(self) -> List[Dict]:
        """Obtener streams duplicados"""
        return await self.execute_query(QUERIES["get_duplicate_streams"])

    async def get_streams_without_tmdb(self, limit: int = 100) -> List[Dict]:
        """Obtener streams sin información TMDB"""
        return await self.execute_query(QUERIES["get_streams_without_tmdb"], (limit,))

    async def get_movies_without_tmdb(self, limit: int = 100) -> List[Dict]:
        """Obtener películas sin información TMDB"""
        return await self.execute_query(QUERIES["get_movies_without_tmdb"], (limit,))

    async def get_series_without_tmdb(self, limit: int = 100) -> List[Dict]:
        """Obtener series sin información TMDB"""
        return await self.execute_query(QUERIES["get_series_without_tmdb"], (limit,))

    async def get_popular_movies(self, limit: int = 50) -> List[Dict]:
        """Obtener películas populares"""
        return await self.execute_query(QUERIES["get_popular_movies"], (limit,))

    async def get_recent_movies(self, days: int = 30, limit: int = 50) -> List[Dict]:
        """Obtener películas recientes"""
        return await self.execute_query(QUERIES["get_recent_movies"], (days, limit))

    async def get_movies_by_year(self, year: int, limit: int = 50) -> List[Dict]:
        """Obtener películas por año"""
        return await self.execute_query(QUERIES["get_movies_by_year"], (year, limit))

    async def get_broken_streams(self, limit: int = 100) -> List[Dict]:
        """Obtener streams con problemas"""
        return await self.execute_query(QUERIES["get_broken_streams"], (limit,))
    
    # Métodos para eliminación de contenido
    async def delete_stream(self, stream_id: int) -> int:
        """Eliminar stream"""
        return await self.execute_update(QUERIES["delete_stream"], (stream_id,))

    async def delete_series(self, series_id: int) -> int:
        """Eliminar serie"""
        return await self.execute_update(QUERIES["delete_series"], (series_id,))

    # Métodos de sesión simplificados (para compatibilidad con la UI)
    async def get_session(self, session_id: str) -> Optional[Dict]:
        """Obtener sesión (método simplificado para XUI One)"""
        # Para XUI One, simplemente retornamos None ya que no manejamos sesiones complejas
        return None

    async def save_session(self, session_id: str, user_data: Dict, preferences: Dict):
        """Guardar sesión (método simplificado para XUI One)"""
        # Para XUI One, simplemente loggeamos que se intentó guardar la sesión
        self.logger.info(f"Sesión {session_id} guardada (modo simplificado)")
        pass

    # Métodos de cache simplificados (para compatibilidad con TMDB)
    async def get_cache(self, key: str) -> Optional[str]:
        """Obtener valor del cache (método simplificado para XUI One)"""
        # Para XUI One, no implementamos cache persistente, siempre retornamos None
        return None

    async def set_cache(self, key: str, value: str, ttl: int = 3600):
        """Establecer valor en cache (método simplificado para XUI One)"""
        # Para XUI One, simplemente loggeamos que se intentó guardar en cache
        self.logger.debug(f"Cache {key} guardado (modo simplificado)")
        pass

    # ========================================
    # MÉTODOS AVANZADOS PARA GESTIÓN DE DUPLICADOS
    # ========================================

    async def get_detailed_duplicates(self) -> List[Dict]:
        """Obtener duplicados con información detallada para gestión"""
        query = """
            SELECT
                stream_display_name,
                COUNT(*) as count,
                GROUP_CONCAT(id) as ids,
                GROUP_CONCAT(tmdb_id) as tmdb_ids,
                GROUP_CONCAT(type) as types,
                GROUP_CONCAT(category_id) as categories,
                GROUP_CONCAT(year) as years,
                GROUP_CONCAT(rating) as ratings,
                GROUP_CONCAT(SUBSTRING(stream_source, 1, 50)) as sources_preview
            FROM streams
            GROUP BY stream_display_name
            HAVING COUNT(*) > 1
            ORDER BY count DESC, stream_display_name
        """
        return await self.execute_query(query)

    async def get_duplicate_movies_by_quality(self) -> List[Dict]:
        """Obtener películas duplicadas agrupadas por calidad con información de symlink y direct_source"""
        query = """
            SELECT
                s.stream_display_name,
                s.id,
                s.tmdb_id,
                s.year,
                s.rating,
                s.category_id,
                sc.category_name,
                CASE
                    WHEN s.stream_display_name LIKE '%4K%' OR s.stream_display_name LIKE '%2160p%' THEN '4K'
                    WHEN s.stream_display_name LIKE '%1080p%' OR s.stream_display_name LIKE '%FHD%' THEN '1080p'
                    WHEN s.stream_display_name LIKE '%720p%' OR s.stream_display_name LIKE '%HD%' THEN '720p'
                    WHEN s.stream_display_name LIKE '%480p%' THEN '480p'
                    WHEN s.stream_display_name LIKE '%CAM%' OR s.stream_display_name LIKE '%TS%' THEN 'CAM/TS'
                    ELSE 'Unknown'
                END as quality,
                s.stream_source,
                s.movie_symlink,
                s.direct_source,
                CASE
                    WHEN s.movie_symlink = 1 THEN 'Symlink'
                    WHEN s.direct_source = 1 THEN 'Direct'
                    ELSE 'Stream'
                END as source_type,
                s.added,
                s.updated
            FROM streams s
            LEFT JOIN streams_categories sc ON FIND_IN_SET(sc.id, s.category_id)
            WHERE s.type = 2
            AND s.stream_display_name IN (
                SELECT stream_display_name
                FROM streams
                WHERE type = 2
                GROUP BY stream_display_name
                HAVING COUNT(*) > 1
            )
            ORDER BY s.stream_display_name, quality DESC, s.added DESC
        """
        return await self.execute_query(query)

    async def merge_duplicate_movies(self, keep_id: int, remove_ids: List[int]) -> bool:
        """Fusionar películas duplicadas manteniendo la mejor calidad"""
        try:
            async with self.get_connection() as conn:
                cursor = conn.cursor()

                # Obtener información de la película a mantener
                cursor.execute("SELECT * FROM streams WHERE id = %s", (keep_id,))
                keep_movie = cursor.fetchone()

                if not keep_movie:
                    return False

                # Eliminar duplicados
                for remove_id in remove_ids:
                    cursor.execute("DELETE FROM streams WHERE id = %s", (remove_id,))

                conn.commit()
                cursor.close()

                self.logger.info(f"Fusionadas películas duplicadas: mantenido {keep_id}, eliminados {remove_ids}")
                return True

        except Exception as e:
            self.logger.error(f"Error al fusionar duplicados: {str(e)}")
            return False

    async def move_stream_to_category(self, stream_id: int, new_category_id: int) -> bool:
        """Mover stream a una nueva categoría"""
        try:
            query = "UPDATE streams SET category_id = %s WHERE id = %s"
            affected = await self.execute_update(query, (new_category_id, stream_id))

            if affected > 0:
                self.logger.info(f"Stream {stream_id} movido a categoría {new_category_id}")
                return True
            return False

        except Exception as e:
            self.logger.error(f"Error al mover stream: {str(e)}")
            return False

    async def rename_stream(self, stream_id: int, new_name: str) -> bool:
        """Renombrar un stream"""
        try:
            query = "UPDATE streams SET stream_display_name = %s WHERE id = %s"
            affected = await self.execute_update(query, (new_name, stream_id))

            if affected > 0:
                self.logger.info(f"Stream {stream_id} renombrado a '{new_name}'")
                return True
            return False

        except Exception as e:
            self.logger.error(f"Error al renombrar stream: {str(e)}")
            return False

    # ========================================
    # MÉTODOS PARA GESTIÓN DE M3U Y TMDB
    # ========================================

    async def insert_stream_from_m3u(self, m3u_entry: Dict, tmdb_data: Optional[Dict] = None) -> int:
        """Insertar stream desde entrada M3U con datos TMDB opcionales"""
        try:
            # Preparar datos básicos del stream
            stream_data = {
                'type': self._determine_stream_type(m3u_entry),
                'category_id': m3u_entry.get('category_id', '[]'),
                'stream_display_name': m3u_entry.get('title', ''),
                'stream_source': json.dumps([m3u_entry.get('url', '')]),
                'stream_icon': m3u_entry.get('logo', ''),
                'added': int(datetime.now().timestamp()),
                'updated': datetime.now()
            }

            # Agregar datos TMDB si están disponibles
            if tmdb_data:
                stream_data.update({
                    'tmdb_id': tmdb_data.get('id'),
                    'year': tmdb_data.get('release_date', '')[:4] if tmdb_data.get('release_date') else None,
                    'rating': tmdb_data.get('vote_average', 0),
                    'movie_properties': self._create_movie_properties_from_tmdb(tmdb_data)
                })

            # Insertar en base de datos
            query = """
                INSERT INTO streams (
                    type, category_id, stream_display_name, stream_source, stream_icon,
                    tmdb_id, year, rating, movie_properties, added, updated
                ) VALUES (
                    %(type)s, %(category_id)s, %(stream_display_name)s, %(stream_source)s, %(stream_icon)s,
                    %(tmdb_id)s, %(year)s, %(rating)s, %(movie_properties)s, %(added)s, %(updated)s
                )
            """

            async with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, stream_data)
                stream_id = cursor.lastrowid
                conn.commit()
                cursor.close()

                self.logger.info(f"Stream insertado desde M3U: {stream_id}")
                return stream_id

        except Exception as e:
            self.logger.error(f"Error al insertar stream desde M3U: {str(e)}")
            return 0

    def _determine_stream_type(self, m3u_entry: Dict) -> int:
        """Determinar tipo de stream basado en entrada M3U"""
        title = m3u_entry.get('title', '').lower()
        group = m3u_entry.get('group_title', '').lower()

        # Detectar películas
        movie_indicators = ['movie', 'film', 'cinema', 'pelicula', '1080p', '720p', '4k', 'hd']
        if any(indicator in title or indicator in group for indicator in movie_indicators):
            return 2  # Movies

        # Detectar series
        series_indicators = ['series', 'tv show', 'season', 'episode', 's01', 's02', 'temporada']
        if any(indicator in title or indicator in group for indicator in series_indicators):
            return 3  # Series

        # Por defecto, Live TV
        return 1  # Live TV

    def _create_movie_properties_from_tmdb(self, tmdb_data: Dict) -> str:
        """Crear JSON de movie_properties desde datos TMDB"""
        try:
            properties = {
                'tmdb_id': tmdb_data.get('id'),
                'name': tmdb_data.get('title', tmdb_data.get('name', '')),
                'o_name': tmdb_data.get('original_title', tmdb_data.get('original_name', '')),
                'plot': tmdb_data.get('overview', ''),
                'movie_image': f"https://image.tmdb.org/t/p/w500{tmdb_data.get('poster_path', '')}" if tmdb_data.get('poster_path') else '',
                'cover_big': f"https://image.tmdb.org/t/p/w500{tmdb_data.get('poster_path', '')}" if tmdb_data.get('poster_path') else '',
                'backdrop_path': [f"https://image.tmdb.org/t/p/w1280{tmdb_data.get('backdrop_path', '')}"] if tmdb_data.get('backdrop_path') else [],
                'release_date': tmdb_data.get('release_date', tmdb_data.get('first_air_date', '')),
                'rating': tmdb_data.get('vote_average', 0),
                'year': tmdb_data.get('release_date', tmdb_data.get('first_air_date', ''))[:4] if tmdb_data.get('release_date') or tmdb_data.get('first_air_date') else '',
                'genre': ', '.join([g.get('name', '') for g in tmdb_data.get('genres', [])]),
                'country': ', '.join([c.get('name', '') for c in tmdb_data.get('production_countries', [])]),
                'episode_run_time': tmdb_data.get('runtime', 0),
                'duration_secs': tmdb_data.get('runtime', 0) * 60 if tmdb_data.get('runtime') else 0
            }

            return json.dumps(properties, ensure_ascii=False)

        except Exception as e:
            self.logger.error(f"Error al crear movie_properties: {str(e)}")
            return "{}"

    # ========================================
    # MÉTODOS PARA BÚSQUEDA Y MATCHING TMDB
    # ========================================

    async def find_potential_tmdb_matches(self, stream_name: str, stream_type: int) -> List[Dict]:
        """Buscar coincidencias potenciales en TMDB para un stream"""
        # Este método trabajaría con el TMDBClient para buscar coincidencias
        # Por ahora retorna lista vacía, se implementaría con integración TMDB
        return []

    async def update_stream_with_tmdb_data(self, stream_id: int, tmdb_data: Dict) -> bool:
        """Actualizar stream con datos de TMDB"""
        try:
            movie_properties = self._create_movie_properties_from_tmdb(tmdb_data)

            query = """
                UPDATE streams
                SET tmdb_id = %s, year = %s, rating = %s, movie_properties = %s, updated = %s
                WHERE id = %s
            """

            year = tmdb_data.get('release_date', tmdb_data.get('first_air_date', ''))[:4] if tmdb_data.get('release_date') or tmdb_data.get('first_air_date') else None

            affected = await self.execute_update(query, (
                tmdb_data.get('id'),
                year,
                tmdb_data.get('vote_average', 0),
                movie_properties,
                datetime.now(),
                stream_id
            ))

            if affected > 0:
                self.logger.info(f"Stream {stream_id} actualizado con datos TMDB {tmdb_data.get('id')}")
                return True
            return False

        except Exception as e:
            self.logger.error(f"Error al actualizar stream con TMDB: {str(e)}")
            return False

    async def batch_update_streams_with_tmdb(self, stream_tmdb_pairs: List[Tuple[int, Dict]]) -> int:
        """Actualizar múltiples streams con datos TMDB en lote"""
        updated_count = 0

        try:
            for stream_id, tmdb_data in stream_tmdb_pairs:
                if await self.update_stream_with_tmdb_data(stream_id, tmdb_data):
                    updated_count += 1

                # Pequeña pausa para no sobrecargar
                await asyncio.sleep(0.1)

            self.logger.info(f"Actualizados {updated_count} streams con datos TMDB")
            return updated_count

        except Exception as e:
            self.logger.error(f"Error en actualización en lote: {str(e)}")
            return updated_count

    # ========================================
    # MÉTODOS PARA ANÁLISIS DE CALIDAD
    # ========================================

    async def analyze_quality_distribution(self) -> Dict[str, int]:
        """Analizar distribución de calidades en el contenido"""
        query = """
            SELECT
                CASE
                    WHEN stream_display_name LIKE '%4K%' OR stream_display_name LIKE '%2160p%' THEN '4K'
                    WHEN stream_display_name LIKE '%1080p%' OR stream_display_name LIKE '%FHD%' THEN '1080p'
                    WHEN stream_display_name LIKE '%720p%' OR stream_display_name LIKE '%HD%' THEN '720p'
                    WHEN stream_display_name LIKE '%480p%' THEN '480p'
                    WHEN stream_display_name LIKE '%CAM%' OR stream_display_name LIKE '%TS%' OR stream_display_name LIKE '%SCREENER%' THEN 'CAM/TS'
                    ELSE 'Unknown'
                END as quality,
                COUNT(*) as count
            FROM streams
            WHERE type = 2
            GROUP BY quality
            ORDER BY count DESC
        """

        results = await self.execute_query(query)
        return {row['quality']: row['count'] for row in results}

    async def analyze_source_type_distribution(self) -> Dict[str, int]:
        """Analizar distribución de tipos de fuente (symlink vs direct_source)"""
        query = """
            SELECT
                CASE
                    WHEN movie_symlink = 1 THEN 'Symlink'
                    WHEN direct_source = 1 THEN 'Direct Source'
                    ELSE 'Stream URL'
                END as source_type,
                COUNT(*) as count
            FROM streams
            WHERE type = 2
            GROUP BY source_type
            ORDER BY count DESC
        """

        results = await self.execute_query(query)
        return {row['source_type']: row['count'] for row in results}

    async def get_movies_by_source_and_quality(self, limit: int = 100) -> List[Dict]:
        """Obtener películas con información detallada de fuente y calidad"""
        query = """
            SELECT
                s.id,
                s.stream_display_name,
                s.tmdb_id,
                s.year,
                s.rating,
                sc.category_name,
                CASE
                    WHEN s.stream_display_name LIKE '%4K%' OR s.stream_display_name LIKE '%2160p%' THEN '4K'
                    WHEN s.stream_display_name LIKE '%1080p%' OR s.stream_display_name LIKE '%FHD%' THEN '1080p'
                    WHEN s.stream_display_name LIKE '%720p%' OR s.stream_display_name LIKE '%HD%' THEN '720p'
                    WHEN s.stream_display_name LIKE '%480p%' THEN '480p'
                    WHEN s.stream_display_name LIKE '%CAM%' OR s.stream_display_name LIKE '%TS%' THEN 'CAM/TS'
                    ELSE 'Unknown'
                END as quality,
                s.movie_symlink,
                s.direct_source,
                CASE
                    WHEN s.movie_symlink = 1 THEN 'Symlink'
                    WHEN s.direct_source = 1 THEN 'Direct Source'
                    ELSE 'Stream URL'
                END as source_type,
                s.stream_source,
                s.added,
                s.updated
            FROM streams s
            LEFT JOIN streams_categories sc ON FIND_IN_SET(sc.id, s.category_id)
            WHERE s.type = 2
            ORDER BY s.added DESC
            LIMIT %s
        """
        return await self.execute_query(query, (limit,))

    async def get_low_quality_movies(self, limit: int = 100) -> List[Dict]:
        """Obtener películas de baja calidad (CAM, TS, etc.)"""
        query = """
            SELECT s.*, sc.category_name
            FROM streams s
            LEFT JOIN streams_categories sc ON FIND_IN_SET(sc.id, s.category_id)
            WHERE s.type = 2
            AND (
                s.stream_display_name LIKE '%CAM%' OR
                s.stream_display_name LIKE '%TS%' OR
                s.stream_display_name LIKE '%SCREENER%' OR
                s.stream_display_name LIKE '%DVDSCR%' OR
                s.stream_display_name LIKE '%WORKPRINT%'
            )
            ORDER BY s.added DESC
            LIMIT %s
        """
        return await self.execute_query(query, (limit,))

    async def get_streams_by_category(self, category_id: int, limit: int = 50, offset: int = 0) -> List[Dict]:
        """Obtener streams de una categoría específica"""
        query = """
            SELECT s.*, sc.category_name, st.type_name
            FROM streams s
            LEFT JOIN streams_categories sc ON FIND_IN_SET(sc.id, s.category_id)
            LEFT JOIN streams_types st ON s.type = st.type_id
            WHERE FIND_IN_SET(%s, s.category_id)
            ORDER BY s.added DESC
            LIMIT %s OFFSET %s
        """
        return await self.execute_query(query, (category_id, limit, offset))

    async def create_category(self, category_name: str, category_type: str, parent_id: Optional[int] = None) -> int:
        """Crear nueva categoría"""
        try:
            query = """
                INSERT INTO streams_categories (category_name, category_type, parent_id, cat_order)
                VALUES (%s, %s, %s, (SELECT COALESCE(MAX(cat_order), 0) + 1 FROM streams_categories sc))
            """

            async with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, (category_name, category_type, parent_id))
                category_id = cursor.lastrowid
                conn.commit()
                cursor.close()

                self.logger.info(f"Categoría creada: {category_name} (ID: {category_id})")
                return category_id

        except Exception as e:
            self.logger.error(f"Error al crear categoría: {str(e)}")
            return 0

    # ========================================
    # MÉTODOS PARA REPORTES Y ESTADÍSTICAS AVANZADAS
    # ========================================

    async def get_content_health_report(self) -> Dict[str, Any]:
        """Generar reporte de salud del contenido"""
        try:
            # Estadísticas básicas
            stats = await self.get_content_stats()

            # Duplicados
            duplicates = await self.get_duplicate_streams()

            # Calidad
            quality_dist = await self.analyze_quality_distribution()

            # Contenido sin TMDB
            no_tmdb_count = len(await self.get_streams_without_tmdb(limit=1000))

            # Streams rotos
            broken_count = len(await self.get_broken_streams(limit=1000))

            return {
                'basic_stats': stats,
                'duplicates_count': len(duplicates),
                'quality_distribution': quality_dist,
                'no_tmdb_count': no_tmdb_count,
                'broken_streams_count': broken_count,
                'health_score': self._calculate_health_score(stats, len(duplicates), no_tmdb_count, broken_count)
            }

        except Exception as e:
            self.logger.error(f"Error al generar reporte de salud: {str(e)}")
            return {}

    def _calculate_health_score(self, stats: Dict, duplicates_count: int, no_tmdb_count: int, broken_count: int) -> float:
        """Calcular puntuación de salud del contenido (0-100)"""
        total_content = stats.get('total_streams', 1)

        # Penalizaciones
        duplicate_penalty = (duplicates_count / total_content) * 30
        no_tmdb_penalty = (no_tmdb_count / total_content) * 40
        broken_penalty = (broken_count / total_content) * 30

        # Puntuación base
        score = 100 - duplicate_penalty - no_tmdb_penalty - broken_penalty

        return max(0, min(100, score))

    async def close(self):
        """Cerrar conexiones"""
        if self.pool:
            # Cerrar todas las conexiones del pool
            self.logger.info("Cerrando pool de conexiones")
            self._initialized = False
