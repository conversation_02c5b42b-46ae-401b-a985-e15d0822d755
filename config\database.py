"""
Configuración de base de datos para XUI One existente
====================================================

Este archivo contiene la configuración para conectarse a una base de datos
XUI One existente, sin crear nuevas tablas.
"""

# Configuración de conexión
CONNECTION_CONFIG = {
    "pool_name": "xui_pool",
    "pool_size": 10,
    "pool_reset_session": True,
    "autocommit": False,
    "buffered": True,
    "connect_timeout": 30,
    "sql_mode": "TRADITIONAL",
    "use_unicode": True,
    "charset": "utf8mb4",
    "collation": "utf8mb4_unicode_ci"
}

# Esquema de tablas existentes (solo para referencia, no se crearán)
EXISTING_TABLES = {
    "streams": {
        "primary_key": "id",
        "columns": [
            "id", "type", "category_id", "stream_display_name", "stream_source",
            "stream_icon", "notes", "enable_transcode", "transcode_attributes",
            "custom_ffmpeg", "movie_properties", "movie_subtitles", "read_native",
            "target_container", "stream_all", "remove_subtitles", "custom_sid",
            "epg_api", "epg_id", "channel_id", "epg_lang", "order", "auto_restart",
            "transcode_profile_id", "gen_timestamps", "added", "series_no",
            "direct_source", "tv_archive_duration", "tv_archive_server_id",
            "tv_archive_pid", "vframes_server_id", "vframes_pid", "movie_symlink",
            "rtmp_output", "allow_record", "probesize_ondemand", "custom_map",
            "external_push", "delay_minutes", "tmdb_language", "llod", "year",
            "rating", "plex_uuid", "uuid", "epg_offset", "updated", "similar",
            "tmdb_id", "adaptive_link", "title_sync", "fps_restart", "fps_threshold",
            "direct_proxy"
        ]
    },
    "streams_categories": {
        "primary_key": "id",
        "columns": [
            "id", "category_type", "category_name", "parent_id", "cat_order", "is_adult"
        ]
    },
    "streams_series": {
        "primary_key": "id",
        "columns": [
            "id", "title", "category_id", "cover", "cover_big", "genre", "plot",
            "cast", "rating", "director", "release_date", "last_modified",
            "tmdb_id", "seasons", "episode_run_time", "backdrop_path",
            "youtube_trailer", "tmdb_language", "year", "plex_uuid", "similar"
        ]
    },
    "streams_episodes": {
        "primary_key": "id",
        "columns": [
            "id", "season_num", "episode_num", "series_id", "stream_id"
        ]
    },
    "streams_types": {
        "primary_key": "type_id",
        "columns": [
            "type_id", "type_name", "type_key", "type_output", "live"
        ]
    },
    "streams_servers": {
        "primary_key": "server_stream_id",
        "columns": [
            "server_stream_id", "stream_id", "server_id", "parent_id", "pid",
            "to_analyze", "stream_status", "stream_started", "stream_info",
            "monitor_pid", "aes_pid", "current_source", "bitrate", "progress_info",
            "cc_info", "on_demand", "delay_pid", "delay_available_at",
            "pids_create_channel", "cchannel_rsources", "updated", "compatible",
            "audio_codec", "video_codec", "resolution", "ondemand_check"
        ]
    }
}

# Consultas SQL para trabajar con la base de datos existente
QUERIES = {
    # Consultas para streams (todas las películas, series, live TV)
    "get_streams": """
        SELECT s.*, sc.category_name, st.type_name 
        FROM streams s 
        LEFT JOIN streams_categories sc ON FIND_IN_SET(sc.id, s.category_id)
        LEFT JOIN streams_types st ON s.type = st.type_id
        ORDER BY s.added DESC 
        LIMIT %s OFFSET %s
    """,
    
    "get_stream_by_id": """
        SELECT s.*, sc.category_name, st.type_name 
        FROM streams s 
        LEFT JOIN streams_categories sc ON FIND_IN_SET(sc.id, s.category_id)
        LEFT JOIN streams_types st ON s.type = st.type_id
        WHERE s.id = %s
    """,
    
    "search_streams": """
        SELECT s.*, sc.category_name, st.type_name 
        FROM streams s 
        LEFT JOIN streams_categories sc ON FIND_IN_SET(sc.id, s.category_id)
        LEFT JOIN streams_types st ON s.type = st.type_id
        WHERE s.stream_display_name LIKE %s OR s.movie_properties LIKE %s
        ORDER BY s.added DESC 
        LIMIT %s
    """,
    
    # Consultas específicas para películas (type = 2)
    "get_movies": """
        SELECT s.*, sc.category_name 
        FROM streams s 
        LEFT JOIN streams_categories sc ON FIND_IN_SET(sc.id, s.category_id)
        WHERE s.type = 2 
        ORDER BY s.added DESC 
        LIMIT %s OFFSET %s
    """,
    
    "get_movie_by_id": """
        SELECT s.*, sc.category_name 
        FROM streams s 
        LEFT JOIN streams_categories sc ON FIND_IN_SET(sc.id, s.category_id)
        WHERE s.id = %s AND s.type = 2
    """,
    
    "search_movies": """
        SELECT s.*, sc.category_name 
        FROM streams s 
        LEFT JOIN streams_categories sc ON FIND_IN_SET(sc.id, s.category_id)
        WHERE s.type = 2 AND (s.stream_display_name LIKE %s OR s.movie_properties LIKE %s)
        ORDER BY s.added DESC 
        LIMIT %s
    """,
    
    # Consultas para series (type = 3) - usando streams_series
    "get_series": """
        SELECT ss.*, sc.category_name 
        FROM streams_series ss
        LEFT JOIN streams_categories sc ON FIND_IN_SET(sc.id, ss.category_id)
        ORDER BY ss.last_modified DESC 
        LIMIT %s OFFSET %s
    """,
    
    "get_series_by_id": """
        SELECT ss.*, sc.category_name 
        FROM streams_series ss
        LEFT JOIN streams_categories sc ON FIND_IN_SET(sc.id, ss.category_id)
        WHERE ss.id = %s
    """,
    
    "search_series": """
        SELECT ss.*, sc.category_name 
        FROM streams_series ss
        LEFT JOIN streams_categories sc ON FIND_IN_SET(sc.id, ss.category_id)
        WHERE ss.title LIKE %s OR ss.plot LIKE %s
        ORDER BY ss.last_modified DESC 
        LIMIT %s
    """,
    
    # Consultas para live TV (type = 1)
    "get_live_streams": """
        SELECT s.*, sc.category_name 
        FROM streams s 
        LEFT JOIN streams_categories sc ON FIND_IN_SET(sc.id, s.category_id)
        WHERE s.type = 1 
        ORDER BY s.added DESC 
        LIMIT %s OFFSET %s
    """,
    
    # Consultas para categorías
    "get_categories": """
        SELECT * FROM streams_categories 
        ORDER BY cat_order ASC, category_name ASC
    """,
    
    "get_categories_by_type": """
        SELECT * FROM streams_categories 
        WHERE category_type = %s 
        ORDER BY cat_order ASC, category_name ASC
    """,
    
    # Consultas para tipos de streams
    "get_stream_types": """
        SELECT * FROM streams_types 
        ORDER BY type_id ASC
    """,
    
    # Consultas para episodios de series
    "get_series_episodes": """
        SELECT se.*, s.stream_display_name, s.stream_source, s.movie_properties
        FROM streams_episodes se
        JOIN streams s ON se.stream_id = s.id
        WHERE se.series_id = %s
        ORDER BY se.season_num ASC, se.episode_num ASC
    """,
    
    # Consultas para estadísticas
    "get_content_stats": """
        SELECT 
            COUNT(CASE WHEN type = 2 THEN 1 END) as total_movies,
            COUNT(CASE WHEN type = 1 THEN 1 END) as total_live,
            COUNT(CASE WHEN type = 5 THEN 1 END) as total_series_episodes,
            COUNT(*) as total_streams,
            COUNT(CASE WHEN tmdb_id IS NOT NULL AND tmdb_id > 0 THEN 1 END) as with_tmdb,
            COUNT(CASE WHEN tmdb_id IS NULL OR tmdb_id = 0 THEN 1 END) as without_tmdb,
            COUNT(CASE WHEN added > UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 7 DAY)) THEN 1 END) as added_this_week,
            (SELECT COUNT(*) FROM streams_series) as total_series
        FROM streams
    """,
    
    # Consultas para duplicados
    "get_duplicate_streams": """
        SELECT stream_display_name, COUNT(*) as count, GROUP_CONCAT(id) as ids,
               GROUP_CONCAT(tmdb_id) as tmdb_ids, GROUP_CONCAT(type) as types
        FROM streams 
        GROUP BY stream_display_name 
        HAVING COUNT(*) > 1
        ORDER BY count DESC
    """,
    
    # Consultas para contenido sin TMDB
    "get_streams_without_tmdb": """
        SELECT * FROM streams 
        WHERE (tmdb_id IS NULL OR tmdb_id = 0) AND type IN (2, 3)
        ORDER BY added DESC 
        LIMIT %s
    """,
    
    "get_movies_without_tmdb": """
        SELECT * FROM streams 
        WHERE (tmdb_id IS NULL OR tmdb_id = 0) AND type = 2
        ORDER BY added DESC 
        LIMIT %s
    """,
    
    "get_series_without_tmdb": """
        SELECT * FROM streams_series 
        WHERE (tmdb_id IS NULL OR tmdb_id = 0)
        ORDER BY last_modified DESC 
        LIMIT %s
    """,
    
    # Consultas para actualización TMDB
    "update_stream_tmdb": """
        UPDATE streams 
        SET tmdb_id = %s, movie_properties = %s, year = %s, rating = %s, 
            updated = CURRENT_TIMESTAMP
        WHERE id = %s
    """,
    
    "update_series_tmdb": """
        UPDATE streams_series 
        SET tmdb_id = %s, plot = %s, cast = %s, rating = %s, 
            backdrop_path = %s, youtube_trailer = %s, year = %s,
            last_modified = UNIX_TIMESTAMP()
        WHERE id = %s
    """,
    
    # Consultas para inserción de nuevo contenido
    "insert_movie_stream": """
        INSERT INTO streams (type, category_id, stream_display_name, stream_source, 
                           stream_icon, movie_properties, tmdb_id, year, rating, 
                           added, updated)
        VALUES (2, %s, %s, %s, %s, %s, %s, %s, %s, UNIX_TIMESTAMP(), CURRENT_TIMESTAMP)
    """,
    
    "insert_series": """
        INSERT INTO streams_series (title, category_id, cover, cover_big, genre, 
                                  plot, cast, rating, director, release_date, 
                                  tmdb_id, year, last_modified)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, UNIX_TIMESTAMP())
    """,
    
    # Consultas para eliminación
    "delete_stream": """
        DELETE FROM streams WHERE id = %s
    """,
    
    "delete_series": """
        DELETE FROM streams_series WHERE id = %s
    """,
    
    # Consultas para análisis y reportes
    "get_popular_movies": """
        SELECT s.*, sc.category_name 
        FROM streams s 
        LEFT JOIN streams_categories sc ON FIND_IN_SET(sc.id, s.category_id)
        WHERE s.type = 2 AND s.rating > 0
        ORDER BY s.rating DESC 
        LIMIT %s
    """,
    
    "get_recent_movies": """
        SELECT s.*, sc.category_name 
        FROM streams s 
        LEFT JOIN streams_categories sc ON FIND_IN_SET(sc.id, s.category_id)
        WHERE s.type = 2 AND s.added > UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL %s DAY))
        ORDER BY s.added DESC 
        LIMIT %s
    """,
    
    "get_movies_by_year": """
        SELECT s.*, sc.category_name 
        FROM streams s 
        LEFT JOIN streams_categories sc ON FIND_IN_SET(sc.id, s.category_id)
        WHERE s.type = 2 AND s.year = %s
        ORDER BY s.rating DESC 
        LIMIT %s
    """,
    
    # Consultas para mantenimiento
    "get_broken_streams": """
        SELECT s.*, sc.category_name 
        FROM streams s 
        LEFT JOIN streams_categories sc ON FIND_IN_SET(sc.id, s.category_id)
        WHERE s.stream_source IS NULL OR s.stream_source = '' OR s.stream_source = '[]'
        ORDER BY s.added DESC 
        LIMIT %s
    """
}

# Configuración adicional para cache interno
CACHE_CONFIG = {
    "enabled": True,
    "default_ttl": 3600,  # 1 hora
    "max_size": 1000,
    "cleanup_interval": 300  # 5 minutos
}

# Para compatibilidad con el código existente
DATABASE_SCHEMA = {}
TABLE_CREATION_ORDER = []
