"""
IPTV XUI One Content Manager
============================

Sistema moderno de gestión de contenido para paneles IPTV XUI One.
Incluye integración con TMDB, parser M3U, interfaz gráfica moderna,
y análisis de popularidad.

Autor: Sistema de gestión IPTV
Versión: 1.0.0
"""

import asyncio
import tkinter as tk
from tkinter import messagebox
import sys
import os
import logging
from pathlib import Path

# Agregar el directorio raíz al path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.settings import Settings
from ui.main_window import MainWindow
from core.database_manager import DatabaseManager
from utils.logger import setup_logging

class IPTVManager:
    """Clase principal del gestor IPTV XUI One"""
    
    def __init__(self):
        self.settings = Settings()
        self.db_manager = None
        self.main_window = None
        self.logger = None
        
    async def initialize(self):
        """Inicializar la aplicación"""
        try:
            # Configurar logging
            self.logger = setup_logging()
            self.logger.info("Iniciando IPTV XUI One Content Manager")
            
            # Inicializar gestor de base de datos
            self.db_manager = DatabaseManager(self.settings)
            
            # Probar conexión a la base de datos
            if await self.db_manager.test_connection():
                self.logger.info("Conexión a base de datos exitosa")
                
                # Verificar que existan las tablas necesarias
                if await self.db_manager.verify_tables():
                    self.logger.info("Tablas de base de datos verificadas")
                else:
                    self.logger.error("Faltan tablas necesarias en la base de datos")
                    messagebox.showerror(
                        "Error de Base de Datos",
                        "La base de datos no contiene las tablas necesarias de XUI One.\n"
                        "Asegúrese de conectar a una base de datos XUI One válida."
                    )
                    return False
            else:
                self.logger.error("Error al conectar con la base de datos")
                messagebox.showerror(
                    "Error de Conexión",
                    "No se pudo conectar a la base de datos.\n"
                    "Verifique la configuración en el archivo .env"
                )
                return False
                
            return True
            
        except Exception as e:
            self.logger.error(f"Error al inicializar aplicación: {str(e)}")
            messagebox.showerror("Error", f"Error al inicializar: {str(e)}")
            return False
    
    def run(self):
        """Ejecutar la aplicación"""
        try:
            # Crear ventana principal
            root = tk.Tk()
            root.withdraw()  # Ocultar ventana inicial
            
            # Inicializar aplicación de forma asíncrona
            async def main():
                if await self.initialize():
                    # Mostrar ventana principal
                    self.main_window = MainWindow(root, self.settings, self.db_manager)
                    root.deiconify()  # Mostrar ventana
                    
                    # Iniciar loop de eventos
                    while True:
                        root.update()
                        await asyncio.sleep(0.01)
                else:
                    root.destroy()
            
            # Ejecutar en loop de eventos
            asyncio.run(main())
            
        except KeyboardInterrupt:
            self.logger.info("Aplicación cerrada por el usuario")
        except Exception as e:
            self.logger.error(f"Error en ejecución: {str(e)}")
            messagebox.showerror("Error Fatal", f"Error en ejecución: {str(e)}")
        finally:
            if self.db_manager:
                asyncio.run(self.db_manager.close())

def main():
    """Función principal"""
    try:
        app = IPTVManager()
        app.run()
    except Exception as e:
        logging.error(f"Error fatal: {str(e)}")
        messagebox.showerror("Error Fatal", f"Error fatal: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
