@echo off
echo ========================================
echo  IPTV XUI Manager - PHP Extensions Setup
echo ========================================
echo.

echo Checking PHP installation...
php --version
if %errorlevel% neq 0 (
    echo ERROR: PHP is not installed or not in PATH
    echo Please install PHP first: https://windows.php.net/download/
    pause
    exit /b 1
)

echo.
echo Checking current PHP configuration...
php --ini

echo.
echo Current PHP modules:
php -m

echo.
echo ========================================
echo  SOLUTION OPTIONS
echo ========================================
echo.
echo You have PHP 8.2.0 but missing MySQL extensions.
echo.
echo OPTION 1: Install XAMPP (Recommended)
echo - Download XAMPP from: https://www.apachefriends.org/
echo - Install XAMPP (includes PHP with all extensions)
echo - Use XAMPP's PHP instead of current PHP
echo.
echo OPTION 2: Download PHP with extensions
echo - Download PHP 8.2 Thread Safe from: https://windows.php.net/download/
echo - Choose "VS16 x64 Thread Safe" version
echo - Extract and replace current PHP installation
echo.
echo OPTION 3: Manual extension installation
echo - Download php_pdo_mysql.dll and php_mysqli.dll
echo - Place in PHP ext/ directory
echo - Create/edit php.ini file
echo.
echo ========================================
echo  QUICK XAMPP INSTALLATION
echo ========================================
echo.
echo If you want to install XAMPP:
echo 1. Download from: https://www.apachefriends.org/download.html
echo 2. Install XAMPP
echo 3. Add C:\xampp\php to your PATH
echo 4. Restart command prompt
echo 5. Run this script again
echo.

set /p choice="Do you want to open XAMPP download page? (y/n): "
if /i "%choice%"=="y" (
    start https://www.apachefriends.org/download.html
)

echo.
echo ========================================
echo  ALTERNATIVE: Use Online Version
echo ========================================
echo.
echo If you can't install PHP extensions locally,
echo you can upload the system to a web hosting
echo that already has PHP with MySQL support.
echo.
echo Most hosting providers include:
echo - PHP with PDO MySQL
echo - MySQLi extension
echo - All required extensions
echo.

pause
