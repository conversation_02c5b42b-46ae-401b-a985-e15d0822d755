<?php
/**
 * Hybrid Database Manager - Real XUI + Local Fallback
 * ===================================================
 * 
 * Intenta conectar a la base de datos XUI real primero,
 * si falla, usa la base de datos local como respaldo
 */

require_once __DIR__ . '/LocalDatabaseManager.php';

class HybridDatabaseManager {
    private $real_connection = null;
    private $local_manager = null;
    private $connection_type = 'none';
    private $last_error = null;
    private $cache = [];
    private $stats = [
        'queries_executed' => 0,
        'real_queries' => 0,
        'local_queries' => 0,
        'cache_hits' => 0
    ];

    // Configuración de tu base de datos XUI real
    private $real_config = [
        'host' => '**************',
        'port' => 3306,
        'database' => 'xui',
        'username' => 'infest84',
        'password' => 'GZM6hh12zHKgfg34FghtAQ84WxloPRX]SV$fP'
    ];

    public function __construct() {
        $this->initializeConnections();
    }

    private function initializeConnections() {
        // Intentar conexión real primero
        $this->tryRealConnection();
        
        // Siempre inicializar conexión local como respaldo
        try {
            $this->local_manager = new LocalDatabaseManager();
            if ($this->connection_type === 'none') {
                $this->connection_type = 'local';
            }
        } catch (Exception $e) {
            if ($this->connection_type === 'none') {
                throw new Exception("No se pudo conectar ni a base de datos real ni local: " . $e->getMessage());
            }
        }
    }

    private function tryRealConnection() {
        try {
            // Intentar conexión con timeout corto para no bloquear
            $dsn = sprintf(
                "mysql:host=%s;port=%d;dbname=%s;charset=utf8mb4",
                $this->real_config['host'],
                $this->real_config['port'],
                $this->real_config['database']
            );

            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_TIMEOUT => 5, // Timeout corto
                PDO::ATTR_EMULATE_PREPARES => false
            ];

            $this->real_connection = new PDO(
                $dsn, 
                $this->real_config['username'], 
                $this->real_config['password'], 
                $options
            );

            // Probar la conexión
            $this->real_connection->query("SELECT 1");
            $this->connection_type = 'real';
            
            error_log("✅ Conexión real a XUI establecida");
            
        } catch (Exception $e) {
            $this->last_error = $e->getMessage();
            $this->real_connection = null;
            error_log("❌ Conexión real falló: " . $e->getMessage());
        }
    }

    public function query($sql, $params = [], $cacheKey = null, $cacheTtl = null) {
        $this->stats['queries_executed']++;

        // Check cache first
        if ($cacheKey && isset($this->cache[$cacheKey])) {
            $cached = $this->cache[$cacheKey];
            if ($cached['expires'] > time()) {
                $this->stats['cache_hits']++;
                return $cached['data'];
            }
        }

        $result = null;

        // Intentar conexión real primero
        if ($this->real_connection) {
            try {
                $stmt = $this->real_connection->prepare($sql);
                $stmt->execute($params);
                $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
                $this->stats['real_queries']++;
                
                // Cache successful real queries
                if ($cacheKey) {
                    $this->cache[$cacheKey] = [
                        'data' => $result,
                        'expires' => time() + ($cacheTtl ?? 300),
                        'source' => 'real'
                    ];
                }
                
                return $result;
                
            } catch (Exception $e) {
                error_log("❌ Query real falló: " . $e->getMessage());
                // Si falla, intentar reconectar para próxima vez
                $this->real_connection = null;
                $this->tryRealConnection();
            }
        }

        // Fallback a base de datos local
        if ($this->local_manager) {
            try {
                $result = $this->local_manager->query($sql, $params, $cacheKey, $cacheTtl);
                $this->stats['local_queries']++;
                return $result;
            } catch (Exception $e) {
                throw new Exception("Error en ambas bases de datos: " . $e->getMessage());
            }
        }

        throw new Exception("No hay conexiones de base de datos disponibles");
    }

    public function execute($sql, $params = []) {
        $this->stats['queries_executed']++;

        // Solo ejecutar en base de datos real si está disponible
        if ($this->real_connection) {
            try {
                $stmt = $this->real_connection->prepare($sql);
                $stmt->execute($params);
                $this->stats['real_queries']++;
                return $stmt->rowCount();
            } catch (Exception $e) {
                error_log("❌ Execute real falló: " . $e->getMessage());
                $this->real_connection = null;
            }
        }

        // Para operaciones de escritura, usar local como respaldo
        if ($this->local_manager) {
            $this->stats['local_queries']++;
            return $this->local_manager->execute($sql, $params);
        }

        throw new Exception("No hay conexiones de base de datos disponibles para escritura");
    }

    // Métodos específicos para XUI One con adaptación automática

    public function getContentStats() {
        $cacheKey = 'content_stats_hybrid';
        
        // SQL compatible con ambas bases de datos
        $sql = "
            SELECT 
                COUNT(*) as total_streams,
                SUM(CASE WHEN type = 1 THEN 1 ELSE 0 END) as live_tv,
                SUM(CASE WHEN type = 2 THEN 1 ELSE 0 END) as movies,
                SUM(CASE WHEN type = 3 THEN 1 ELSE 0 END) as series,
                SUM(CASE WHEN type = 2 AND movie_symlink = 1 THEN 1 ELSE 0 END) as symlink_movies,
                SUM(CASE WHEN type = 2 AND direct_source = 1 THEN 1 ELSE 0 END) as direct_movies
            FROM streams
        ";
        
        $result = $this->query($sql, [], $cacheKey, 300);
        return $result[0] ?? [];
    }

    public function getQualityStats() {
        $cacheKey = 'quality_stats_hybrid';
        
        $sql = "
            SELECT 
                SUM(CASE WHEN stream_display_name LIKE '%4K%' OR stream_display_name LIKE '%2160p%' THEN 1 ELSE 0 END) as content_4k,
                SUM(CASE WHEN stream_display_name LIKE '%60fps%' OR stream_display_name LIKE '%60FPS%' THEN 1 ELSE 0 END) as content_60fps,
                SUM(CASE WHEN stream_display_name LIKE '%HDR%' THEN 1 ELSE 0 END) as content_hdr,
                SUM(CASE WHEN stream_display_name LIKE '%1080p%' OR stream_display_name LIKE '%FHD%' THEN 1 ELSE 0 END) as content_fhd,
                SUM(CASE WHEN stream_display_name LIKE '%720p%' OR stream_display_name LIKE '%HD%' THEN 1 ELSE 0 END) as content_hd
            FROM streams 
            WHERE type = 2
        ";
        
        $result = $this->query($sql, [], $cacheKey, 300);
        return $result[0] ?? [];
    }

    public function getRecentAdditions($limit = 10) {
        $sql = "
            SELECT s.*, sc.category_name 
            FROM streams s 
            LEFT JOIN streams_categories sc ON sc.id = 1
            WHERE s.type = 2 
            ORDER BY s.added DESC 
            LIMIT ?
        ";
        
        return $this->query($sql, [$limit]);
    }

    public function getPopularContent($limit = 10) {
        $sql = "
            SELECT s.*, sc.category_name 
            FROM streams s 
            LEFT JOIN streams_categories sc ON sc.id = 1
            WHERE s.type = 2 AND s.rating > 0
            ORDER BY s.rating DESC 
            LIMIT ?
        ";
        
        return $this->query($sql, [$limit]);
    }

    public function searchContent($searchTerm, $limit = 50, $offset = 0) {
        $searchPattern = '%' . $searchTerm . '%';
        $sql = "
            SELECT s.*, sc.category_name 
            FROM streams s 
            LEFT JOIN streams_categories sc ON sc.id = 1
            WHERE s.stream_display_name LIKE ? 
            ORDER BY s.added DESC 
            LIMIT ? OFFSET ?
        ";
        
        return $this->query($sql, [$searchPattern, $limit, $offset]);
    }

    public function get4KContent($limit = 50, $offset = 0) {
        $sql = "
            SELECT s.*, sc.category_name 
            FROM streams s 
            LEFT JOIN streams_categories sc ON sc.id = 1
            WHERE s.type = 2 
            AND (s.stream_display_name LIKE '%4K%' OR s.stream_display_name LIKE '%2160p%')
            ORDER BY s.added DESC 
            LIMIT ? OFFSET ?
        ";
        
        return $this->query($sql, [$limit, $offset]);
    }

    public function getCategories($type = null) {
        $sql = "SELECT * FROM streams_categories";
        $params = [];
        
        if ($type) {
            $sql .= " WHERE category_type = ?";
            $params[] = $type;
        }
        
        $sql .= " ORDER BY cat_order ASC, category_name ASC";
        
        return $this->query($sql, $params, 'categories_' . ($type ?? 'all'), 3600);
    }

    // Métodos de información del sistema

    public function getConnectionInfo() {
        return [
            'type' => $this->connection_type,
            'real_available' => $this->real_connection !== null,
            'local_available' => $this->local_manager !== null,
            'last_error' => $this->last_error,
            'stats' => $this->stats
        ];
    }

    public function testConnection() {
        if ($this->real_connection) {
            try {
                $this->real_connection->query("SELECT 1");
                return true;
            } catch (Exception $e) {
                $this->real_connection = null;
            }
        }
        
        if ($this->local_manager) {
            return $this->local_manager->testConnection();
        }
        
        return false;
    }

    public function getStats() {
        return $this->stats;
    }

    public function forceReconnect() {
        $this->real_connection = null;
        $this->tryRealConnection();
        return $this->connection_type;
    }

    // Delegación a local manager para métodos específicos
    public function __call($method, $args) {
        if ($this->local_manager && method_exists($this->local_manager, $method)) {
            return call_user_func_array([$this->local_manager, $method], $args);
        }
        
        throw new Exception("Método $method no encontrado");
    }
}
?>
