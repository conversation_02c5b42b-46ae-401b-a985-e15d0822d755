<?php
/**
 * Demo Interface - IPTV XUI Manager
 * =================================
 * 
 * Shows the interface without database connection
 * Use this to see how the system looks while setting up PHP extensions
 */
?>
<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IPTV XUI Manager - Demo Interface</title>
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        :root {
            --primary-color: #3b82f6;
            --secondary-color: #1e40af;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --dark-bg: #0f172a;
            --dark-surface: #1e293b;
            --dark-border: #334155;
            --text-primary: #f1f5f9;
            --text-secondary: #94a3b8;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--dark-bg);
            color: var(--text-primary);
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .header h1 {
            font-size: 3rem;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
        }

        .header p {
            font-size: 1.25rem;
            color: var(--text-secondary);
        }

        .status-card {
            background: var(--dark-surface);
            border: 1px solid var(--dark-border);
            border-radius: 1rem;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .status-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--dark-surface);
            border: 1px solid var(--dark-border);
            border-radius: 0.75rem;
            padding: 1.5rem;
            text-align: center;
        }

        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: var(--primary-color);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .action-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .action-card {
            background: var(--dark-surface);
            border: 1px solid var(--dark-border);
            border-radius: 0.75rem;
            padding: 2rem;
            text-align: center;
            transition: var(--transition);
            cursor: pointer;
        }

        .action-card:hover {
            border-color: var(--primary-color);
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
        }

        .action-icon {
            font-size: 3rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .action-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .action-description {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .alert {
            padding: 1rem 1.5rem;
            border-radius: 0.5rem;
            margin-bottom: 2rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .alert-warning {
            background: rgba(245, 158, 11, 0.1);
            border: 1px solid var(--warning-color);
            color: var(--warning-color);
        }

        .alert-info {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid var(--primary-color);
            color: var(--primary-color);
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            background: var(--primary-color);
            color: white;
            text-decoration: none;
            border-radius: 0.5rem;
            font-weight: 500;
            transition: var(--transition);
        }

        .btn:hover {
            background: var(--secondary-color);
            transform: translateY(-2px);
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 0.5rem 0;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .feature-list li i {
            color: var(--success-color);
            width: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-tv"></i> IPTV XUI Manager</h1>
            <p>Professional IPTV Content Management System</p>
        </div>

        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle"></i>
            <div>
                <strong>Demo Mode:</strong> This is the interface preview. To connect to your XUI database, you need to install PHP MySQL extensions.
                <br><a href="install-php-extensions.bat" style="color: inherit; text-decoration: underline;">Run Extension Installer</a>
            </div>
        </div>

        <div class="status-grid">
            <div class="stat-card">
                <div class="stat-icon"><i class="fas fa-film"></i></div>
                <div class="stat-number">12,847</div>
                <div class="stat-label">Total Movies</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon"><i class="fas fa-tv"></i></div>
                <div class="stat-number">3,421</div>
                <div class="stat-label">TV Series</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon"><i class="fas fa-broadcast-tower"></i></div>
                <div class="stat-number">856</div>
                <div class="stat-label">Live Channels</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon"><i class="fas fa-hdd"></i></div>
                <div class="stat-number">2,134</div>
                <div class="stat-label">4K Content</div>
            </div>
        </div>

        <div class="action-grid">
            <div class="action-card">
                <div class="action-icon"><i class="fas fa-upload"></i></div>
                <div class="action-title">Upload M3U Files</div>
                <div class="action-description">Import new content from M3U playlists with automatic TMDB matching</div>
            </div>
            <div class="action-card">
                <div class="action-icon"><i class="fas fa-search"></i></div>
                <div class="action-title">Browse Content</div>
                <div class="action-description">Search and manage your IPTV content library with advanced filters</div>
            </div>
            <div class="action-card">
                <div class="action-icon"><i class="fas fa-chart-bar"></i></div>
                <div class="action-title">Analytics</div>
                <div class="action-description">View detailed statistics and insights about your content</div>
            </div>
            <div class="action-card">
                <div class="action-icon"><i class="fas fa-cog"></i></div>
                <div class="action-title">Settings</div>
                <div class="action-description">Configure system settings and database connections</div>
            </div>
        </div>

        <div class="status-card">
            <div class="status-title">
                <i class="fas fa-list-check"></i>
                System Features
            </div>
            <ul class="feature-list">
                <li><i class="fas fa-check"></i> Direct XUI One database integration</li>
                <li><i class="fas fa-check"></i> TMDB metadata enrichment</li>
                <li><i class="fas fa-check"></i> 4K and 60fps content prioritization</li>
                <li><i class="fas fa-check"></i> Symlink content preservation</li>
                <li><i class="fas fa-check"></i> Duplicate content detection</li>
                <li><i class="fas fa-check"></i> Mass content management</li>
                <li><i class="fas fa-check"></i> Responsive web interface</li>
                <li><i class="fas fa-check"></i> Real-time statistics</li>
            </ul>
        </div>

        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i>
            <div>
                <strong>Next Steps:</strong> Install PHP MySQL extensions to connect to your XUI database.
                <br><a href="test-mysqli.php" class="btn" style="margin-top: 0.5rem;">
                    <i class="fas fa-database"></i> Test Database Connection
                </a>
            </div>
        </div>
    </div>
</body>
</html>
