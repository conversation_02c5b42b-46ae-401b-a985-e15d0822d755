# IPTV XUI One Content Manager

Un sistema moderno de gestión de contenido para paneles IPTV XUI One con interfaz gráfica avanzada.

## Características Principales

### 🎬 Gestión de Contenido
- **Integración con TMDB**: Comparación automática de contenido existente
- **Parser M3U**: Importación y análisis de listas de reproducción
- **Detección de Duplicados**: Identificación y eliminación automática
- **Gestión Completa**: Agregar, editar y eliminar contenido

### 📊 Análisis y Reportes
- **Gráficos de Popularidad**: Visualización de contenido más visto
- **Recomendaciones**: Sugerencias basadas en tendencias
- **Estadísticas**: Métricas detalladas del contenido

### 🎨 Interfaz Moderna
- **CustomTkinter**: Interfaz moderna y atractiva
- **Iconos y Animaciones**: Experiencia visual mejorada
- **Barras de Progreso**: Indicadores en tiempo real
- **Diseño Responsivo**: Adaptable a diferentes tamaños

### 🔐 Gestión de Sesiones
- **Autenticación**: Sistema de usuarios seguro
- **Preferencias**: Configuración personalizada
- **Persistencia**: Sesiones guardadas automáticamente

## Requisitos del Sistema

- Python 3.8 o superior
- MariaDB/MySQL
- Windows 10/11 (recomendado)

## Instalación

1. **Clonar el repositorio**
   ```bash
   git clone <repository-url>
   cd "1 INTENTO GESTOR XUI"
   ```

2. **Instalar dependencias**
   ```bash
   pip install -r requirements.txt
   ```

3. **Configurar base de datos**
   - Crear base de datos MariaDB/MySQL
## 🚀 Configuración Inicial

### 1. Configurar Base de Datos XUI One

**Opción A: Configurador Automático (Recomendado)**
```bash
python setup_database.py
```

**Opción B: Configuración Manual**
Edita el archivo `.env` con tus credenciales:

```env
# Database Configuration (XUI One Database)
DB_HOST=localhost
DB_PORT=3306
DB_NAME=xtream_codes
DB_USER=tu_usuario
DB_PASSWORD=tu_contraseña

# TMDB API Configuration
TMDB_API_KEY=201066b4b17391d478e55247f43eed64
```

### 2. Probar Conexión

```bash
python test_connection.py
```

### 3. Ejecutar la Aplicación

```bash
python main.py
```

## Configuración

### Variables de Entorno (.env)

```env
# Database Configuration (XUI One Database)
DB_HOST=localhost
DB_PORT=3306
DB_NAME=xtream_codes
DB_USER=root
DB_PASSWORD=your_password

# TMDB API Configuration
TMDB_API_KEY=201066b4b17391d478e55247f43eed64

# Application Settings
APP_NAME=IPTV XUI Manager
APP_VERSION=1.0.0
DEBUG=False
```

## Estructura del Proyecto

```
├── main.py                 # Punto de entrada principal
├── config/                 # Configuración
│   ├── database.py         # Configuración de BD
│   └── settings.py         # Configuración general
├── core/                   # Lógica principal
│   ├── database_manager.py # Gestor de BD
│   ├── tmdb_api.py         # Cliente TMDB
│   └── m3u_parser.py       # Parser M3U
├── ui/                     # Interfaz gráfica
│   ├── main_window.py      # Ventana principal
│   ├── components/         # Componentes UI
│   └── styles/             # Estilos y temas
├── models/                 # Modelos de datos
├── utils/                  # Utilidades
└── assets/                 # Recursos (iconos, imágenes)
```

## Uso

1. **Iniciar Aplicación**: Ejecutar `python main.py`
2. **Conectar BD**: Configurar conexión a MariaDB
3. **Importar M3U**: Cargar lista de reproducción
4. **Sincronizar TMDB**: Comparar con base de datos de películas
5. **Gestionar Contenido**: Agregar, editar, eliminar contenido
6. **Analizar Datos**: Ver gráficos y estadísticas

## Tecnologías Utilizadas

- **Python 3.8+**: Lenguaje principal
- **CustomTkinter**: Framework de interfaz moderna
- **MariaDB/MySQL**: Base de datos
- **TMDB API**: Metadatos de películas y series
- **Matplotlib**: Gráficos y visualizaciones
- **Pandas**: Análisis de datos
- **Asyncio**: Operaciones asíncronas

## Contribuir

1. Fork el proyecto
2. Crear rama feature (`git checkout -b feature/nueva-funcionalidad`)
3. Commit cambios (`git commit -m 'Agregar nueva funcionalidad'`)
4. Push a la rama (`git push origin feature/nueva-funcionalidad`)
5. Crear Pull Request

## Licencia

Este proyecto está bajo la licencia MIT. Ver archivo `LICENSE` para más detalles.

## Soporte

Para soporte técnico o reportar bugs, crear un issue en el repositorio.
