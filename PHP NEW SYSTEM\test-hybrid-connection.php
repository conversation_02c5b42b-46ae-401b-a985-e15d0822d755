<?php
/**
 * Test Hybrid Database Connection
 * ===============================
 * 
 * Prueba la conexión híbrida (real + local)
 */

echo "<h1>Prueba de Conexión Híbrida XUI</h1>\n";

try {
    require_once __DIR__ . '/core/HybridDatabaseManager.php';
    
    $db = new HybridDatabaseManager();
    
    echo "<h2>✅ Gestor Híbrido Inicializado</h2>\n";
    
    // Información de conexión
    $connection_info = $db->getConnectionInfo();
    echo "<h3>Estado de Conexión:</h3>\n";
    echo "<ul>\n";
    echo "<li><strong>Tipo Activo:</strong> " . ucfirst($connection_info['type']) . "</li>\n";
    echo "<li><strong>XUI Real Disponible:</strong> " . ($connection_info['real_available'] ? '✅ Sí' : '❌ No') . "</li>\n";
    echo "<li><strong>Local Disponible:</strong> " . ($connection_info['local_available'] ? '✅ Sí' : '❌ No') . "</li>\n";
    if ($connection_info['last_error']) {
        echo "<li><strong>Último Error:</strong> <span style='color: red;'>" . htmlspecialchars($connection_info['last_error']) . "</span></li>\n";
    }
    echo "</ul>\n";
    
    // Estadísticas de uso
    echo "<h3>Estadísticas de Uso:</h3>\n";
    $stats = $connection_info['stats'];
    echo "<ul>\n";
    echo "<li><strong>Total Consultas:</strong> " . $stats['queries_executed'] . "</li>\n";
    echo "<li><strong>Consultas Reales:</strong> " . $stats['real_queries'] . "</li>\n";
    echo "<li><strong>Consultas Locales:</strong> " . $stats['local_queries'] . "</li>\n";
    echo "<li><strong>Cache Hits:</strong> " . $stats['cache_hits'] . "</li>\n";
    echo "</ul>\n";
    
    // Probar consultas
    echo "<h2>📊 Prueba de Consultas</h2>\n";
    
    // Estadísticas de contenido
    echo "<h3>Estadísticas de Contenido:</h3>\n";
    $contentStats = $db->getContentStats();
    echo "<ul>\n";
    echo "<li><strong>Total Streams:</strong> " . number_format($contentStats['total_streams'] ?? 0) . "</li>\n";
    echo "<li><strong>Películas:</strong> " . number_format($contentStats['movies'] ?? 0) . "</li>\n";
    echo "<li><strong>Series:</strong> " . number_format($contentStats['series'] ?? 0) . "</li>\n";
    echo "<li><strong>TV en Vivo:</strong> " . number_format($contentStats['live_tv'] ?? 0) . "</li>\n";
    echo "<li><strong>Contenido Symlink:</strong> " . number_format($contentStats['symlink_movies'] ?? 0) . "</li>\n";
    echo "<li><strong>Contenido Direct Source:</strong> " . number_format($contentStats['direct_movies'] ?? 0) . "</li>\n";
    echo "</ul>\n";
    
    // Estadísticas de calidad
    echo "<h3>Estadísticas de Calidad:</h3>\n";
    $qualityStats = $db->getQualityStats();
    echo "<ul>\n";
    echo "<li><strong>Contenido 4K:</strong> " . number_format($qualityStats['content_4k'] ?? 0) . "</li>\n";
    echo "<li><strong>Contenido 60fps:</strong> " . number_format($qualityStats['content_60fps'] ?? 0) . "</li>\n";
    echo "<li><strong>Contenido HDR:</strong> " . number_format($qualityStats['content_hdr'] ?? 0) . "</li>\n";
    echo "<li><strong>Contenido FHD:</strong> " . number_format($qualityStats['content_fhd'] ?? 0) . "</li>\n";
    echo "<li><strong>Contenido HD:</strong> " . number_format($qualityStats['content_hd'] ?? 0) . "</li>\n";
    echo "</ul>\n";
    
    // Contenido reciente
    echo "<h3>Contenido Reciente (5 últimos):</h3>\n";
    $recent = $db->getRecentAdditions(5);
    echo "<ul>\n";
    foreach ($recent as $item) {
        echo "<li><strong>" . htmlspecialchars($item['stream_display_name']) . "</strong>";
        if (isset($item['added'])) {
            echo " - Agregado: " . date('d/m/Y H:i', $item['added']);
        }
        if (isset($item['year']) && $item['year']) {
            echo " | Año: " . $item['year'];
        }
        echo "</li>\n";
    }
    echo "</ul>\n";
    
    // Prueba de búsqueda
    echo "<h3>Prueba de Búsqueda ('Avatar'):</h3>\n";
    $searchResults = $db->searchContent('Avatar', 5);
    echo "<ul>\n";
    foreach ($searchResults as $item) {
        echo "<li><strong>" . htmlspecialchars($item['stream_display_name']) . "</strong>";
        if (isset($item['year']) && $item['year']) {
            echo " (" . $item['year'] . ")";
        }
        if (isset($item['rating']) && $item['rating'] > 0) {
            echo " - Rating: " . $item['rating'] . "/10";
        }
        echo "</li>\n";
    }
    echo "</ul>\n";
    
    // Contenido 4K
    echo "<h3>Contenido 4K (5 primeros):</h3>\n";
    $content4k = $db->get4KContent(5);
    echo "<ul>\n";
    foreach ($content4k as $item) {
        echo "<li><strong>" . htmlspecialchars($item['stream_display_name']) . "</strong>";
        if (isset($item['year']) && $item['year']) {
            echo " (" . $item['year'] . ")";
        }
        echo "</li>\n";
    }
    echo "</ul>\n";
    
    // Categorías
    echo "<h3>Categorías Disponibles:</h3>\n";
    $categories = $db->getCategories();
    echo "<ul>\n";
    foreach ($categories as $cat) {
        echo "<li><strong>" . htmlspecialchars($cat['category_name']) . "</strong> (" . $cat['category_type'] . ")</li>\n";
    }
    echo "</ul>\n";
    
    // Estadísticas finales
    $final_stats = $db->getStats();
    echo "<h2>📈 Estadísticas Finales</h2>\n";
    echo "<ul>\n";
    echo "<li><strong>Total Consultas Ejecutadas:</strong> " . $final_stats['queries_executed'] . "</li>\n";
    echo "<li><strong>Consultas a XUI Real:</strong> " . $final_stats['real_queries'] . "</li>\n";
    echo "<li><strong>Consultas Locales:</strong> " . $final_stats['local_queries'] . "</li>\n";
    echo "<li><strong>Cache Hits:</strong> " . $final_stats['cache_hits'] . "</li>\n";
    echo "</ul>\n";
    
    // Determinar fuente de datos
    $data_source = $final_stats['real_queries'] > 0 ? 'XUI Real' : 'Local';
    $source_color = $final_stats['real_queries'] > 0 ? 'green' : 'orange';
    
    echo "<h2 style='color: $source_color;'>🎯 Fuente de Datos: $data_source</h2>\n";
    
    if ($final_stats['real_queries'] > 0) {
        echo "<p style='color: green;'>✅ <strong>¡Excelente!</strong> El sistema está leyendo datos reales de tu base de datos XUI.</p>\n";
    } else {
        echo "<p style='color: orange;'>⚠️ <strong>Modo Fallback:</strong> El sistema está usando datos locales porque no pudo conectar a la base de datos XUI real.</p>\n";
        echo "<p><strong>Posibles causas:</strong></p>\n";
        echo "<ul>\n";
        echo "<li>Problemas de conectividad de red</li>\n";
        echo "<li>Firewall bloqueando puerto 3306</li>\n";
        echo "<li>Servidor XUI temporalmente no disponible</li>\n";
        echo "<li>Credenciales incorrectas</li>\n";
        echo "</ul>\n";
    }
    
    echo "<h3>Próximos Pasos:</h3>\n";
    echo "<ul>\n";
    echo "<li><a href='sistema-real.php' style='color: #3b82f6; text-decoration: none;'>🚀 Lanzar Sistema con Datos Reales</a></li>\n";
    echo "<li><a href='test-local-database.php' style='color: #10b981; text-decoration: none;'>🧪 Probar Solo Base Local</a></li>\n";
    echo "<li><a href='test-mysqli.php' style='color: #f59e0b; text-decoration: none;'>🔗 Probar Solo Conexión XUI</a></li>\n";
    echo "</ul>\n";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</h2>\n";
    echo "<p>Posibles soluciones:</p>\n";
    echo "<ul>\n";
    echo "<li><a href='create-local-database.php'>Crear base de datos local</a></li>\n";
    echo "<li>Verificar conexión de red</li>\n";
    echo "<li>Revisar credenciales de base de datos</li>\n";
    echo "</ul>\n";
}
?>
