<?php
/**
 * XUI Database Test - IPTV XUI One Content Manager
 * ===============================================
 * 
 * Specific test for XUI One database structure and content
 */

// Include path management
require_once __DIR__ . '/config/paths.php';

// Initialize session management
require_once getConfigPath('session.php');

// Include configuration
require_once getConfigPath('database.php');

// Test XUI One database structure
function testXUIDatabase() {
    try {
        $config = DB_CONFIG;
        $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}";
        
        $pdo = new PDO($dsn, $config['username'], $config['password'], $config['options']);
        
        $results = [
            'connection' => true,
            'tables' => [],
            'content_stats' => [],
            'sample_data' => []
        ];
        
        // Check XUI One tables
        $xuiTables = [
            'streams' => 'Main content streams table',
            'streams_categories' => 'Content categories',
            'streams_series' => 'TV series information',
            'streams_episodes' => 'TV episodes',
            'users' => 'User accounts',
            'bouquets' => 'Channel bouquets',
            'settings' => 'System settings'
        ];
        
        foreach ($xuiTables as $table => $description) {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            $exists = $stmt->fetch() !== false;
            
            $results['tables'][$table] = [
                'exists' => $exists,
                'description' => $description,
                'count' => 0
            ];
            
            if ($exists) {
                try {
                    $countStmt = $pdo->query("SELECT COUNT(*) as count FROM `$table`");
                    $count = $countStmt->fetch();
                    $results['tables'][$table]['count'] = $count['count'];
                } catch (Exception $e) {
                    $results['tables'][$table]['error'] = $e->getMessage();
                }
            }
        }
        
        // Get content statistics if streams table exists
        if ($results['tables']['streams']['exists']) {
            try {
                // Total content by type
                $stmt = $pdo->query("
                    SELECT 
                        type,
                        COUNT(*) as count,
                        CASE 
                            WHEN type = 1 THEN 'Live TV'
                            WHEN type = 2 THEN 'Movies'
                            WHEN type = 3 THEN 'Series'
                            ELSE 'Other'
                        END as type_name
                    FROM streams 
                    GROUP BY type
                    ORDER BY type
                ");
                $results['content_stats']['by_type'] = $stmt->fetchAll();
                
                // Quality analysis
                $stmt = $pdo->query("
                    SELECT 
                        SUM(CASE WHEN stream_display_name LIKE '%4K%' OR stream_display_name LIKE '%2160p%' THEN 1 ELSE 0 END) as content_4k,
                        SUM(CASE WHEN stream_display_name LIKE '%60fps%' OR stream_display_name LIKE '%60FPS%' THEN 1 ELSE 0 END) as content_60fps,
                        SUM(CASE WHEN stream_display_name LIKE '%HDR%' THEN 1 ELSE 0 END) as content_hdr,
                        SUM(CASE WHEN stream_display_name LIKE '%1080p%' OR stream_display_name LIKE '%FHD%' THEN 1 ELSE 0 END) as content_fhd,
                        SUM(CASE WHEN stream_display_name LIKE '%720p%' OR stream_display_name LIKE '%HD%' THEN 1 ELSE 0 END) as content_hd,
                        SUM(CASE WHEN type = 2 AND movie_symlink = 1 THEN 1 ELSE 0 END) as symlink_movies,
                        SUM(CASE WHEN type = 2 AND direct_source = 1 THEN 1 ELSE 0 END) as direct_movies
                    FROM streams
                ");
                $results['content_stats']['quality'] = $stmt->fetch();
                
                // Sample content
                $stmt = $pdo->query("
                    SELECT 
                        id,
                        stream_display_name,
                        type,
                        category_id,
                        added,
                        movie_symlink,
                        direct_source,
                        tmdb_id
                    FROM streams 
                    ORDER BY added DESC 
                    LIMIT 10
                ");
                $results['sample_data']['recent_content'] = $stmt->fetchAll();
                
            } catch (Exception $e) {
                $results['content_stats']['error'] = $e->getMessage();
            }
        }
        
        // Get categories if table exists
        if ($results['tables']['streams_categories']['exists']) {
            try {
                $stmt = $pdo->query("
                    SELECT 
                        id,
                        category_name,
                        category_type,
                        parent_id
                    FROM streams_categories 
                    ORDER BY category_name 
                    LIMIT 20
                ");
                $results['sample_data']['categories'] = $stmt->fetchAll();
                
            } catch (Exception $e) {
                $results['sample_data']['categories_error'] = $e->getMessage();
            }
        }
        
        return $results;
        
    } catch (Exception $e) {
        return [
            'connection' => false,
            'error' => $e->getMessage()
        ];
    }
}

// Run the test
$xuiTest = testXUIDatabase();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XUI Database Test - IPTV Manager</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #0f172a;
            color: #f1f5f9;
            margin: 0;
            padding: 2rem;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .card {
            background: #1e293b;
            border: 1px solid #334155;
            border-radius: 1rem;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .success {
            background: rgba(16, 185, 129, 0.1);
            border-color: #10b981;
        }
        .error {
            background: rgba(239, 68, 68, 0.1);
            border-color: #ef4444;
        }
        .info {
            background: rgba(59, 130, 246, 0.1);
            border-color: #3b82f6;
        }
        .warning {
            background: rgba(245, 158, 11, 0.1);
            border-color: #f59e0b;
        }
        pre {
            background: #0f172a;
            padding: 1rem;
            border-radius: 0.5rem;
            overflow-x: auto;
            border: 1px solid #334155;
            white-space: pre-wrap;
        }
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #334155;
        }
        .test-item:last-child {
            border-bottom: none;
        }
        .status {
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: bold;
        }
        .status-ok { background: #10b981; color: white; }
        .status-error { background: #ef4444; color: white; }
        .status-warning { background: #f59e0b; color: white; }
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #3b82f6;
            color: white;
            text-decoration: none;
            border-radius: 0.5rem;
            margin: 0.5rem 0.5rem 0.5rem 0;
        }
        .btn:hover {
            background: #1e40af;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        th, td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #334155;
        }
        th {
            background: #0f172a;
            font-weight: 600;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }
        .metric {
            background: #0f172a;
            padding: 1rem;
            border-radius: 0.5rem;
            text-align: center;
        }
        .metric-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color, #3b82f6);
        }
        .metric-label {
            font-size: 0.875rem;
            color: #94a3b8;
            margin-top: 0.25rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗄️ XUI Database Test - IPTV Manager</h1>
        
        <!-- Connection Status -->
        <div class="card <?= $xuiTest['connection'] ? 'success' : 'error' ?>">
            <h3>Database Connection</h3>
            <?php if ($xuiTest['connection']): ?>
                <p>✅ Successfully connected to XUI One database</p>
                <div class="test-item">
                    <span><strong>Host:</strong> <?= DB_CONFIG['host'] ?></span>
                </div>
                <div class="test-item">
                    <span><strong>Database:</strong> <?= DB_CONFIG['database'] ?></span>
                </div>
            <?php else: ?>
                <p>❌ Failed to connect to database</p>
                <pre><?= htmlspecialchars($xuiTest['error']) ?></pre>
            <?php endif; ?>
        </div>

        <?php if ($xuiTest['connection']): ?>
        
        <!-- Tables Status -->
        <div class="card">
            <h3>XUI One Tables</h3>
            <?php foreach ($xuiTest['tables'] as $table => $info): ?>
            <div class="test-item">
                <span>
                    <strong><?= $table ?>:</strong> <?= $info['description'] ?>
                    <?php if ($info['exists']): ?>
                        <small>(<?= number_format($info['count']) ?> records)</small>
                    <?php endif; ?>
                </span>
                <span class="status <?= $info['exists'] ? 'status-ok' : 'status-error' ?>">
                    <?= $info['exists'] ? 'EXISTS' : 'NOT FOUND' ?>
                </span>
            </div>
            <?php endforeach; ?>
        </div>

        <!-- Content Statistics -->
        <?php if (isset($xuiTest['content_stats']['by_type'])): ?>
        <div class="card info">
            <h3>Content Statistics</h3>
            
            <h4>Content by Type</h4>
            <table>
                <thead>
                    <tr>
                        <th>Type</th>
                        <th>Count</th>
                        <th>Description</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($xuiTest['content_stats']['by_type'] as $type): ?>
                    <tr>
                        <td><?= $type['type'] ?></td>
                        <td><?= number_format($type['count']) ?></td>
                        <td><?= $type['type_name'] ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            
            <?php if (isset($xuiTest['content_stats']['quality'])): ?>
            <h4>Quality Analysis</h4>
            <div class="grid">
                <div class="metric">
                    <div class="metric-value"><?= number_format($xuiTest['content_stats']['quality']['content_4k']) ?></div>
                    <div class="metric-label">4K Content</div>
                </div>
                <div class="metric">
                    <div class="metric-value"><?= number_format($xuiTest['content_stats']['quality']['content_60fps']) ?></div>
                    <div class="metric-label">60fps Content</div>
                </div>
                <div class="metric">
                    <div class="metric-value"><?= number_format($xuiTest['content_stats']['quality']['content_hdr']) ?></div>
                    <div class="metric-label">HDR Content</div>
                </div>
                <div class="metric">
                    <div class="metric-value"><?= number_format($xuiTest['content_stats']['quality']['content_fhd']) ?></div>
                    <div class="metric-label">Full HD</div>
                </div>
                <div class="metric">
                    <div class="metric-value"><?= number_format($xuiTest['content_stats']['quality']['symlink_movies']) ?></div>
                    <div class="metric-label">Symlink Movies</div>
                </div>
                <div class="metric">
                    <div class="metric-value"><?= number_format($xuiTest['content_stats']['quality']['direct_movies']) ?></div>
                    <div class="metric-label">Direct Movies</div>
                </div>
            </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>

        <!-- Sample Content -->
        <?php if (isset($xuiTest['sample_data']['recent_content'])): ?>
        <div class="card">
            <h3>Recent Content (Sample)</h3>
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Title</th>
                        <th>Type</th>
                        <th>Symlink</th>
                        <th>TMDB ID</th>
                        <th>Added</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($xuiTest['sample_data']['recent_content'] as $content): ?>
                    <tr>
                        <td><?= $content['id'] ?></td>
                        <td><?= htmlspecialchars(substr($content['stream_display_name'], 0, 50)) ?><?= strlen($content['stream_display_name']) > 50 ? '...' : '' ?></td>
                        <td><?= $content['type'] == 1 ? 'Live' : ($content['type'] == 2 ? 'Movie' : 'Series') ?></td>
                        <td><?= $content['movie_symlink'] ? '✅' : '❌' ?></td>
                        <td><?= $content['tmdb_id'] ?: 'N/A' ?></td>
                        <td><?= date('Y-m-d H:i', $content['added']) ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>

        <!-- Categories -->
        <?php if (isset($xuiTest['sample_data']['categories'])): ?>
        <div class="card">
            <h3>Categories (Sample)</h3>
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Type</th>
                        <th>Parent ID</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($xuiTest['sample_data']['categories'] as $category): ?>
                    <tr>
                        <td><?= $category['id'] ?></td>
                        <td><?= htmlspecialchars($category['category_name']) ?></td>
                        <td><?= $category['category_type'] ?: 'N/A' ?></td>
                        <td><?= $category['parent_id'] ?: 'N/A' ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>

        <?php endif; ?>

        <!-- Actions -->
        <div class="card">
            <h3>Test Actions</h3>
            <a href="?refresh=1" class="btn">🔄 Refresh Test</a>
            <a href="database-test.php" class="btn">🔍 Basic DB Test</a>
            <a href="system-check.php" class="btn">🔧 System Check</a>
            <a href="index.php" class="btn">🏠 Go to Main App</a>
        </div>
    </div>
</body>
</html>
