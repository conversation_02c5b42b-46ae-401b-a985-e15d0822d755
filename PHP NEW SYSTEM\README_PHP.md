# 🎬 IPTV XUI One Content Manager - PHP Edition

## 🚀 Modern Web-Based IPTV Management System

A comprehensive, modern web application for managing IPTV XUI One content with enhanced UI/UX, real-time operations, and advanced features. This is the **complete PHP migration** of the original Python system with significant improvements and a modern web interface.

### ✨ Key Features

#### 🎯 Core Functionality
- **M3U Upload & Processing**: Drag & drop M3U files with real-time parsing and batch processing
- **TMDB Integration**: Automatic metadata enrichment with 154,725+ content items support
- **Content Management**: Smart duplicate detection with symlink/direct_source prioritization
- **Export System**: Multiple format exports (M3U, TXT, JSON, CSV) with preview and filtering
- **Real-time Analytics**: Live statistics, charts, and content analysis dashboard
- **Advanced Filtering**: Search, categorize, and filter content by quality, type, and source

#### 🎨 Modern Interface
- **Responsive Design**: Fully responsive - works perfectly on desktop, tablet, and mobile
- **Dark/Light Mode**: Seamless theme switching with system preference detection
- **Animated Components**: Smooth transitions, loading animations, and micro-interactions
- **Interactive Tables**: Sortable, filterable, searchable content with pagination
- **Real-time Progress**: Live progress tracking for all operations
- **Drag & Drop**: Intuitive file upload with validation and preview

#### ⚡ Performance Features
- **AJAX Operations**: Single-page application feel with no page reloads
- **Batch Processing**: Optimized for large datasets (100+ items per batch)
- **Progress Tracking**: Real-time progress bars and status updates
- **Smart Caching**: Intelligent caching for TMDB data and database queries
- **Database Optimization**: Connection pooling, prepared statements, and query optimization
- **Memory Management**: Efficient memory usage for large M3U files (50MB+ support)

### 🛠️ Technology Stack

- **Backend**: PHP 8+ with PDO for MySQL connectivity
- **Frontend**: Modern HTML5, CSS3, JavaScript (ES6+) with Chart.js
- **Database**: MySQL/MariaDB (XUI One compatible)
- **API**: RESTful endpoints for all operations
- **Styling**: Custom CSS with CSS Grid/Flexbox and CSS Variables
- **Icons**: Font Awesome 6.4.0 for comprehensive iconography

### 📁 Project Structure

```
PHP NEW SYSTEM/
├── 📁 config/
│   ├── database.php          # Database configuration and SQL queries
│   ├── settings.php          # Application settings and preferences
│   └── tmdb_config.php       # TMDB API configuration
├── 📁 core/
│   ├── DatabaseManager.php   # Database operations with caching
│   ├── M3UParser.php         # M3U file processing and parsing
│   ├── TMDBClient.php        # TMDB API client with rate limiting
│   └── ContentManager.php    # Content management orchestration
├── 📁 public/
│   ├── index.php             # Main application entry point
│   ├── 📁 assets/
│   │   ├── css/              # Modern CSS with theming support
│   │   ├── js/               # JavaScript utilities and components
│   │   └── images/           # Images and icons
│   └── 📁 pages/
│       ├── dashboard.php     # Main dashboard with statistics
│       ├── content-browser.php # Content exploration and management
│       ├── m3u-manager.php   # M3U upload and processing
│       ├── analytics.php     # Advanced analytics and charts
│       └── settings.php      # Configuration management
├── 📁 api/
│   ├── upload-m3u.php        # M3U upload API endpoint
│   ├── detect-missing.php    # Missing content detection API
│   └── export-data.php       # Data export API
├── 📁 includes/
│   ├── header.php            # Common header components
│   ├── sidebar.php           # Navigation sidebar
│   └── footer.php            # Common footer
├── 📁 uploads/               # Temporary file storage
├── 📁 logs/                  # Application logs
├── composer.json             # PHP dependencies
├── INSTALLATION.md           # Detailed installation guide
└── README_PHP.md            # This file
```

### 🎯 Migration from Python

This PHP version maintains all functionality from the original Python system while adding:

1. **Enhanced UI/UX**: Modern web interface with animations and responsive design
2. **Better Performance**: Optimized for web environments with caching and batch processing
3. **Cross-platform Access**: Available from any device with a browser
4. **Real-time Features**: Live updates and progress tracking
5. **Improved Customization**: Easy theme and layout modifications
6. **API-First Design**: RESTful APIs for all operations
7. **Better Error Handling**: Comprehensive error reporting and logging

### 📊 Content Management Rules

#### 🔗 Symlink Content (PROTECTED)
- **4K Content**: Maximum priority (155 items detected in original system)
- **60fps Content**: High priority (28 items detected in original system)
- **All Symlink**: Protected from deletion (5,785 items in original system)

#### 📁 Direct Source Content (MANAGED)
- **Duplicates**: Smart detection and management recommendations
- **Quality Priority**: Automatic quality-based prioritization
- **User-Controlled**: Manual selection for deletion to prevent errors

### 🚀 Quick Start

1. **Prerequisites**: PHP 8+, MySQL/MariaDB, Web Server
2. **Installation**: Follow the detailed [INSTALLATION.md](INSTALLATION.md) guide
3. **Configuration**: Set up database credentials and TMDB API key
4. **Launch**: Access via web browser at your configured domain
5. **Import**: Upload M3U files using the drag & drop interface

### 🎨 Visual Features

#### Dashboard
- **Real-time Statistics**: Animated counters and progress indicators
- **Interactive Charts**: Content distribution with Chart.js
- **Quick Actions**: One-click access to common operations
- **Recent Activity**: Latest additions and popular content

#### Content Browser
- **Grid/List Views**: Toggle between visual layouts
- **Advanced Filtering**: Filter by quality, type, source, and more
- **Search**: Real-time search with debounced input
- **Bulk Operations**: Select multiple items for batch actions

#### M3U Manager
- **Drag & Drop Upload**: Intuitive file upload with validation
- **Real-time Progress**: Live progress bars and statistics
- **Processing Options**: Configurable TMDB enrichment and duplicate handling
- **Results Summary**: Detailed processing results with export options

#### Analytics
- **Interactive Charts**: Multiple chart types (pie, bar, line, doughnut)
- **Content Insights**: AI-powered recommendations and insights
- **Quality Analysis**: Detailed quality distribution statistics
- **Growth Trends**: Historical content growth visualization

### 🔧 Development Features

- **PSR-4 Autoloading**: Organized class structure following PHP standards
- **RESTful API**: Clean, documented API endpoints
- **Responsive CSS**: Mobile-first approach with CSS Grid/Flexbox
- **Progressive Enhancement**: Works without JavaScript as fallback
- **Security**: Input validation, SQL injection prevention, CSRF protection
- **Logging**: Comprehensive logging system for debugging and monitoring

### 🔐 Security Features

- **Input Validation**: All user inputs are validated and sanitized
- **SQL Injection Prevention**: Prepared statements for all database queries
- **CSRF Protection**: Cross-site request forgery protection
- **File Upload Security**: Strict file type and size validation
- **Session Management**: Secure session handling with timeouts
- **Error Handling**: Safe error messages without information disclosure

### 📈 Performance Optimizations

- **Database Indexing**: Optimized indexes for common queries
- **Query Caching**: Smart caching for frequently accessed data
- **Batch Processing**: Efficient handling of large datasets
- **Memory Management**: Optimized memory usage for large files
- **Compression**: Gzip compression for faster page loads
- **CDN Ready**: Static assets can be served from CDN

### 🌐 Browser Support

- **Modern Browsers**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **Mobile Browsers**: iOS Safari 14+, Chrome Mobile 90+
- **Progressive Enhancement**: Basic functionality works on older browsers
- **Responsive Design**: Optimized for all screen sizes

### 📞 Support & Documentation

- **Installation Guide**: Comprehensive [INSTALLATION.md](INSTALLATION.md)
- **API Documentation**: Inline code documentation
- **Configuration**: Detailed configuration options in settings files
- **Troubleshooting**: Common issues and solutions in installation guide
- **Community**: GitHub Issues for bug reports and feature requests

### 🎉 What's New in PHP Edition

#### Compared to Python Version:
✅ **Web-based interface** (vs desktop application)  
✅ **Real-time progress tracking** (vs batch processing only)  
✅ **Responsive design** (vs fixed desktop layout)  
✅ **API endpoints** (vs direct database access only)  
✅ **Theme switching** (vs single theme)  
✅ **Advanced analytics** (vs basic statistics)  
✅ **Drag & drop uploads** (vs file picker only)  
✅ **Interactive charts** (vs static reports)  
✅ **Multi-format exports** (vs limited export options)  
✅ **Cross-platform access** (vs Windows/Linux only)  

### 🚧 Roadmap

- [ ] **WebSocket Integration**: Real-time updates without page refresh
- [ ] **User Management**: Multi-user support with role-based access
- [ ] **Automated Scheduling**: Scheduled content processing and cleanup
- [ ] **Advanced Reporting**: PDF reports and email notifications
- [ ] **Plugin System**: Extensible architecture for custom features
- [ ] **Mobile App**: Native mobile application for iOS/Android
- [ ] **Docker Support**: Containerized deployment options
- [ ] **Cloud Integration**: Support for cloud storage providers

---

**🎬 Ready to revolutionize your IPTV content management with a modern web interface!**

For installation instructions, see [INSTALLATION.md](INSTALLATION.md)  
For the original Python version, see [README.md](README.md)
