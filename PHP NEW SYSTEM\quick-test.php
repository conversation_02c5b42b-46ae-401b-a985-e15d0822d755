<?php
/**
 * Quick Test - IPTV XUI One Content Manager
 * ========================================
 * 
 * Quick test to verify database connection and basic functionality
 */

// Include path management
require_once __DIR__ . '/config/paths.php';

// Initialize session management
require_once getConfigPath('session.php');

// Include configuration
require_once getConfigPath('database.php');

// Test with SimpleDatabaseManager
try {
    require_once getCorePath('SimpleDatabaseManager.php');
    
    echo "<h2>🧪 Quick Database Test</h2>";
    
    $db = new SimpleDatabaseManager();
    
    echo "<p>✅ Database connection established</p>";
    
    // Test connection
    if ($db->testConnection()) {
        echo "<p>✅ Connection test passed</p>";
    } else {
        echo "<p>❌ Connection test failed</p>";
    }
    
    // Test table validation
    if ($db->validateTables()) {
        echo "<p>✅ Required tables exist</p>";
    } else {
        echo "<p>⚠️ Some required tables missing</p>";
    }
    
    // Get basic stats
    $stats = $db->getContentStats();
    echo "<h3>📊 Content Statistics:</h3>";
    echo "<ul>";
    echo "<li>Total Streams: " . number_format($stats['total_streams']) . "</li>";
    echo "<li>Movies: " . number_format($stats['movies']) . "</li>";
    echo "<li>Live TV: " . number_format($stats['live_tv']) . "</li>";
    echo "<li>Series: " . number_format($stats['series']) . "</li>";
    echo "<li>Symlink Movies: " . number_format($stats['symlink_movies']) . "</li>";
    echo "<li>Direct Movies: " . number_format($stats['direct_movies']) . "</li>";
    echo "</ul>";
    
    // Get quality stats
    $qualityStats = $db->getQualityStats();
    echo "<h3>🎬 Quality Statistics:</h3>";
    echo "<ul>";
    echo "<li>4K Content: " . number_format($qualityStats['content_4k']) . "</li>";
    echo "<li>60fps Content: " . number_format($qualityStats['content_60fps']) . "</li>";
    echo "<li>HDR Content: " . number_format($qualityStats['content_hdr']) . "</li>";
    echo "<li>Full HD: " . number_format($qualityStats['content_fhd']) . "</li>";
    echo "<li>HD: " . number_format($qualityStats['content_hd']) . "</li>";
    echo "</ul>";
    
    // Get recent content
    $recentContent = $db->getRecentContent(5);
    echo "<h3>📅 Recent Content:</h3>";
    if (!empty($recentContent)) {
        echo "<ul>";
        foreach ($recentContent as $content) {
            $title = htmlspecialchars(substr($content['stream_display_name'], 0, 50));
            $type = $content['type'] == 1 ? 'Live' : ($content['type'] == 2 ? 'Movie' : 'Series');
            $symlink = $content['movie_symlink'] ? '🔗' : '📁';
            echo "<li>$symlink $title ($type)</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>No recent content found</p>";
    }
    
    echo "<h3>🎯 System Status:</h3>";
    echo "<p>✅ All systems operational</p>";
    echo "<p>✅ No long threads detected</p>";
    echo "<p>✅ Database optimized for performance</p>";
    
    echo "<hr>";
    echo "<p><a href='index.php' style='background: #3b82f6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🏠 Go to Main Application</a></p>";
    echo "<p><a href='system-check.php' style='background: #10b981; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔍 Full System Check</a></p>";
    
} catch (Exception $e) {
    echo "<h2>❌ Test Failed</h2>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>Database Host:</strong> " . DB_CONFIG['host'] . "</p>";
    echo "<p><strong>Database Name:</strong> " . DB_CONFIG['database'] . "</p>";
    echo "<p><strong>Database User:</strong> " . DB_CONFIG['username'] . "</p>";
    
    echo "<h3>🔧 Troubleshooting:</h3>";
    echo "<ul>";
    echo "<li>Check database credentials in .env file</li>";
    echo "<li>Verify database server is running</li>";
    echo "<li>Ensure database user has proper permissions</li>";
    echo "<li>Check if XUI One database structure exists</li>";
    echo "</ul>";
    
    echo "<hr>";
    echo "<p><a href='database-test.php' style='background: #ef4444; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔍 Detailed Database Test</a></p>";
}

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Test - IPTV Manager</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #0f172a;
            color: #f1f5f9;
            margin: 0;
            padding: 2rem;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: #1e293b;
            padding: 2rem;
            border-radius: 1rem;
            border: 1px solid #334155;
        }
        h2, h3 {
            color: #3b82f6;
        }
        ul {
            background: #0f172a;
            padding: 1rem;
            border-radius: 0.5rem;
            border: 1px solid #334155;
        }
        hr {
            border: none;
            height: 1px;
            background: #334155;
            margin: 2rem 0;
        }
        a {
            display: inline-block;
            margin: 0.5rem 0.5rem 0.5rem 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 IPTV XUI One Content Manager</h1>
        <!-- PHP content is echoed above -->
    </div>
</body>
</html>
