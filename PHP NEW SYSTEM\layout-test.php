<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Layout Test - IPTV Manager</title>
    <style>
        :root {
            --dark-bg: #0f172a;
            --dark-surface: #1e293b;
            --dark-border: #334155;
            --primary-color: #3b82f6;
            --secondary-color: #8b5cf6;
            --text-primary: #f1f5f9;
            --text-secondary: #94a3b8;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --transition: all 0.3s ease;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--dark-bg);
            color: var(--text-primary);
            margin: 0;
            padding: 2rem;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        h1, h2 {
            color: var(--primary-color);
            margin-bottom: 2rem;
        }

        /* Statistics Cards */
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1.5rem;
            margin-bottom: 3rem;
        }

        .stat-card {
            background: linear-gradient(135deg, var(--dark-surface), var(--dark-bg));
            border: 1px solid var(--dark-border);
            border-radius: 1rem;
            padding: 1.5rem;
            position: relative;
            overflow: hidden;
            transition: var(--transition);
            min-height: 140px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .stat-title {
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
            flex-shrink: 0;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            line-height: 1;
        }

        .stat-trend {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        /* Quick Actions */
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .action-card {
            background: var(--dark-surface);
            border: 1px solid var(--dark-border);
            border-radius: 1rem;
            padding: 1.5rem;
            text-align: center;
            transition: var(--transition);
            cursor: pointer;
            text-decoration: none;
            color: inherit;
            min-height: 120px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .action-card:hover {
            transform: translateY(-2px);
            border-color: var(--primary-color);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
        }

        .action-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
            color: white;
            margin: 0 auto 0.75rem;
            flex-shrink: 0;
        }

        .action-title {
            font-weight: 600;
            margin-bottom: 0.25rem;
            font-size: 0.95rem;
        }

        .action-description {
            font-size: 0.8rem;
            color: var(--text-secondary);
            line-height: 1.3;
        }

        /* Responsive */
        @media (max-width: 1200px) {
            .dashboard-grid,
            .quick-actions {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .dashboard-grid,
            .quick-actions {
                grid-template-columns: 1fr;
            }
        }

        .demo-note {
            background: var(--dark-surface);
            border: 1px solid var(--dark-border);
            border-radius: 1rem;
            padding: 1.5rem;
            margin-bottom: 2rem;
            text-align: center;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <h1>🎬 Layout Test - IPTV Manager</h1>
        
        <div class="demo-note">
            <h3>✅ Horizontal Layout Test</h3>
            <p>This page shows how the cards should look horizontally aligned</p>
        </div>

        <h2>📊 Statistics Cards (Horizontal)</h2>
        <div class="dashboard-grid">
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-title">Total Streams</div>
                    <div class="stat-icon" style="background: rgba(59, 130, 246, 0.1); color: var(--primary-color);">
                        <i class="fas fa-film"></i>
                    </div>
                </div>
                <div class="stat-value">12,543</div>
                <div class="stat-trend">
                    <i class="fas fa-arrow-up"></i>
                    <span>All content types</span>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-title">Movies</div>
                    <div class="stat-icon" style="background: rgba(16, 185, 129, 0.1); color: var(--success-color);">
                        <i class="fas fa-video"></i>
                    </div>
                </div>
                <div class="stat-value">8,234</div>
                <div class="stat-trend">
                    <i class="fas fa-link"></i>
                    <span>2,156 symlink</span>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-title">4K Content</div>
                    <div class="stat-icon" style="background: rgba(245, 158, 11, 0.1); color: var(--warning-color);">
                        <i class="fas fa-crown"></i>
                    </div>
                </div>
                <div class="stat-value">1,456</div>
                <div class="stat-trend">
                    <i class="fas fa-shield-alt"></i>
                    <span>Protected content</span>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-title">60fps Content</div>
                    <div class="stat-icon" style="background: rgba(139, 92, 246, 0.1); color: #8b5cf6;">
                        <i class="fas fa-tachometer-alt"></i>
                    </div>
                </div>
                <div class="stat-value">892</div>
                <div class="stat-trend">
                    <i class="fas fa-bolt"></i>
                    <span>High framerate</span>
                </div>
            </div>
        </div>

        <h2>🚀 Quick Actions (Horizontal)</h2>
        <div class="quick-actions">
            <a href="#" class="action-card">
                <div class="action-icon">
                    <i class="fas fa-upload"></i>
                </div>
                <div class="action-title">Upload M3U</div>
                <div class="action-description">Import new content from M3U files</div>
            </a>
            
            <a href="#" class="action-card">
                <div class="action-icon">
                    <i class="fas fa-search"></i>
                </div>
                <div class="action-title">Browse Content</div>
                <div class="action-description">Explore and manage your content</div>
            </a>
            
            <a href="#" class="action-card">
                <div class="action-icon">
                    <i class="fas fa-chart-bar"></i>
                </div>
                <div class="action-title">View Analytics</div>
                <div class="action-description">Detailed content statistics</div>
            </a>
            
            <a href="#" class="action-card">
                <div class="action-icon">
                    <i class="fas fa-magic"></i>
                </div>
                <div class="action-title">Enrich TMDB</div>
                <div class="action-description">Add missing metadata</div>
            </a>
        </div>

        <div class="demo-note">
            <h3>📱 Responsive Design</h3>
            <p><strong>Desktop:</strong> 4 cards per row<br>
            <strong>Tablet:</strong> 2 cards per row<br>
            <strong>Mobile:</strong> 1 card per row</p>
            
            <p style="margin-top: 1rem;">
                <a href="index.php" style="background: var(--primary-color); color: white; padding: 10px 20px; text-decoration: none; border-radius: 8px;">
                    🏠 Go to Real Dashboard
                </a>
            </p>
        </div>
    </div>
</body>
</html>
