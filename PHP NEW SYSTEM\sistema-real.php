<?php
/**
 * Sistema IPTV XUI Manager - DATOS REALES
 * =======================================
 *
 * Sistema que lee datos reales de tu base de datos XUI
 * con fallback automático a datos locales
 */

// Incluir el gestor híbrido
require_once __DIR__ . '/core/HybridDatabaseManager.php';

// Inicializar gestor híbrido
$db_error = null;
$connection_info = null;

try {
    $db = new HybridDatabaseManager();

    if (!$db->testConnection()) {
        throw new Exception("No se pudo conectar a ninguna base de datos");
    }

    $connection_info = $db->getConnectionInfo();

    // Obtener estadísticas reales
    $contentStats = $db->getContentStats();
    $qualityStats = $db->getQualityStats();
    $recentContent = $db->getRecentAdditions(5);
    $popularContent = $db->getPopularContent(5);
    $categories = $db->getCategories();

} catch (Exception $e) {
    $db_error = $e->getMessage();
    $contentStats = [];
    $qualityStats = [];
    $recentContent = [];
    $popularContent = [];
    $categories = [];
    $connection_info = ['type' => 'error', 'last_error' => $e->getMessage()];
}

// Manejar acciones AJAX
if (isset($_GET['action']) && $_GET['action'] === 'api') {
    header('Content-Type: application/json');

    try {
        switch ($_GET['endpoint'] ?? '') {
            case 'stats':
                echo json_encode([
                    'success' => true,
                    'data' => [
                        'content' => $contentStats,
                        'quality' => $qualityStats,
                        'connection' => $connection_info
                    ]
                ]);
                break;

            case 'recent':
                echo json_encode([
                    'success' => true,
                    'data' => $recentContent
                ]);
                break;

            case 'search':
                $term = $_GET['term'] ?? '';
                $results = $db->searchContent($term, 20);
                echo json_encode([
                    'success' => true,
                    'data' => $results,
                    'connection' => $connection_info
                ]);
                break;

            case 'categories':
                echo json_encode([
                    'success' => true,
                    'data' => $categories
                ]);
                break;

            case '4k-content':
                $content = $db->get4KContent(10);
                echo json_encode([
                    'success' => true,
                    'data' => $content,
                    'connection' => $connection_info
                ]);
                break;

            case 'connection-info':
                echo json_encode([
                    'success' => true,
                    'data' => $connection_info
                ]);
                break;

            case 'reconnect':
                $newType = $db->forceReconnect();
                $newInfo = $db->getConnectionInfo();
                echo json_encode([
                    'success' => true,
                    'data' => $newInfo,
                    'message' => "Reconectado como: $newType"
                ]);
                break;

            default:
                echo json_encode(['success' => false, 'message' => 'Endpoint no encontrado']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
    exit;
}
?>
<!DOCTYPE html>
<html lang="es" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IPTV XUI Manager - Datos Reales</title>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        :root {
            --primary-color: #3b82f6;
            --secondary-color: #1e40af;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --dark-bg: #0f172a;
            --dark-surface: #1e293b;
            --dark-border: #334155;
            --text-primary: #f1f5f9;
            --text-secondary: #94a3b8;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * { margin: 0; padding: 0; box-sizing: border-box; }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--dark-bg);
            color: var(--text-primary);
            line-height: 1.6;
        }

        .app-container {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            background: var(--dark-surface);
            border-right: 1px solid var(--dark-border);
            padding: 2rem 0;
            width: 280px;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 0 2rem 2rem;
            border-bottom: 1px solid var(--dark-border);
            margin-bottom: 2rem;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 1rem;
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .logo i { font-size: 2rem; }

        .connection-status {
            margin-top: 1rem;
            padding: 0.75rem;
            border-radius: 0.5rem;
            font-size: 0.875rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .status-real {
            background: rgba(16, 185, 129, 0.2);
            color: var(--success-color);
            border: 1px solid var(--success-color);
        }

        .status-local {
            background: rgba(245, 158, 11, 0.2);
            color: var(--warning-color);
            border: 1px solid var(--warning-color);
        }

        .status-error {
            background: rgba(239, 68, 68, 0.2);
            color: var(--error-color);
            border: 1px solid var(--error-color);
        }

        .nav-menu {
            list-style: none;
            padding: 0 1rem;
        }

        .nav-item { margin-bottom: 0.5rem; }

        .nav-link {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: 0.75rem;
            transition: var(--transition);
            cursor: pointer;
        }

        .nav-link:hover, .nav-link.active {
            background: var(--primary-color);
            color: white;
            transform: translateX(4px);
        }

        .main-content {
            flex: 1;
            margin-left: 280px;
            padding: 2rem;
            min-height: 100vh;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding: 1.5rem 2rem;
            background: var(--dark-surface);
            border-radius: 1rem;
            border: 1px solid var(--dark-border);
        }

        .header-title {
            font-size: 2rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 0.5rem;
            font-weight: 500;
            text-decoration: none;
            transition: var(--transition);
            cursor: pointer;
            font-size: 0.875rem;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: var(--secondary-color);
            transform: translateY(-2px);
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-warning {
            background: var(--warning-color);
            color: white;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--dark-surface);
            border: 1px solid var(--dark-border);
            border-radius: 1rem;
            padding: 2rem;
            text-align: center;
            transition: var(--transition);
            position: relative;
        }

        .stat-card:hover {
            border-color: var(--primary-color);
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
        }

        .stat-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: var(--primary-color);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 1rem;
            font-weight: 500;
        }

        .data-source {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-weight: 600;
        }

        .source-real {
            background: rgba(16, 185, 129, 0.2);
            color: var(--success-color);
        }

        .source-local {
            background: rgba(245, 158, 11, 0.2);
            color: var(--warning-color);
        }

        .content-section {
            background: var(--dark-surface);
            border: 1px solid var(--dark-border);
            border-radius: 1rem;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            color: var(--primary-color);
        }

        .content-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .content-list {
            list-style: none;
        }

        .content-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid var(--dark-border);
            transition: var(--transition);
        }

        .content-item:hover {
            background: rgba(59, 130, 246, 0.1);
        }

        .content-item:last-child {
            border-bottom: none;
        }

        .content-name {
            font-weight: 500;
            color: var(--text-primary);
        }

        .content-meta {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .search-box {
            width: 100%;
            padding: 1rem;
            background: var(--dark-bg);
            border: 1px solid var(--dark-border);
            border-radius: 0.5rem;
            color: var(--text-primary);
            font-size: 1rem;
            margin-bottom: 1rem;
        }

        .search-box:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .chart-container {
            position: relative;
            height: 300px;
            margin-top: 1rem;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .loading {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--text-secondary);
        }

        .loading i {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .alert {
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .alert-info {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid var(--primary-color);
            color: var(--primary-color);
        }

        .alert-warning {
            background: rgba(245, 158, 11, 0.1);
            border: 1px solid var(--warning-color);
            color: var(--warning-color);
        }

        .alert-error {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid var(--error-color);
            color: var(--error-color);
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .main-content {
                margin-left: 0;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-database"></i>
                    <span>IPTV XUI Real</span>
                </div>

                <!-- Connection Status -->
                <?php if ($connection_info): ?>
                <div class="connection-status <?php
                    echo $connection_info['type'] === 'real' ? 'status-real' :
                         ($connection_info['type'] === 'local' ? 'status-local' : 'status-error');
                ?>">
                    <i class="fas fa-<?php
                        echo $connection_info['type'] === 'real' ? 'check-circle' :
                             ($connection_info['type'] === 'local' ? 'exclamation-triangle' : 'times-circle');
                    ?>"></i>
                    <div>
                        <div style="font-weight: 600;">
                            <?php
                            echo $connection_info['type'] === 'real' ? 'Base de Datos XUI Real' :
                                 ($connection_info['type'] === 'local' ? 'Modo Local (Fallback)' : 'Sin Conexión');
                            ?>
                        </div>
                        <div style="font-size: 0.75rem; opacity: 0.8;">
                            <?php
                            if ($connection_info['type'] === 'real') {
                                echo 'Conectado a **************';
                            } elseif ($connection_info['type'] === 'local') {
                                echo 'Usando datos locales';
                            } else {
                                echo 'Error de conexión';
                            }
                            ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>

            <nav>
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="#" class="nav-link active" data-tab="dashboard">
                            <i class="fas fa-chart-line"></i>
                            <span>Dashboard Real</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" data-tab="content">
                            <i class="fas fa-film"></i>
                            <span>Contenido XUI</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" data-tab="quality">
                            <i class="fas fa-star"></i>
                            <span>Análisis 4K</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" data-tab="search">
                            <i class="fas fa-search"></i>
                            <span>Buscar Real</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" data-tab="connection">
                            <i class="fas fa-network-wired"></i>
                            <span>Conexión</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="header">
                <div>
                    <h1 class="header-title">IPTV XUI Manager - Datos Reales</h1>
                </div>
                <div class="header-actions">
                    <button class="btn btn-primary" onclick="refreshData()">
                        <i class="fas fa-sync-alt"></i>
                        Actualizar
                    </button>
                    <button class="btn btn-warning" onclick="forceReconnect()">
                        <i class="fas fa-plug"></i>
                        Reconectar
                    </button>
                </div>
            </header>

            <?php if ($db_error): ?>
            <div class="alert alert-error">
                <i class="fas fa-exclamation-triangle"></i>
                <div>
                    <strong>Error de Conexión:</strong> <?= htmlspecialchars($db_error) ?>
                    <br><small>Verifica tu conexión de red y credenciales de base de datos.</small>
                </div>
            </div>
            <?php endif; ?>

            <?php if ($connection_info && $connection_info['type'] === 'local'): ?>
            <div class="alert alert-warning">
                <i class="fas fa-info-circle"></i>
                <div>
                    <strong>Modo Fallback:</strong> No se pudo conectar a la base de datos XUI real. Mostrando datos locales.
                    <br><small>Haz clic en "Reconectar" para intentar conectar a la base de datos real.</small>
                </div>
            </div>
            <?php endif; ?>

            <?php if ($connection_info && $connection_info['type'] === 'real'): ?>
            <div class="alert alert-info">
                <i class="fas fa-check-circle"></i>
                <div>
                    <strong>Conectado a XUI Real:</strong> Mostrando datos en tiempo real de tu base de datos XUI.
                    <br><small>Servidor: ************** | Base de datos: xui</small>
                </div>
            </div>
            <?php endif; ?>

            <!-- Dashboard Tab -->
            <div id="dashboard" class="tab-content active">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="data-source <?= $connection_info['type'] === 'real' ? 'source-real' : 'source-local' ?>">
                            <?= $connection_info['type'] === 'real' ? 'REAL' : 'LOCAL' ?>
                        </div>
                        <div class="stat-icon"><i class="fas fa-film"></i></div>
                        <div class="stat-number"><?= number_format($contentStats['movies'] ?? 0) ?></div>
                        <div class="stat-label">Películas</div>
                    </div>
                    <div class="stat-card">
                        <div class="data-source <?= $connection_info['type'] === 'real' ? 'source-real' : 'source-local' ?>">
                            <?= $connection_info['type'] === 'real' ? 'REAL' : 'LOCAL' ?>
                        </div>
                        <div class="stat-icon"><i class="fas fa-tv"></i></div>
                        <div class="stat-number"><?= number_format($contentStats['series'] ?? 0) ?></div>
                        <div class="stat-label">Series</div>
                    </div>
                    <div class="stat-card">
                        <div class="data-source <?= $connection_info['type'] === 'real' ? 'source-real' : 'source-local' ?>">
                            <?= $connection_info['type'] === 'real' ? 'REAL' : 'LOCAL' ?>
                        </div>
                        <div class="stat-icon"><i class="fas fa-broadcast-tower"></i></div>
                        <div class="stat-number"><?= number_format($contentStats['live_tv'] ?? 0) ?></div>
                        <div class="stat-label">TV en Vivo</div>
                    </div>
                    <div class="stat-card">
                        <div class="data-source <?= $connection_info['type'] === 'real' ? 'source-real' : 'source-local' ?>">
                            <?= $connection_info['type'] === 'real' ? 'REAL' : 'LOCAL' ?>
                        </div>
                        <div class="stat-icon"><i class="fas fa-gem"></i></div>
                        <div class="stat-number"><?= number_format($qualityStats['content_4k'] ?? 0) ?></div>
                        <div class="stat-label">Contenido 4K</div>
                    </div>
                </div>

                <div class="content-grid">
                    <div class="content-section">
                        <div class="section-title">
                            <i class="fas fa-clock"></i>
                            Contenido Reciente
                            <span style="font-size: 0.75rem; color: var(--text-secondary);">
                                (<?= $connection_info['type'] === 'real' ? 'Datos Reales XUI' : 'Datos Locales' ?>)
                            </span>
                        </div>
                        <ul class="content-list">
                            <?php foreach ($recentContent as $item): ?>
                            <li class="content-item">
                                <div>
                                    <div class="content-name"><?= htmlspecialchars($item['stream_display_name']) ?></div>
                                    <div class="content-meta">
                                        <?php if (isset($item['added'])): ?>
                                            Agregado: <?= date('d/m/Y H:i', $item['added']) ?>
                                        <?php endif; ?>
                                        <?php if (isset($item['year']) && $item['year']): ?>
                                            | Año: <?= $item['year'] ?>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>

                    <div class="content-section">
                        <div class="section-title">
                            <i class="fas fa-star"></i>
                            Contenido Popular
                            <span style="font-size: 0.75rem; color: var(--text-secondary);">
                                (<?= $connection_info['type'] === 'real' ? 'Datos Reales XUI' : 'Datos Locales' ?>)
                            </span>
                        </div>
                        <ul class="content-list">
                            <?php foreach ($popularContent as $item): ?>
                            <li class="content-item">
                                <div>
                                    <div class="content-name"><?= htmlspecialchars($item['stream_display_name']) ?></div>
                                    <div class="content-meta">
                                        <?php if (isset($item['rating']) && $item['rating'] > 0): ?>
                                            Rating: <?= $item['rating'] ?>/10
                                        <?php endif; ?>
                                        <?php if (isset($item['year']) && $item['year']): ?>
                                            | Año: <?= $item['year'] ?>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                </div>

                <div class="content-section">
                    <div class="section-title">
                        <i class="fas fa-chart-pie"></i>
                        Distribución de Calidad
                        <span style="font-size: 0.75rem; color: var(--text-secondary);">
                            (<?= $connection_info['type'] === 'real' ? 'Análisis Real XUI' : 'Análisis Local' ?>)
                        </span>
                    </div>
                    <div class="chart-container">
                        <canvas id="qualityChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Content Tab -->
            <div id="content" class="tab-content">
                <div class="content-section">
                    <div class="section-title">
                        <i class="fas fa-film"></i>
                        Gestión de Contenido XUI
                    </div>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon"><i class="fas fa-link"></i></div>
                            <div class="stat-number"><?= number_format($contentStats['symlink_movies'] ?? 0) ?></div>
                            <div class="stat-label">Contenido Symlink</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon"><i class="fas fa-file"></i></div>
                            <div class="stat-number"><?= number_format($contentStats['direct_movies'] ?? 0) ?></div>
                            <div class="stat-label">Direct Source</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon"><i class="fas fa-database"></i></div>
                            <div class="stat-number"><?= number_format($contentStats['total_streams'] ?? 0) ?></div>
                            <div class="stat-label">Total Streams</div>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <div>
                            <strong>Datos de tu XUI:</strong> Estas estadísticas provienen directamente de tu base de datos XUI One.
                            <br><small>Symlink = Contenido protegido | Direct Source = Contenido gestionable</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quality Tab -->
            <div id="quality" class="tab-content">
                <div class="content-section">
                    <div class="section-title">
                        <i class="fas fa-star"></i>
                        Análisis de Calidad XUI Real
                    </div>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="data-source <?= $connection_info['type'] === 'real' ? 'source-real' : 'source-local' ?>">
                                <?= $connection_info['type'] === 'real' ? 'XUI REAL' : 'LOCAL' ?>
                            </div>
                            <div class="stat-icon"><i class="fas fa-gem"></i></div>
                            <div class="stat-number"><?= number_format($qualityStats['content_4k'] ?? 0) ?></div>
                            <div class="stat-label">4K Ultra HD</div>
                        </div>
                        <div class="stat-card">
                            <div class="data-source <?= $connection_info['type'] === 'real' ? 'source-real' : 'source-local' ?>">
                                <?= $connection_info['type'] === 'real' ? 'XUI REAL' : 'LOCAL' ?>
                            </div>
                            <div class="stat-icon"><i class="fas fa-tachometer-alt"></i></div>
                            <div class="stat-number"><?= number_format($qualityStats['content_60fps'] ?? 0) ?></div>
                            <div class="stat-label">60fps</div>
                        </div>
                        <div class="stat-card">
                            <div class="data-source <?= $connection_info['type'] === 'real' ? 'source-real' : 'source-local' ?>">
                                <?= $connection_info['type'] === 'real' ? 'XUI REAL' : 'LOCAL' ?>
                            </div>
                            <div class="stat-icon"><i class="fas fa-sun"></i></div>
                            <div class="stat-number"><?= number_format($qualityStats['content_hdr'] ?? 0) ?></div>
                            <div class="stat-label">HDR</div>
                        </div>
                        <div class="stat-card">
                            <div class="data-source <?= $connection_info['type'] === 'real' ? 'source-real' : 'source-local' ?>">
                                <?= $connection_info['type'] === 'real' ? 'XUI REAL' : 'LOCAL' ?>
                            </div>
                            <div class="stat-icon"><i class="fas fa-tv"></i></div>
                            <div class="stat-number"><?= number_format($qualityStats['content_fhd'] ?? 0) ?></div>
                            <div class="stat-label">Full HD</div>
                        </div>
                    </div>

                    <div id="4k-content-section" class="content-section">
                        <div class="section-title">
                            <i class="fas fa-gem"></i>
                            Contenido 4K Disponible
                            <button class="btn btn-primary" onclick="load4KContent()" style="margin-left: auto; font-size: 0.875rem;">
                                <i class="fas fa-sync-alt"></i>
                                Cargar 4K
                            </button>
                        </div>
                        <div id="4k-content-list">
                            <div class="loading">
                                <i class="fas fa-spinner"></i>
                                Haz clic en "Cargar 4K" para ver contenido 4K real...
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Search Tab -->
            <div id="search" class="tab-content">
                <div class="content-section">
                    <div class="section-title">
                        <i class="fas fa-search"></i>
                        Buscar en Base de Datos XUI Real
                    </div>
                    <input type="text" class="search-box" id="searchInput" placeholder="Buscar en tu base de datos XUI real...">
                    <div id="searchResults">
                        <div style="color: var(--text-secondary); text-align: center; padding: 2rem;">
                            <i class="fas fa-search" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                            <p>Escribe para buscar en tu base de datos XUI real</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Connection Tab -->
            <div id="connection" class="tab-content">
                <div class="content-section">
                    <div class="section-title">
                        <i class="fas fa-network-wired"></i>
                        Estado de Conexión
                    </div>

                    <?php if ($connection_info): ?>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-<?= $connection_info['real_available'] ? 'check-circle' : 'times-circle' ?>"></i>
                            </div>
                            <div class="stat-number"><?= $connection_info['real_available'] ? 'SÍ' : 'NO' ?></div>
                            <div class="stat-label">XUI Real Disponible</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-<?= $connection_info['local_available'] ? 'check-circle' : 'times-circle' ?>"></i>
                            </div>
                            <div class="stat-number"><?= $connection_info['local_available'] ? 'SÍ' : 'NO' ?></div>
                            <div class="stat-label">Local Disponible</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon"><i class="fas fa-chart-line"></i></div>
                            <div class="stat-number"><?= $connection_info['stats']['queries_executed'] ?? 0 ?></div>
                            <div class="stat-label">Consultas Ejecutadas</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon"><i class="fas fa-database"></i></div>
                            <div class="stat-number"><?= $connection_info['stats']['real_queries'] ?? 0 ?></div>
                            <div class="stat-label">Consultas Reales</div>
                        </div>
                    </div>

                    <div class="content-section">
                        <div class="section-title">
                            <i class="fas fa-info-circle"></i>
                            Información Técnica
                        </div>
                        <ul style="list-style: none; padding: 0;">
                            <li style="padding: 0.5rem 0; border-bottom: 1px solid var(--dark-border);">
                                <strong>Tipo de Conexión:</strong>
                                <span style="color: var(--<?= $connection_info['type'] === 'real' ? 'success' : ($connection_info['type'] === 'local' ? 'warning' : 'error') ?>-color);">
                                    <?= ucfirst($connection_info['type']) ?>
                                </span>
                            </li>
                            <li style="padding: 0.5rem 0; border-bottom: 1px solid var(--dark-border);">
                                <strong>Servidor XUI:</strong> **************:3306
                            </li>
                            <li style="padding: 0.5rem 0; border-bottom: 1px solid var(--dark-border);">
                                <strong>Base de Datos:</strong> xui
                            </li>
                            <li style="padding: 0.5rem 0; border-bottom: 1px solid var(--dark-border);">
                                <strong>Usuario:</strong> infest84
                            </li>
                            <?php if ($connection_info['last_error']): ?>
                            <li style="padding: 0.5rem 0; color: var(--error-color);">
                                <strong>Último Error:</strong> <?= htmlspecialchars($connection_info['last_error']) ?>
                            </li>
                            <?php endif; ?>
                        </ul>

                        <div style="margin-top: 1.5rem; display: flex; gap: 1rem;">
                            <button class="btn btn-primary" onclick="testConnection()">
                                <i class="fas fa-plug"></i>
                                Probar Conexión
                            </button>
                            <button class="btn btn-warning" onclick="forceReconnect()">
                                <i class="fas fa-sync-alt"></i>
                                Forzar Reconexión
                            </button>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

        </main>
    </div>

    <script>
        // Variables globales
        let currentConnectionType = '<?= $connection_info['type'] ?? 'none' ?>';

        // Tab functionality
        document.addEventListener('DOMContentLoaded', function() {
            const navLinks = document.querySelectorAll('.nav-link');
            const tabContents = document.querySelectorAll('.tab-content');

            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Remove active class from all links and contents
                    navLinks.forEach(l => l.classList.remove('active'));
                    tabContents.forEach(t => t.classList.remove('active'));

                    // Add active class to clicked link
                    this.classList.add('active');

                    // Show corresponding tab content
                    const tabId = this.getAttribute('data-tab');
                    document.getElementById(tabId).classList.add('active');
                });
            });

            // Initialize quality chart
            <?php if (!$db_error && !empty($qualityStats)): ?>
            const ctx = document.getElementById('qualityChart').getContext('2d');
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['4K', '60fps', 'HDR', 'FHD', 'HD'],
                    datasets: [{
                        data: [
                            <?= $qualityStats['content_4k'] ?? 0 ?>,
                            <?= $qualityStats['content_60fps'] ?? 0 ?>,
                            <?= $qualityStats['content_hdr'] ?? 0 ?>,
                            <?= $qualityStats['content_fhd'] ?? 0 ?>,
                            <?= $qualityStats['content_hd'] ?? 0 ?>
                        ],
                        backgroundColor: [
                            '#3b82f6',
                            '#10b981',
                            '#f59e0b',
                            '#8b5cf6',
                            '#ef4444'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: {
                                color: '#f1f5f9'
                            }
                        },
                        title: {
                            display: true,
                            text: currentConnectionType === 'real' ? 'Datos Reales XUI' : 'Datos Locales',
                            color: '#f1f5f9'
                        }
                    }
                }
            });
            <?php endif; ?>

            // Search functionality
            const searchInput = document.getElementById('searchInput');
            const searchResults = document.getElementById('searchResults');

            if (searchInput) {
                let searchTimeout;
                searchInput.addEventListener('input', function() {
                    const term = this.value.trim();

                    clearTimeout(searchTimeout);

                    if (term.length < 2) {
                        searchResults.innerHTML = `
                            <div style="color: var(--text-secondary); text-align: center; padding: 2rem;">
                                <i class="fas fa-search" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                                <p>Escribe para buscar en tu base de datos XUI real</p>
                            </div>
                        `;
                        return;
                    }

                    // Show loading
                    searchResults.innerHTML = `
                        <div class="loading" style="justify-content: center; padding: 2rem;">
                            <i class="fas fa-spinner"></i>
                            Buscando en base de datos ${currentConnectionType === 'real' ? 'XUI real' : 'local'}...
                        </div>
                    `;

                    searchTimeout = setTimeout(() => {
                        fetch(`?action=api&endpoint=search&term=${encodeURIComponent(term)}`)
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    displaySearchResults(data.data, data.connection);
                                } else {
                                    searchResults.innerHTML = `
                                        <div style="color: var(--error-color); text-align: center; padding: 2rem;">
                                            <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 1rem;"></i>
                                            <p>Error: ${data.message}</p>
                                        </div>
                                    `;
                                }
                            })
                            .catch(error => {
                                console.error('Error:', error);
                                searchResults.innerHTML = `
                                    <div style="color: var(--error-color); text-align: center; padding: 2rem;">
                                        <i class="fas fa-times-circle" style="font-size: 2rem; margin-bottom: 1rem;"></i>
                                        <p>Error de conexión</p>
                                    </div>
                                `;
                            });
                    }, 500);
                });
            }
        });

        function displaySearchResults(results, connectionInfo) {
            const searchResults = document.getElementById('searchResults');

            if (results.length === 0) {
                searchResults.innerHTML = `
                    <div style="color: var(--text-secondary); text-align: center; padding: 2rem;">
                        <i class="fas fa-search" style="font-size: 2rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                        <p>No se encontraron resultados en la base de datos ${connectionInfo?.type === 'real' ? 'XUI real' : 'local'}.</p>
                    </div>
                `;
                return;
            }

            const sourceLabel = connectionInfo?.type === 'real' ? 'XUI REAL' : 'LOCAL';
            const sourceClass = connectionInfo?.type === 'real' ? 'source-real' : 'source-local';

            const html = `
                <div class="content-section">
                    <div class="section-title">
                        <i class="fas fa-search"></i>
                        Resultados de Búsqueda
                        <span class="data-source ${sourceClass}" style="margin-left: auto; font-size: 0.75rem;">
                            ${sourceLabel}
                        </span>
                    </div>
                    <ul class="content-list">
                        ${results.map(item => `
                            <div class="content-item">
                                <div>
                                    <div class="content-name">${item.stream_display_name}</div>
                                    <div class="content-meta">
                                        ${item.year ? `Año: ${item.year}` : ''}
                                        ${item.rating && item.rating > 0 ? ` | Rating: ${item.rating}/10` : ''}
                                        ${item.added ? ` | Agregado: ${new Date(item.added * 1000).toLocaleDateString()}` : ''}
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </ul>
                </div>
            `;

            searchResults.innerHTML = html;
        }

        function refreshData() {
            const btn = event.target.closest('.btn');
            const originalText = btn.innerHTML;

            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Actualizando...';
            btn.disabled = true;

            fetch('?action=api&endpoint=stats')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update connection type
                        currentConnectionType = data.data.connection?.type || 'none';

                        // Reload page to show updated data
                        window.location.reload();
                    } else {
                        alert('Error al actualizar datos: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error de conexión al actualizar datos');
                })
                .finally(() => {
                    btn.innerHTML = originalText;
                    btn.disabled = false;
                });
        }

        function forceReconnect() {
            const btn = event.target.closest('.btn');
            const originalText = btn.innerHTML;

            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Reconectando...';
            btn.disabled = true;

            fetch('?action=api&endpoint=reconnect')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        currentConnectionType = data.data.type;
                        alert(data.message);
                        window.location.reload();
                    } else {
                        alert('Error al reconectar: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error de conexión al intentar reconectar');
                })
                .finally(() => {
                    btn.innerHTML = originalText;
                    btn.disabled = false;
                });
        }

        function load4KContent() {
            const btn = event.target.closest('.btn');
            const originalText = btn.innerHTML;
            const contentList = document.getElementById('4k-content-list');

            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Cargando...';
            btn.disabled = true;

            contentList.innerHTML = `
                <div class="loading" style="justify-content: center; padding: 2rem;">
                    <i class="fas fa-spinner"></i>
                    Cargando contenido 4K desde base de datos ${currentConnectionType === 'real' ? 'XUI real' : 'local'}...
                </div>
            `;

            fetch('?action=api&endpoint=4k-content')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        display4KContent(data.data, data.connection);
                    } else {
                        contentList.innerHTML = `
                            <div style="color: var(--error-color); text-align: center; padding: 2rem;">
                                <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 1rem;"></i>
                                <p>Error: ${data.message}</p>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    contentList.innerHTML = `
                        <div style="color: var(--error-color); text-align: center; padding: 2rem;">
                            <i class="fas fa-times-circle" style="font-size: 2rem; margin-bottom: 1rem;"></i>
                            <p>Error de conexión</p>
                        </div>
                    `;
                })
                .finally(() => {
                    btn.innerHTML = originalText;
                    btn.disabled = false;
                });
        }

        function display4KContent(content, connectionInfo) {
            const contentList = document.getElementById('4k-content-list');

            if (content.length === 0) {
                contentList.innerHTML = `
                    <div style="color: var(--text-secondary); text-align: center; padding: 2rem;">
                        <i class="fas fa-gem" style="font-size: 2rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                        <p>No se encontró contenido 4K en la base de datos ${connectionInfo?.type === 'real' ? 'XUI real' : 'local'}.</p>
                    </div>
                `;
                return;
            }

            const sourceLabel = connectionInfo?.type === 'real' ? 'XUI REAL' : 'LOCAL';
            const sourceClass = connectionInfo?.type === 'real' ? 'source-real' : 'source-local';

            const html = `
                <div style="margin-bottom: 1rem; display: flex; align-items: center; gap: 1rem;">
                    <span style="color: var(--text-secondary);">Encontrados: ${content.length} títulos 4K</span>
                    <span class="data-source ${sourceClass}">${sourceLabel}</span>
                </div>
                <ul class="content-list">
                    ${content.map(item => `
                        <div class="content-item">
                            <div>
                                <div class="content-name">${item.stream_display_name}</div>
                                <div class="content-meta">
                                    ${item.year ? `Año: ${item.year}` : ''}
                                    ${item.rating && item.rating > 0 ? ` | Rating: ${item.rating}/10` : ''}
                                    ${item.added ? ` | Agregado: ${new Date(item.added * 1000).toLocaleDateString()}` : ''}
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </ul>
            `;

            contentList.innerHTML = html;
        }

        function testConnection() {
            const btn = event.target.closest('.btn');
            const originalText = btn.innerHTML;

            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Probando...';
            btn.disabled = true;

            fetch('?action=api&endpoint=connection-info')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const info = data.data;
                        const status = info.real_available ? 'Conexión XUI exitosa' : 'Conexión XUI falló, usando local';
                        alert(status);
                    } else {
                        alert('Error al probar conexión: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error de red al probar conexión');
                })
                .finally(() => {
                    btn.innerHTML = originalText;
                    btn.disabled = false;
                });
        }
    </script>
</body>
</html>