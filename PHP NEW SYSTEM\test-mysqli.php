<?php
/**
 * Test Database Connection using MySQLi
 * ====================================
 * 
 * Alternative test when PDO MySQL is not available
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>XUI Database Test - MySQLi Version</h1>\n";

// Check MySQLi availability
echo "<h2>MySQLi Extension Check</h2>\n";
echo "<pre>\n";
echo "MySQLi available: " . (extension_loaded('mysqli') ? 'YES' : 'NO') . "\n";
echo "PHP Version: " . PHP_VERSION . "\n";
echo "</pre>\n";

if (!extension_loaded('mysqli')) {
    echo "<div style='color: red; font-weight: bold;'>ERROR: MySQLi extension is not installed!</div>\n";
    echo "<p>Please install MySQLi extension or enable it in php.ini</p>\n";
    exit;
}

// Database configuration
$host = '**************';
$port = 3306;
$database = 'xui';
$username = 'infest84';
$password = 'GZM6hh12zHKgfg34FghtAQ84WxloPRX]SV$fP';

echo "<h2>Database Connection Test</h2>\n";
echo "<pre>\n";
echo "Connecting to: {$host}:{$port}\n";
echo "Database: {$database}\n";
echo "Username: {$username}\n\n";

try {
    // Create connection
    $connection = new mysqli($host, $username, $password, $database, $port);
    
    // Check connection
    if ($connection->connect_error) {
        throw new Exception("Connection failed: " . $connection->connect_error);
    }
    
    echo "✓ Connection successful!\n\n";
    
    // Set charset
    $connection->set_charset("utf8mb4");
    
    // Test basic query
    $result = $connection->query("SELECT VERSION() as version, NOW() as current_time");
    if ($result) {
        $row = $result->fetch_assoc();
        echo "MySQL Version: " . $row['version'] . "\n";
        echo "Server Time: " . $row['current_time'] . "\n\n";
        $result->free();
    }
    
    // Test XUI tables
    echo "Checking XUI One tables:\n";
    $tables = ['streams', 'streams_categories', 'streams_series', 'streams_episodes'];
    
    foreach ($tables as $table) {
        $result = $connection->query("SHOW TABLES LIKE '{$table}'");
        if ($result && $result->num_rows > 0) {
            echo "✓ Table '{$table}' exists\n";
            $result->free();
            
            // Get row count
            $result = $connection->query("SELECT COUNT(*) as count FROM {$table}");
            if ($result) {
                $row = $result->fetch_assoc();
                echo "  - Total rows: " . $row['count'] . "\n";
                $result->free();
                
                // Get specific counts for streams table
                if ($table === 'streams') {
                    $sql = "SELECT 
                        SUM(CASE WHEN type = 1 THEN 1 ELSE 0 END) as live_tv,
                        SUM(CASE WHEN type = 2 THEN 1 ELSE 0 END) as movies,
                        SUM(CASE WHEN type = 3 THEN 1 ELSE 0 END) as series,
                        SUM(CASE WHEN type = 2 AND movie_symlink = 1 THEN 1 ELSE 0 END) as symlink_movies,
                        SUM(CASE WHEN type = 2 AND direct_source = 1 THEN 1 ELSE 0 END) as direct_movies
                    FROM streams";
                    
                    $result = $connection->query($sql);
                    if ($result) {
                        $stats = $result->fetch_assoc();
                        echo "  - Live TV: " . ($stats['live_tv'] ?? 0) . "\n";
                        echo "  - Movies: " . ($stats['movies'] ?? 0) . "\n";
                        echo "  - Series: " . ($stats['series'] ?? 0) . "\n";
                        echo "  - Symlink Movies: " . ($stats['symlink_movies'] ?? 0) . "\n";
                        echo "  - Direct Source Movies: " . ($stats['direct_movies'] ?? 0) . "\n";
                        $result->free();
                    }
                }
            }
        } else {
            echo "✗ Table '{$table}' missing\n";
        }
        echo "\n";
    }
    
    echo "✓ Database is ready for IPTV management!\n";
    
    // Close connection
    $connection->close();
    
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
    echo "Please check your database credentials and network connectivity.\n";
}

echo "</pre>\n";

echo "<h2>Next Steps</h2>\n";
if (!isset($e)) {
    echo "<p style='color: green;'>✅ Database connection successful! You can now:</p>\n";
    echo "<ul>\n";
    echo "<li><a href='index.php'>Launch the IPTV Manager</a></li>\n";
    echo "<li><a href='public/index.php'>Access the Dashboard</a></li>\n";
    echo "</ul>\n";
} else {
    echo "<p style='color: red;'>❌ Database connection failed. Please:</p>\n";
    echo "<ul>\n";
    echo "<li>Check your network connection</li>\n";
    echo "<li>Verify database credentials</li>\n";
    echo "<li>Ensure the database server is accessible</li>\n";
    echo "</ul>\n";
}
?>
