#!/usr/bin/env python3
"""
Test script para verificar la funcionalidad básica del gestor IPTV
"""

import sys
import os

# Añadir el directorio del proyecto al path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Probar importaciones básicas"""
    try:
        from config.settings import Settings
        print("✅ Config importado correctamente")
        
        from core.database_manager import DatabaseManager
        print("✅ DatabaseManager importado correctamente")
        
        from core.tmdb_api import TMDBClient
        print("✅ TMDBClient importado correctamente")
        
        from core.m3u_parser import M3UParser
        print("✅ M3UParser importado correctamente")
        
        from ui.main_window import MainWindow
        print("✅ MainWindow importado correctamente")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en importaciones: {e}")
        return False

def test_settings():
    """Probar configuración"""
    try:
        from config.settings import Settings
        settings = Settings()
        print(f"✅ Settings inicializado - App: {settings.app_name}")
        return True
        
    except Exception as e:
        print(f"❌ Error en settings: {e}")
        return False

def test_database_schema():
    """Probar configuración de base de datos"""
    try:
        from config.database import EXISTING_TABLES, QUERIES
        print(f"✅ Configuración de BD - {len(EXISTING_TABLES)} tablas existentes")
        
        # Verificar que las tablas principales están configuradas
        required_tables = ['streams', 'streams_categories', 'streams_series', 'streams_episodes']
        for table in required_tables:
            if table in EXISTING_TABLES:
                print(f"  ✅ Tabla {table} configurada")
            else:
                print(f"  ❌ Tabla {table} no encontrada")
                return False
        
        # Verificar que existen queries
        if len(QUERIES) > 0:
            print(f"  ✅ {len(QUERIES)} queries SQL definidas")
        else:
            print("  ❌ No se encontraron queries SQL")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error en configuración: {e}")
        return False

def test_models():
    """Probar modelos de datos"""
    try:
        from models import Movie, TVShow, M3UEntry, Playlist
        
        # Crear instancias de prueba
        movie = Movie(title="Test Movie", tmdb_id=12345)
        tv_show = TVShow(name="Test Show", tmdb_id=67890)
        entry = M3UEntry(title="Test Channel", url="http://test.com/stream")
        playlist = Playlist(name="Test Playlist")
        
        print("✅ Modelos creados correctamente")
        print(f"  - Movie: {movie.title}")
        print(f"  - TV Show: {tv_show.name}")
        print(f"  - M3U Entry: {entry.title}")
        print(f"  - Playlist: {playlist.name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en modelos: {e}")
        return False

def main():
    """Función principal de test"""
    print("🧪 Iniciando tests del gestor IPTV XUI One")
    print("=" * 50)
    
    tests = [
        ("Importaciones", test_imports),
        ("Settings", test_settings),
        ("Database Configuration", test_database_schema),
        ("Models", test_models)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Test: {test_name}")
        print("-" * 30)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name} - PASADO")
        else:
            print(f"❌ {test_name} - FALLIDO")
    
    print("\n" + "=" * 50)
    print(f"📊 Resultado: {passed}/{total} tests pasados")
    
    if passed == total:
        print("🎉 ¡Todos los tests pasaron correctamente!")
        return True
    else:
        print("⚠️  Algunos tests fallaron")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
