<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IPTV XUI One - System Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #0f172a;
            color: #f1f5f9;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: #1e293b;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }
        h1 {
            color: #3b82f6;
            border-bottom: 2px solid #334155;
            padding-bottom: 15px;
            margin-bottom: 25px;
        }
        .test-section {
            margin: 25px 0;
            padding: 20px;
            background: #334155;
            border-radius: 8px;
            border-left: 4px solid #3b82f6;
        }
        .success { color: #10b981; }
        .error { color: #ef4444; }
        .warning { color: #f59e0b; }
        .info { color: #06b6d4; }
        .stat-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .stat-card {
            background: #475569;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #3b82f6;
        }
        pre {
            background: #0f172a;
            padding: 15px;
            border-radius: 6px;
            overflow-x: auto;
            border: 1px solid #475569;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 IPTV XUI One Content Manager - System Test</h1>
        
        <?php
        error_reporting(E_ALL);
        ini_set('display_errors', 1);
        
        echo '<div class="test-section">';
        echo '<h3>📋 System Information</h3>';
        echo '<p><strong>PHP Version:</strong> ' . PHP_VERSION . '</p>';
        echo '<p><strong>Server:</strong> ' . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . '</p>';
        echo '<p><strong>OS:</strong> ' . PHP_OS . '</p>';
        echo '</div>';
        
        // Test environment loading
        echo '<div class="test-section">';
        echo '<h3>🔧 Environment Configuration</h3>';
        
        try {
            require_once 'config/env.php';
            
            if (file_exists('.env')) {
                echo '<p class="success">✅ .env file found and loaded</p>';
                
                echo '<p><strong>Database Configuration:</strong></p>';
                echo '<ul>';
                echo '<li>Host: ' . (env('DB_HOST') ?: '<span class="error">Not set</span>') . '</li>';
                echo '<li>Port: ' . (env('DB_PORT') ?: '<span class="error">Not set</span>') . '</li>';
                echo '<li>Database: ' . (env('DB_NAME') ?: '<span class="error">Not set</span>') . '</li>';
                echo '<li>User: ' . (env('DB_USER') ?: '<span class="error">Not set</span>') . '</li>';
                echo '<li>Password: ' . (env('DB_PASSWORD') ? '<span class="success">[SET]</span>' : '<span class="error">[NOT SET]</span>') . '</li>';
                echo '</ul>';
            } else {
                echo '<p class="error">❌ .env file not found</p>';
            }
        } catch (Exception $e) {
            echo '<p class="error">❌ Error loading environment: ' . htmlspecialchars($e->getMessage()) . '</p>';
        }
        echo '</div>';
        
        // Test PHP extensions
        echo '<div class="test-section">';
        echo '<h3>🔌 PHP Extensions</h3>';
        
        $required_extensions = ['pdo', 'pdo_mysql', 'curl', 'json', 'mbstring'];
        foreach ($required_extensions as $ext) {
            if (extension_loaded($ext)) {
                echo '<p class="success">✅ ' . $ext . '</p>';
            } else {
                echo '<p class="error">❌ ' . $ext . ' (missing)</p>';
            }
        }
        echo '</div>';
        
        // Test database connection
        echo '<div class="test-section">';
        echo '<h3>🗄️ Database Connection</h3>';
        
        if (extension_loaded('pdo') && extension_loaded('pdo_mysql')) {
            try {
                require_once 'config/database.php';
                
                $config = DB_CONFIG;
                $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}";
                
                $options = [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_TIMEOUT => 10
                ];
                
                $pdo = new PDO($dsn, $config['username'], $config['password'], $options);
                echo '<p class="success">✅ Database connection successful</p>';
                
                // Test basic query
                $stmt = $pdo->query("SELECT COUNT(*) as total FROM streams");
                $result = $stmt->fetch();
                echo '<p class="info">📊 Total streams in database: ' . number_format($result['total']) . '</p>';
                
                // Get detailed statistics
                $stats_query = "SELECT 
                    COUNT(*) as total_streams,
                    SUM(CASE WHEN type = 1 THEN 1 ELSE 0 END) as live_tv,
                    SUM(CASE WHEN type = 2 THEN 1 ELSE 0 END) as movies,
                    SUM(CASE WHEN type = 3 THEN 1 ELSE 0 END) as series,
                    SUM(CASE WHEN type = 2 AND movie_symlink = 1 THEN 1 ELSE 0 END) as symlink_movies,
                    SUM(CASE WHEN type = 2 AND direct_source = 1 THEN 1 ELSE 0 END) as direct_movies
                FROM streams";
                
                $stmt = $pdo->query($stats_query);
                $stats = $stmt->fetch();
                
                echo '<div class="stat-grid">';
                echo '<div class="stat-card"><div class="stat-number">' . number_format($stats['total_streams']) . '</div><div>Total Streams</div></div>';
                echo '<div class="stat-card"><div class="stat-number">' . number_format($stats['live_tv']) . '</div><div>Live TV</div></div>';
                echo '<div class="stat-card"><div class="stat-number">' . number_format($stats['movies']) . '</div><div>Movies</div></div>';
                echo '<div class="stat-card"><div class="stat-number">' . number_format($stats['series']) . '</div><div>Series</div></div>';
                echo '<div class="stat-card"><div class="stat-number">' . number_format($stats['symlink_movies']) . '</div><div>Symlink Movies</div></div>';
                echo '<div class="stat-card"><div class="stat-number">' . number_format($stats['direct_movies']) . '</div><div>Direct Movies</div></div>';
                echo '</div>';
                
                // Test required tables
                echo '<h4>📋 Required Tables</h4>';
                $tables = ['streams', 'streams_categories', 'streams_series', 'streams_episodes'];
                foreach ($tables as $table) {
                    $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
                    if ($stmt->rowCount() > 0) {
                        $count_stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
                        $count = $count_stmt->fetch()['count'];
                        echo '<p class="success">✅ ' . $table . ' (' . number_format($count) . ' records)</p>';
                    } else {
                        echo '<p class="error">❌ ' . $table . ' (missing)</p>';
                    }
                }
                
            } catch (Exception $e) {
                echo '<p class="error">❌ Database connection failed: ' . htmlspecialchars($e->getMessage()) . '</p>';
            }
        } else {
            echo '<p class="error">❌ PDO MySQL extension not available</p>';
        }
        echo '</div>';
        
        // Test file permissions
        echo '<div class="test-section">';
        echo '<h3>📁 File Permissions</h3>';
        
        $test_dirs = ['logs', 'uploads', 'public/assets'];
        foreach ($test_dirs as $dir) {
            if (is_dir($dir)) {
                if (is_writable($dir)) {
                    echo '<p class="success">✅ ' . $dir . ' (writable)</p>';
                } else {
                    echo '<p class="warning">⚠️ ' . $dir . ' (not writable)</p>';
                }
            } else {
                echo '<p class="warning">⚠️ ' . $dir . ' (directory not found)</p>';
            }
        }
        echo '</div>';
        
        // Show server info
        if (isset($_SERVER['HTTP_HOST'])) {
            echo '<div class="test-section">';
            echo '<h3>🌐 Server Information</h3>';
            echo '<p><strong>Host:</strong> ' . $_SERVER['HTTP_HOST'] . '</p>';
            echo '<p><strong>Request URI:</strong> ' . $_SERVER['REQUEST_URI'] . '</p>';
            echo '<p><strong>Document Root:</strong> ' . $_SERVER['DOCUMENT_ROOT'] . '</p>';
            echo '<p><strong>Current Directory:</strong> ' . __DIR__ . '</p>';
            echo '</div>';
        }
        ?>
        
        <div class="test-section">
            <h3>🚀 Next Steps</h3>
            <p>If all tests pass, you can proceed to:</p>
            <ul>
                <li><a href="index.php" style="color: #3b82f6;">Main Application</a></li>
                <li><a href="public/" style="color: #3b82f6;">Public Interface</a></li>
                <li><a href="api/export-data.php" style="color: #3b82f6;">API Test</a></li>
            </ul>
        </div>
    </div>
</body>
</html>
