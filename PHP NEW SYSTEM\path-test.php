<?php
/**
 * Path Test - IPTV XUI One Content Manager
 * =======================================
 * 
 * Test path resolution and debug path issues
 */

// Include path management
require_once __DIR__ . '/config/paths.php';

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Path Test - IPTV Manager</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #0f172a;
            color: #f1f5f9;
            margin: 0;
            padding: 2rem;
            line-height: 1.6;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .card {
            background: #1e293b;
            border: 1px solid #334155;
            border-radius: 1rem;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .success {
            background: rgba(16, 185, 129, 0.1);
            border-color: #10b981;
        }
        .error {
            background: rgba(239, 68, 68, 0.1);
            border-color: #ef4444;
        }
        .info {
            background: rgba(59, 130, 246, 0.1);
            border-color: #3b82f6;
        }
        pre {
            background: #0f172a;
            padding: 1rem;
            border-radius: 0.5rem;
            overflow-x: auto;
            border: 1px solid #334155;
            white-space: pre-wrap;
        }
        .path-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #334155;
        }
        .path-item:last-child {
            border-bottom: none;
        }
        .status {
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: bold;
        }
        .status-ok { background: #10b981; color: white; }
        .status-error { background: #ef4444; color: white; }
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #3b82f6;
            color: white;
            text-decoration: none;
            border-radius: 0.5rem;
            margin: 0.5rem 0.5rem 0.5rem 0;
        }
        .btn:hover {
            background: #1e40af;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛣️ Path Test - IPTV Manager</h1>
        
        <!-- Debug Information -->
        <div class="card info">
            <h3>Debug Information</h3>
            <pre><?= htmlspecialchars(print_r(debugPaths(), true)) ?></pre>
        </div>

        <!-- Path Constants -->
        <div class="card">
            <h3>Path Constants</h3>
            <div class="path-item">
                <span><strong>PROJECT_ROOT:</strong> <?= PROJECT_ROOT ?></span>
                <span class="status <?= is_dir(PROJECT_ROOT) ? 'status-ok' : 'status-error' ?>">
                    <?= is_dir(PROJECT_ROOT) ? 'EXISTS' : 'NOT FOUND' ?>
                </span>
            </div>
            <div class="path-item">
                <span><strong>CONFIG_PATH:</strong> <?= CONFIG_PATH ?></span>
                <span class="status <?= is_dir(CONFIG_PATH) ? 'status-ok' : 'status-error' ?>">
                    <?= is_dir(CONFIG_PATH) ? 'EXISTS' : 'NOT FOUND' ?>
                </span>
            </div>
            <div class="path-item">
                <span><strong>CORE_PATH:</strong> <?= CORE_PATH ?></span>
                <span class="status <?= is_dir(CORE_PATH) ? 'status-ok' : 'status-error' ?>">
                    <?= is_dir(CORE_PATH) ? 'EXISTS' : 'NOT FOUND' ?>
                </span>
            </div>
            <div class="path-item">
                <span><strong>PUBLIC_PATH:</strong> <?= PUBLIC_PATH ?></span>
                <span class="status <?= is_dir(PUBLIC_PATH) ? 'status-ok' : 'status-error' ?>">
                    <?= is_dir(PUBLIC_PATH) ? 'EXISTS' : 'NOT FOUND' ?>
                </span>
            </div>
            <div class="path-item">
                <span><strong>API_PATH:</strong> <?= API_PATH ?></span>
                <span class="status <?= is_dir(API_PATH) ? 'status-ok' : 'status-error' ?>">
                    <?= is_dir(API_PATH) ? 'EXISTS' : 'NOT FOUND' ?>
                </span>
            </div>
        </div>

        <!-- Configuration Files -->
        <div class="card">
            <h3>Configuration Files</h3>
            <?php
            $configFiles = [
                'paths.php' => getConfigPath('paths.php'),
                'session.php' => getConfigPath('session.php'),
                'settings.php' => getConfigPath('settings.php'),
                'database.php' => getConfigPath('database.php'),
                'tmdb_config.php' => getConfigPath('tmdb_config.php'),
                'hosting.php' => getConfigPath('hosting.php')
            ];
            
            foreach ($configFiles as $name => $path):
            ?>
            <div class="path-item">
                <span><strong><?= $name ?>:</strong> <?= $path ?></span>
                <span class="status <?= file_exists($path) ? 'status-ok' : 'status-error' ?>">
                    <?= file_exists($path) ? 'EXISTS' : 'NOT FOUND' ?>
                </span>
            </div>
            <?php endforeach; ?>
        </div>

        <!-- Core Files -->
        <div class="card">
            <h3>Core Files</h3>
            <?php
            $coreFiles = [
                'DatabaseManager.php' => getCorePath('DatabaseManager.php'),
                'M3UParser.php' => getCorePath('M3UParser.php'),
                'TMDBClient.php' => getCorePath('TMDBClient.php'),
                'ContentManager.php' => getCorePath('ContentManager.php')
            ];
            
            foreach ($coreFiles as $name => $path):
            ?>
            <div class="path-item">
                <span><strong><?= $name ?>:</strong> <?= $path ?></span>
                <span class="status <?= file_exists($path) ? 'status-ok' : 'status-error' ?>">
                    <?= file_exists($path) ? 'EXISTS' : 'NOT FOUND' ?>
                </span>
            </div>
            <?php endforeach; ?>
        </div>

        <!-- API Files -->
        <div class="card">
            <h3>API Files</h3>
            <?php
            $apiFiles = [
                'upload-m3u.php' => getApiPath('upload-m3u.php'),
                'detect-missing.php' => getApiPath('detect-missing.php'),
                'export-data.php' => getApiPath('export-data.php')
            ];
            
            foreach ($apiFiles as $name => $path):
            ?>
            <div class="path-item">
                <span><strong><?= $name ?>:</strong> <?= $path ?></span>
                <span class="status <?= file_exists($path) ? 'status-ok' : 'status-error' ?>">
                    <?= file_exists($path) ? 'EXISTS' : 'NOT FOUND' ?>
                </span>
            </div>
            <?php endforeach; ?>
        </div>

        <!-- Public Files -->
        <div class="card">
            <h3>Public Files</h3>
            <?php
            $publicFiles = [
                'index.php' => getPublicPath('index.php'),
                'assets/css/main.css' => getPublicPath('assets/css/main.css'),
                'assets/js/main.js' => getPublicPath('assets/js/main.js'),
                'pages/dashboard.php' => getPublicPath('pages/dashboard.php'),
                'pages/content-browser.php' => getPublicPath('pages/content-browser.php'),
                'pages/m3u-manager.php' => getPublicPath('pages/m3u-manager.php'),
                'pages/analytics.php' => getPublicPath('pages/analytics.php'),
                'pages/settings.php' => getPublicPath('pages/settings.php')
            ];
            
            foreach ($publicFiles as $name => $path):
            ?>
            <div class="path-item">
                <span><strong><?= $name ?>:</strong> <?= $path ?></span>
                <span class="status <?= file_exists($path) ? 'status-ok' : 'status-error' ?>">
                    <?= file_exists($path) ? 'EXISTS' : 'NOT FOUND' ?>
                </span>
            </div>
            <?php endforeach; ?>
        </div>

        <!-- Test Include -->
        <div class="card">
            <h3>Test Include</h3>
            <?php
            try {
                require_once getConfigPath('session.php');
                echo '<div class="success">✅ Successfully included session.php</div>';
            } catch (Exception $e) {
                echo '<div class="error">❌ Failed to include session.php: ' . htmlspecialchars($e->getMessage()) . '</div>';
            }
            ?>
        </div>

        <!-- Actions -->
        <div class="card">
            <h3>Test Actions</h3>
            <a href="index.php" class="btn">🏠 Go to Main App</a>
            <a href="system-check.php" class="btn">🔍 System Check</a>
            <a href="session-test.php" class="btn">🧪 Session Test</a>
            <a href="?refresh=1" class="btn">🔄 Refresh</a>
        </div>
    </div>
</body>
</html>
