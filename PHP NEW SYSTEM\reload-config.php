<?php
/**
 * Reload Configuration - IPTV XUI One Content Manager
 * ==================================================
 * 
 * Forces reload of configuration and tests database connection
 */

// Clear any existing environment variables
foreach ($_ENV as $key => $value) {
    if (strpos($key, 'DB_') === 0) {
        unset($_ENV[$key]);
    }
}

// Force reload environment
require_once __DIR__ . '/config/env.php';
loadEnvironmentVariables(__DIR__ . '/.env');

// Force reload database config
require_once __DIR__ . '/config/database.php';

echo "<h1>🔄 Configuration Reload</h1>";

echo "<h2>📋 Environment Variables</h2>";
$envVars = ['DB_HOST', 'DB_PORT', 'DB_NAME', 'DB_USER', 'DB_PASSWORD'];
foreach ($envVars as $var) {
    $value = env($var, 'NOT SET');
    if ($var === 'DB_PASSWORD' && $value !== 'NOT SET') {
        $value = '***' . substr($value, -4);
    }
    echo "<p><strong>{$var}:</strong> {$value}</p>";
}

echo "<h2>🔧 Database Configuration</h2>";
$config = DB_CONFIG;
echo "<pre>";
echo "Host: " . $config['host'] . "\n";
echo "Port: " . $config['port'] . "\n";
echo "Database: " . $config['database'] . "\n";
echo "Username: " . $config['username'] . "\n";
echo "Password: " . (strlen($config['password']) > 0 ? '***' . substr($config['password'], -4) : 'NOT SET') . "\n";
echo "Charset: " . $config['charset'] . "\n";
echo "Options: " . (isset($config['options']) ? 'SET (' . count($config['options']) . ' items)' : 'NOT SET') . "\n";
echo "</pre>";

echo "<h2>🚀 Testing DatabaseManager</h2>";

try {
    require_once __DIR__ . '/core/DatabaseManager.php';
    
    echo "<p>✅ DatabaseManager class loaded</p>";
    
    $db = new DatabaseManager();
    echo "<p>✅ DatabaseManager instance created</p>";
    
    if ($db->testConnection()) {
        echo "<p>✅ <strong>Database connection successful!</strong></p>";
        
        // Try to get some stats
        $stats = $db->getContentStats();
        if (!empty($stats)) {
            echo "<p>✅ Content stats retrieved:</p>";
            echo "<pre>" . print_r($stats, true) . "</pre>";
        }
        
        echo '<p><a href="public/index.php" style="background: #10b981; color: white; padding: 0.5rem 1rem; text-decoration: none; border-radius: 0.25rem;">🚀 Launch Application with Real Data</a></p>';
        
    } else {
        echo "<p>❌ Database connection test failed</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ <strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo '<p><a href="force-database.php" style="background: #3b82f6; color: white; padding: 0.5rem 1rem; text-decoration: none; border-radius: 0.25rem;">🔧 Force Database Connection</a></p>';
}

echo "<h2>🔍 Debug Information</h2>";
echo "<p><strong>PHP Version:</strong> " . PHP_VERSION . "</p>";
echo "<p><strong>PDO Available:</strong> " . (extension_loaded('pdo') ? 'YES' : 'NO') . "</p>";
echo "<p><strong>PDO MySQL Available:</strong> " . (extension_loaded('pdo_mysql') ? 'YES' : 'NO') . "</p>";
echo "<p><strong>Current Working Directory:</strong> " . getcwd() . "</p>";
echo "<p><strong>Script Directory:</strong> " . __DIR__ . "</p>";

if (extension_loaded('pdo_mysql')) {
    echo "<h3>Available PDO Drivers:</h3>";
    echo "<pre>" . implode(', ', PDO::getAvailableDrivers()) . "</pre>";
}

echo '<style>
body { font-family: Arial, sans-serif; margin: 2rem; background: #f5f5f5; }
h1, h2, h3 { color: #333; }
p { margin: 0.5rem 0; }
pre { background: #eee; padding: 1rem; border-radius: 0.25rem; overflow-x: auto; }
</style>';
?>
