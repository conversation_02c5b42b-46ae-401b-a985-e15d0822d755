"""
Script para probar específicamente el dashboard
"""

import asyncio
import sys
import os
import tkinter as tk
import customtkinter as ctk

# Agregar el directorio raíz al path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.settings import Settings
from core.database_manager import DatabaseManager
from ui.components.dashboard import Dashboard

async def test_dashboard():
    """Probar el dashboard específicamente"""
    try:
        # Inicializar configuración
        settings = Settings()
        
        # Inicializar database manager
        db_manager = DatabaseManager(settings)
        
        # Probar conexión
        if not await db_manager.test_connection():
            print("✗ Error de conexión")
            return
            
        if not await db_manager.verify_tables():
            print("✗ Error en verificación de tablas")
            return
            
        print("✓ Base de datos conectada y verificada")
        
        # Obtener estadísticas
        stats = await db_manager.get_content_stats()
        print(f"✓ Estadísticas obtenidas: {stats}")
        
        # Crear ventana de prueba
        root = tk.Tk()
        root.title("Test Dashboard")
        root.geometry("800x600")
        
        # Crear dashboard
        dashboard = Dashboard(root, settings, db_manager)
        print("✓ Dashboard creado")
        
        # Simular carga de datos
        print("Cargando datos del dashboard...")
        await dashboard._load_data()
        print("✓ Datos cargados")
        
        # Mostrar la ventana
        root.mainloop()
        
    except Exception as e:
        print(f"✗ Error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_dashboard())
