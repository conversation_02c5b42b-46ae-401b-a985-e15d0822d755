"""
Barra de progreso animada
========================

Componente de barra de progreso con animaciones suaves.
"""

import tkinter as tk
import customtkinter as ctk
import threading
import time
from typing import Optional, Callable

class AnimatedProgressBar:
    """Barra de progreso con animaciones"""
    
    def __init__(self, parent, width: int = 200, height: int = 20):
        self.parent = parent
        self.width = width
        self.height = height
        
        # Estado
        self.current_value = 0.0
        self.target_value = 0.0
        self.is_animating = False
        self.animation_thread = None
        
        # Crear componente
        self.frame = ctk.CTkFrame(parent, width=width, height=height, corner_radius=10)
        self.frame.pack_propagate(False)
        
        # Barra de progreso
        self.progress_bar = ctk.CTkProgressBar(
            self.frame,
            width=width - 10,
            height=height - 4,
            corner_radius=8
        )
        self.progress_bar.pack(expand=True, padx=5, pady=2)
        
        # Configurar valor inicial
        self.progress_bar.set(0.0)
    
    def pack(self, **kwargs):
        """Empaquetar el frame"""
        self.frame.pack(**kwargs)
    
    def set_progress(self, value: float, animate: bool = True):
        """Establecer progreso"""
        # Limitar valor entre 0 y 1
        value = max(0.0, min(1.0, value))
        
        if animate:
            self.target_value = value
            self._start_animation()
        else:
            self.current_value = value
            self.target_value = value
            self.progress_bar.set(value)
    
    def _start_animation(self):
        """Iniciar animación"""
        if not self.is_animating:
            self.is_animating = True
            self.animation_thread = threading.Thread(target=self._animate, daemon=True)
            self.animation_thread.start()
    
    def _animate(self):
        """Animar progreso"""
        try:
            while self.is_animating and abs(self.current_value - self.target_value) > 0.01:
                # Calcular siguiente valor
                diff = self.target_value - self.current_value
                self.current_value += diff * 0.1
                
                # Actualizar barra
                self.progress_bar.set(self.current_value)
                
                # Pausa
                time.sleep(0.02)
            
            # Valor final
            self.current_value = self.target_value
            self.progress_bar.set(self.current_value)
            self.is_animating = False
            
        except Exception as e:
            self.is_animating = False
    
    def reset(self):
        """Resetear progreso"""
        self.set_progress(0.0, animate=False)
    
    def pulse(self, duration: float = 1.0):
        """Efecto de pulso"""
        def pulse_animation():
            steps = 20
            for i in range(steps):
                value = 0.5 + 0.5 * abs(2 * i / steps - 1)
                self.set_progress(value, animate=False)
                time.sleep(duration / steps)
        
        threading.Thread(target=pulse_animation, daemon=True).start()
