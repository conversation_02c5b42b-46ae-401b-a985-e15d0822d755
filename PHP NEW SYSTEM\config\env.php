<?php
/**
 * Environment Configuration Loader
 * ================================
 * 
 * Loads environment variables from .env file
 */

/**
 * Load environment variables from .env file
 */
function loadEnvironmentVariables($envFile = null) {
    if ($envFile === null) {
        $envFile = dirname(__DIR__) . '/.env';
    }
    
    if (!file_exists($envFile)) {
        return false;
    }
    
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    
    foreach ($lines as $line) {
        // Skip comments
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        
        // Parse key=value pairs
        if (strpos($line, '=') !== false) {
            list($key, $value) = explode('=', $line, 2);
            $key = trim($key);
            $value = trim($value);
            
            // Remove quotes if present
            if ((substr($value, 0, 1) === '"' && substr($value, -1) === '"') ||
                (substr($value, 0, 1) === "'" && substr($value, -1) === "'")) {
                $value = substr($value, 1, -1);
            }
            
            // Set environment variable if not already set
            if (!isset($_ENV[$key])) {
                $_ENV[$key] = $value;
                putenv("$key=$value");
            }
        }
    }
    
    return true;
}

/**
 * Get environment variable with default value
 */
function env($key, $default = null) {
    return $_ENV[$key] ?? getenv($key) ?: $default;
}

/**
 * Detect environment (local vs hosting)
 */
function detectEnvironment() {
    // Check for common hosting indicators
    $hostingIndicators = [
        'HTTP_HOST' => ['hostinger', 'cpanel', 'shared'],
        'SERVER_NAME' => ['hostinger', 'cpanel'],
        'DOCUMENT_ROOT' => ['/home/', '/public_html/'],
        'SERVER_SOFTWARE' => ['LiteSpeed', 'Apache/2.4']
    ];

    foreach ($hostingIndicators as $var => $indicators) {
        if (isset($_SERVER[$var])) {
            foreach ($indicators as $indicator) {
                if (stripos($_SERVER[$var], $indicator) !== false) {
                    return 'hosting';
                }
            }
        }
    }

    // Check for local development indicators
    $localIndicators = [
        'HTTP_HOST' => ['localhost', '127.0.0.1', '.local'],
        'SERVER_NAME' => ['localhost', '127.0.0.1'],
        'DOCUMENT_ROOT' => ['xampp', 'wamp', 'laragon', 'mamp']
    ];

    foreach ($localIndicators as $var => $indicators) {
        if (isset($_SERVER[$var])) {
            foreach ($indicators as $indicator) {
                if (stripos($_SERVER[$var], $indicator) !== false) {
                    return 'local';
                }
            }
        }
    }

    return 'unknown';
}

/**
 * Get environment info
 */
function getEnvironmentInfo() {
    $env = detectEnvironment();

    return [
        'type' => $env,
        'host' => $_SERVER['HTTP_HOST'] ?? 'unknown',
        'server' => $_SERVER['SERVER_SOFTWARE'] ?? 'unknown',
        'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? 'unknown',
        'php_version' => PHP_VERSION,
        'extensions' => [
            'pdo' => extension_loaded('pdo'),
            'pdo_mysql' => extension_loaded('pdo_mysql'),
            'curl' => extension_loaded('curl'),
            'json' => extension_loaded('json')
        ]
    ];
}

// Load environment variables when this file is included
loadEnvironmentVariables();
