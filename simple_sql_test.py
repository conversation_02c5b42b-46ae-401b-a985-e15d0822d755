"""
Test SQL Simple
===============

Script simple para probar consultas SQL básicas.
"""

import asyncio
import sys
import os

# Agregar el directorio raíz al path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.settings import Settings
from core.database_manager import DatabaseManager

async def simple_sql_test():
    """Test SQL simple"""
    print("🔍 Test SQL Simple")
    print("=" * 30)
    
    try:
        # Inicializar componentes
        settings = Settings()
        db_manager = DatabaseManager(settings)
        
        # Probar conexión
        print("1. Probando conexión...")
        if not await db_manager.test_connection():
            print("❌ Error de conexión")
            return
        print("✅ Conexión exitosa")
        
        # Consulta simple 1: Contar streams
        print("\n2. Contando streams...")
        query1 = "SELECT COUNT(*) as total FROM streams"
        result1 = await db_manager.execute_query(query1)
        print(f"   Total streams: {result1[0]['total']:,}")
        
        # Consulta simple 2: Contar por tipo
        print("\n3. Contando por tipo...")
        query2 = """
        SELECT 
            type,
            COUNT(*) as cantidad
        FROM streams 
        GROUP BY type 
        ORDER BY type
        """
        result2 = await db_manager.execute_query(query2)
        for row in result2:
            type_name = {1: "Live TV", 2: "Movies", 3: "Series"}.get(row['type'], f"Type {row['type']}")
            print(f"   {type_name}: {row['cantidad']:,}")
        
        # Consulta simple 3: Symlink y Direct Source
        print("\n4. Analizando symlink y direct_source...")
        query3 = """
        SELECT 
            SUM(CASE WHEN movie_symlink = 1 THEN 1 ELSE 0 END) as symlink_count,
            SUM(CASE WHEN direct_source = 1 THEN 1 ELSE 0 END) as direct_count,
            COUNT(*) as total_movies
        FROM streams 
        WHERE type = 2
        """
        result3 = await db_manager.execute_query(query3)
        if result3:
            row = result3[0]
            total = row['total_movies']
            symlink = row['symlink_count']
            direct = row['direct_count']
            stream_url = total - symlink - direct
            
            print(f"   Total películas: {total:,}")
            print(f"   Con Symlink: {symlink:,} ({(symlink/total*100):.1f}%)")
            print(f"   Con Direct Source: {direct:,} ({(direct/total*100):.1f}%)")
            print(f"   Con Stream URL: {stream_url:,} ({(stream_url/total*100):.1f}%)")
        
        # Consulta simple 4: Muestra de películas
        print("\n5. Muestra de películas...")
        query4 = """
        SELECT 
            id,
            stream_display_name,
            movie_symlink,
            direct_source,
            tmdb_id,
            year,
            rating
        FROM streams 
        WHERE type = 2 
        ORDER BY added DESC 
        LIMIT 5
        """
        result4 = await db_manager.execute_query(query4)
        for i, row in enumerate(result4, 1):
            symlink = "✅" if row['movie_symlink'] == 1 else "❌"
            direct = "✅" if row['direct_source'] == 1 else "❌"
            print(f"   {i}. {row['stream_display_name'][:40]}")
            print(f"      ID: {row['id']} | Symlink: {symlink} | Direct: {direct}")
            print(f"      TMDB: {row['tmdb_id']} | Año: {row['year']} | Rating: {row['rating']}")
            print()
        
        # Consulta simple 5: Top duplicados
        print("6. Top 5 duplicados...")
        query5 = """
        SELECT 
            stream_display_name,
            COUNT(*) as count
        FROM streams 
        WHERE type = 2
        GROUP BY stream_display_name 
        HAVING COUNT(*) > 1
        ORDER BY COUNT(*) DESC
        LIMIT 5
        """
        result5 = await db_manager.execute_query(query5)
        for i, row in enumerate(result5, 1):
            print(f"   {i}. '{row['stream_display_name']}' - {row['count']} veces")
        
        print("\n✅ Test SQL simple completado")
        
    except Exception as e:
        print(f"\n❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        if 'db_manager' in locals():
            await db_manager.close()
    
    return True

if __name__ == "__main__":
    success = asyncio.run(simple_sql_test())
    if success:
        print("\n🚀 Test exitoso")
    else:
        print("\n💥 Test falló")
        sys.exit(1)
