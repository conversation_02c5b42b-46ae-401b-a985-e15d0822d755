<?php
/**
 * Application Settings for IPTV XUI One Content Manager
 * ====================================================
 * 
 * Global configuration settings for the PHP application
 */

// Application information
define('APP_CONFIG', [
    'name' => 'IPTV XUI One Content Manager',
    'version' => '2.0.0',
    'description' => 'Modern web-based IPTV content management system',
    'author' => 'IPTV Management Team',
    'timezone' => 'UTC',
    'locale' => 'en_US',
    'debug' => $_ENV['APP_DEBUG'] ?? false
]);

// Security settings
define('SECURITY_CONFIG', [
    'session_lifetime' => 3600, // 1 hour
    'csrf_protection' => true,
    'max_upload_size' => '50M',
    'allowed_file_types' => ['m3u', 'm3u8', 'txt'],
    'password_min_length' => 8,
    'max_login_attempts' => 5,
    'lockout_duration' => 900 // 15 minutes
]);

// UI/UX Configuration
define('UI_CONFIG', [
    'theme' => $_ENV['DEFAULT_THEME'] ?? 'dark',
    'available_themes' => ['light', 'dark', 'auto'],
    'default_language' => 'en',
    'available_languages' => ['en', 'es', 'fr', 'de'],
    'animation_duration' => 300, // milliseconds
    'page_size_options' => [25, 50, 100, 200],
    'default_page_size' => 50,
    'enable_animations' => true,
    'enable_tooltips' => true,
    'enable_notifications' => true
]);

// Content management settings
define('CONTENT_CONFIG', [
    'priority_rules' => [
        '4k_content' => [
            'priority' => 1,
            'protection_level' => 'maximum',
            'keywords' => ['4K', '2160p', 'UHD']
        ],
        '60fps_content' => [
            'priority' => 2,
            'protection_level' => 'high',
            'keywords' => ['60fps', '60FPS', 'HFR']
        ],
        'hdr_content' => [
            'priority' => 3,
            'protection_level' => 'high',
            'keywords' => ['HDR', 'HDR10', 'Dolby Vision']
        ],
        'symlink_content' => [
            'priority' => 4,
            'protection_level' => 'medium',
            'auto_protect' => true
        ]
    ],
    'duplicate_handling' => [
        'detection_method' => 'title_similarity',
        'similarity_threshold' => 0.85,
        'auto_merge' => false,
        'keep_highest_quality' => true
    ],
    'quality_detection' => [
        '4K' => ['4K', '2160p', 'UHD'],
        'FHD' => ['1080p', 'FHD', 'Full HD'],
        'HD' => ['720p', 'HD'],
        'SD' => ['480p', 'SD'],
        'HDR' => ['HDR', 'HDR10', 'Dolby Vision'],
        '60FPS' => ['60fps', '60FPS', 'HFR']
    ]
]);

// M3U Processing settings
define('M3U_CONFIG', [
    'max_file_size' => 50 * 1024 * 1024, // 50MB
    'batch_size' => 100,
    'timeout' => 300, // 5 minutes
    'encoding' => 'UTF-8',
    'fallback_encodings' => ['ISO-8859-1', 'Windows-1252'],
    'validate_urls' => false,
    'extract_metadata' => true,
    'auto_categorize' => true,
    'supported_formats' => ['m3u', 'm3u8']
]);

// Export settings
define('EXPORT_CONFIG', [
    'formats' => [
        'm3u' => [
            'extension' => 'm3u',
            'mime_type' => 'audio/x-mpegurl',
            'include_metadata' => true
        ],
        'txt' => [
            'extension' => 'txt',
            'mime_type' => 'text/plain',
            'format' => 'title_per_line'
        ],
        'json' => [
            'extension' => 'json',
            'mime_type' => 'application/json',
            'pretty_print' => true
        ],
        'csv' => [
            'extension' => 'csv',
            'mime_type' => 'text/csv',
            'delimiter' => ','
        ]
    ],
    'max_export_size' => 10000, // Maximum items per export
    'include_thumbnails' => false,
    'compress_output' => true
]);

// Performance settings
define('PERFORMANCE_CONFIG', [
    'enable_caching' => true,
    'cache_driver' => 'file', // file, redis, memcached
    'cache_ttl' => 3600,
    'enable_compression' => true,
    'max_execution_time' => 300,
    'memory_limit' => '512M',
    'enable_opcache' => true,
    'lazy_loading' => true,
    'pagination_size' => 50
]);

// Logging configuration
define('LOGGING_CONFIG', [
    'enabled' => true,
    'level' => $_ENV['LOG_LEVEL'] ?? 'INFO',
    'file_path' => __DIR__ . '/../logs/app.log',
    'max_file_size' => '10M',
    'max_files' => 5,
    'log_queries' => $_ENV['LOG_QUERIES'] ?? false,
    'log_api_calls' => true,
    'log_user_actions' => true
]);

// API Configuration
define('API_CONFIG', [
    'rate_limiting' => [
        'enabled' => true,
        'requests_per_minute' => 60,
        'burst_limit' => 10
    ],
    'cors' => [
        'enabled' => true,
        'allowed_origins' => ['*'],
        'allowed_methods' => ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
        'allowed_headers' => ['Content-Type', 'Authorization', 'X-Requested-With']
    ],
    'authentication' => [
        'required' => true,
        'token_lifetime' => 3600,
        'refresh_token_lifetime' => 86400
    ]
]);

// WebSocket configuration for real-time updates
define('WEBSOCKET_CONFIG', [
    'enabled' => false, // Enable when WebSocket server is available
    'host' => $_ENV['WS_HOST'] ?? 'localhost',
    'port' => $_ENV['WS_PORT'] ?? 8080,
    'secure' => $_ENV['WS_SECURE'] ?? false,
    'heartbeat_interval' => 30
]);

// File upload configuration
define('UPLOAD_CONFIG', [
    'max_file_size' => 50 * 1024 * 1024, // 50MB
    'allowed_extensions' => ['m3u', 'm3u8', 'txt'],
    'upload_path' => __DIR__ . '/../uploads/',
    'temp_path' => __DIR__ . '/../uploads/temp/',
    'auto_cleanup' => true,
    'cleanup_interval' => 3600 // 1 hour
]);

// Dashboard configuration
define('DASHBOARD_CONFIG', [
    'refresh_interval' => 30, // seconds
    'show_charts' => true,
    'chart_types' => ['pie', 'bar', 'line', 'doughnut'],
    'default_chart_type' => 'doughnut',
    'max_recent_items' => 10,
    'show_statistics' => true,
    'auto_refresh' => true
]);

// Notification settings
define('NOTIFICATION_CONFIG', [
    'enabled' => true,
    'types' => [
        'success' => ['duration' => 3000, 'icon' => 'check'],
        'error' => ['duration' => 5000, 'icon' => 'error'],
        'warning' => ['duration' => 4000, 'icon' => 'warning'],
        'info' => ['duration' => 3000, 'icon' => 'info']
    ],
    'position' => 'top-right',
    'max_notifications' => 5,
    'auto_dismiss' => true
]);

// Development settings
if (APP_CONFIG['debug']) {
    ini_set('display_errors', 1);
    ini_set('display_startup_errors', 1);
    error_reporting(E_ALL);
    
    // Override some settings for development
    define('DEV_OVERRIDES', [
        'cache_ttl' => 60, // Shorter cache for development
        'log_level' => 'DEBUG',
        'enable_query_logging' => true
    ]);
}

// Helper function to get configuration values
function get_config($key, $default = null) {
    $keys = explode('.', $key);
    $config = null;
    
    // Map first key to constant
    switch ($keys[0]) {
        case 'app': $config = APP_CONFIG; break;
        case 'security': $config = SECURITY_CONFIG; break;
        case 'ui': $config = UI_CONFIG; break;
        case 'content': $config = CONTENT_CONFIG; break;
        case 'm3u': $config = M3U_CONFIG; break;
        case 'export': $config = EXPORT_CONFIG; break;
        case 'performance': $config = PERFORMANCE_CONFIG; break;
        case 'logging': $config = LOGGING_CONFIG; break;
        case 'api': $config = API_CONFIG; break;
        case 'websocket': $config = WEBSOCKET_CONFIG; break;
        case 'upload': $config = UPLOAD_CONFIG; break;
        case 'dashboard': $config = DASHBOARD_CONFIG; break;
        case 'notification': $config = NOTIFICATION_CONFIG; break;
        default: return $default;
    }
    
    // Navigate through nested keys
    for ($i = 1; $i < count($keys); $i++) {
        if (isset($config[$keys[$i]])) {
            $config = $config[$keys[$i]];
        } else {
            return $default;
        }
    }
    
    return $config;
}

// Environment-specific overrides
if (file_exists(__DIR__ . '/local_settings.php')) {
    include __DIR__ . '/local_settings.php';
}
