<?php
/**
 * Install Extensions Guide - IPTV XUI One Content Manager
 * =======================================================
 * 
 * Provides instructions for installing required PHP extensions
 */

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Install PHP Extensions - IPTV XUI Manager</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #0f172a;
            color: #f1f5f9;
            margin: 0;
            padding: 2rem;
            line-height: 1.6;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: #1e293b;
            border-radius: 1rem;
            padding: 2rem;
            border: 1px solid #334155;
        }
        h1 {
            color: #3b82f6;
            text-align: center;
            margin-bottom: 2rem;
        }
        h2 {
            color: #10b981;
            border-bottom: 1px solid #334155;
            padding-bottom: 0.5rem;
        }
        .platform {
            background: #0f172a;
            border: 1px solid #334155;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }
        .status {
            padding: 0.5rem 1rem;
            border-radius: 0.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
            display: inline-block;
        }
        .status.success {
            background: rgba(16, 185, 129, 0.2);
            color: #10b981;
            border: 1px solid #10b981;
        }
        .status.error {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
            border: 1px solid #ef4444;
        }
        .command {
            background: #0f172a;
            border: 1px solid #334155;
            border-radius: 0.25rem;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            margin: 1rem 0;
            overflow-x: auto;
        }
        .copy-btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            cursor: pointer;
            font-size: 0.75rem;
            margin-left: 0.5rem;
        }
        .copy-btn:hover {
            background: #1e40af;
        }
        .warning {
            background: rgba(245, 158, 11, 0.1);
            border: 1px solid #f59e0b;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
            color: #f59e0b;
        }
        .info {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid #3b82f6;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
            color: #3b82f6;
        }
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #3b82f6;
            color: white;
            text-decoration: none;
            border-radius: 0.5rem;
            margin: 0.5rem;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #1e40af;
        }
        .btn.success {
            background: #10b981;
        }
        .btn.success:hover {
            background: #059669;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Install PHP Extensions</h1>

        <div class="info">
            <strong>Current Status:</strong> The IPTV XUI Manager requires PHP extensions to connect to the database.
            Below are platform-specific instructions to install the required extensions.
        </div>

        <?php
        // Check current status
        $extensions = [
            'pdo' => extension_loaded('pdo'),
            'pdo_mysql' => extension_loaded('pdo_mysql'),
            'json' => extension_loaded('json'),
            'curl' => extension_loaded('curl'),
            'mbstring' => extension_loaded('mbstring'),
            'openssl' => extension_loaded('openssl')
        ];

        echo '<h2>📋 Current Extension Status</h2>';
        foreach ($extensions as $ext => $loaded) {
            $status = $loaded ? 'success' : 'error';
            $statusText = $loaded ? '✓ Installed' : '✗ Missing';
            echo "<div class='status {$status}'>{$ext}: {$statusText}</div>";
        }

        $missingExtensions = array_keys(array_filter($extensions, function($loaded) { return !$loaded; }));
        $criticalMissing = array_intersect($missingExtensions, ['pdo', 'pdo_mysql']);

        if (empty($criticalMissing)) {
            echo '<div class="status success">✅ All critical extensions are installed!</div>';
            echo '<a href="reload-config.php" class="btn success">🔄 Test Database Connection</a>';
        } else {
            echo '<div class="warning">⚠️ Critical extensions missing: ' . implode(', ', $criticalMissing) . '</div>';
        }
        ?>

        <h2>🖥️ Windows Installation</h2>
        <div class="platform">
            <h3>XAMPP / WAMP / Local Development</h3>
            <p>1. <strong>Edit php.ini file:</strong></p>
            <div class="command">
                ; Find and uncomment these lines in php.ini:
                extension=pdo_mysql
                extension=curl
                extension=mbstring
                extension=openssl
                <button class="copy-btn" onclick="copyToClipboard(this.previousSibling.textContent)">Copy</button>
            </div>
            
            <p>2. <strong>Restart your web server</strong> (Apache/Nginx)</p>
            
            <p>3. <strong>Verify installation:</strong></p>
            <div class="command">
                php -m | findstr pdo_mysql
                <button class="copy-btn" onclick="copyToClipboard(this.previousSibling.textContent)">Copy</button>
            </div>

            <h3>Windows Server / IIS</h3>
            <div class="command">
                # Download PHP extensions from php.net
                # Copy php_pdo_mysql.dll to PHP extensions directory
                # Edit php.ini to enable extension=pdo_mysql
                # Restart IIS
                <button class="copy-btn" onclick="copyToClipboard(this.previousSibling.textContent)">Copy</button>
            </div>
        </div>

        <h2>🐧 Linux Installation</h2>
        <div class="platform">
            <h3>Ubuntu / Debian</h3>
            <div class="command">
                sudo apt update
                sudo apt install php-mysql php-curl php-mbstring php-json
                sudo systemctl restart apache2
                # or for nginx:
                sudo systemctl restart nginx
                sudo systemctl restart php8.1-fpm
                <button class="copy-btn" onclick="copyToClipboard(this.previousSibling.textContent)">Copy</button>
            </div>

            <h3>CentOS / RHEL / Rocky Linux</h3>
            <div class="command">
                sudo yum install php-mysql php-curl php-mbstring php-json
                # or for newer versions:
                sudo dnf install php-mysql php-curl php-mbstring php-json
                sudo systemctl restart httpd
                <button class="copy-btn" onclick="copyToClipboard(this.previousSibling.textContent)">Copy</button>
            </div>

            <h3>Alpine Linux</h3>
            <div class="command">
                apk add php-pdo_mysql php-curl php-mbstring php-json
                rc-service apache2 restart
                <button class="copy-btn" onclick="copyToClipboard(this.previousSibling.textContent)">Copy</button>
            </div>
        </div>

        <h2>🍎 macOS Installation</h2>
        <div class="platform">
            <h3>Homebrew</h3>
            <div class="command">
                brew install php
                # Extensions are usually included by default
                brew services restart php
                <button class="copy-btn" onclick="copyToClipboard(this.previousSibling.textContent)">Copy</button>
            </div>

            <h3>MacPorts</h3>
            <div class="command">
                sudo port install php81-mysql php81-curl php81-mbstring
                sudo port load php81-fpm
                <button class="copy-btn" onclick="copyToClipboard(this.previousSibling.textContent)">Copy</button>
            </div>
        </div>

        <h2>🐳 Docker Installation</h2>
        <div class="platform">
            <p>If you're using Docker, add these to your Dockerfile:</p>
            <div class="command">
                FROM php:8.1-apache
                RUN docker-php-ext-install pdo_mysql curl mbstring
                # or
                RUN apt-get update && apt-get install -y \
                    && docker-php-ext-install pdo_mysql
                <button class="copy-btn" onclick="copyToClipboard(this.previousSibling.textContent)">Copy</button>
            </div>
        </div>

        <h2>✅ Verification Steps</h2>
        <div class="platform">
            <p>After installing extensions, verify they're working:</p>
            
            <p>1. <strong>Command line check:</strong></p>
            <div class="command">
                php -m | grep pdo_mysql
                <button class="copy-btn" onclick="copyToClipboard(this.previousSibling.textContent)">Copy</button>
            </div>

            <p>2. <strong>Web check:</strong></p>
            <div class="command">
                &lt;?php phpinfo(); ?&gt;
                <button class="copy-btn" onclick="copyToClipboard(this.previousSibling.textContent)">Copy</button>
            </div>

            <p>3. <strong>Test with our application:</strong></p>
            <a href="check-requirements.php" class="btn">🔍 Check Requirements</a>
            <a href="reload-config.php" class="btn">🔄 Test Database</a>
        </div>

        <h2>🆘 Troubleshooting</h2>
        <div class="platform">
            <h3>Common Issues:</h3>
            <ul>
                <li><strong>Extension not loading:</strong> Check php.ini path with <code>php --ini</code></li>
                <li><strong>Permission denied:</strong> Ensure web server has read access to extension files</li>
                <li><strong>Wrong PHP version:</strong> Make sure you're editing the correct php.ini for your web server</li>
                <li><strong>Module not found:</strong> Verify extension files exist in the extensions directory</li>
            </ul>

            <h3>Find PHP Configuration:</h3>
            <div class="command">
                php --ini
                php -i | grep "Configuration File"
                <button class="copy-btn" onclick="copyToClipboard(this.previousSibling.textContent)">Copy</button>
            </div>
        </div>

        <div class="info">
            <strong>Need Help?</strong> If you're still having issues after following these instructions,
            the application will continue to work in demo mode until the extensions are properly installed.
        </div>

        <div style="text-align: center; margin-top: 2rem;">
            <a href="public/index.php" class="btn">📱 Continue with Demo Mode</a>
            <a href="reload-config.php" class="btn">🔄 Test Configuration</a>
        </div>
    </div>

    <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text.trim()).then(function() {
                // Could add a toast notification here
                console.log('Copied to clipboard');
            });
        }
    </script>
</body>
</html>
