"""
Consultas Básicas de Gestión
============================

Consultas SQL simples y directas para gestión de contenido.
"""

import asyncio
import sys
import os

# Agregar el directorio raíz al path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.settings import Settings
from core.database_manager import DatabaseManager

async def basic_queries():
    """Consultas básicas de gestión"""
    print("🔍 CONSULTAS BÁSICAS DE GESTIÓN")
    print("=" * 50)
    
    try:
        # Inicializar componentes
        settings = Settings()
        db_manager = DatabaseManager(settings)
        
        # Probar conexión
        print("1. Probando conexión...")
        if not await db_manager.test_connection():
            print("❌ Error de conexión")
            return
        print("✅ Conexión exitosa\n")
        
        # CONSULTA 1: Conteos básicos
        print("📊 CONSULTA 1: CONTEOS BÁSICOS")
        print("-" * 30)
        
        query1 = "SELECT COUNT(*) as total FROM streams WHERE type = 2 AND movie_symlink = 1"
        result1 = await db_manager.execute_query(query1)
        print(f"   Symlink total: {result1[0]['total']:,}")
        
        query2 = "SELECT COUNT(*) as total FROM streams WHERE type = 2 AND direct_source = 1"
        result2 = await db_manager.execute_query(query2)
        print(f"   Direct_source total: {result2[0]['total']:,}")
        
        # CONSULTA 2: Symlink 4K
        print("\n🎬 CONSULTA 2: SYMLINK 4K")
        print("-" * 30)
        
        query3 = """
        SELECT COUNT(*) as total 
        FROM streams 
        WHERE type = 2 AND movie_symlink = 1
        AND (stream_display_name LIKE '%4K%' OR stream_display_name LIKE '%2160p%')
        """
        result3 = await db_manager.execute_query(query3)
        print(f"   Symlink 4K: {result3[0]['total']:,}")
        
        # CONSULTA 3: Symlink CAM/TS
        print("\n📹 CONSULTA 3: SYMLINK CAM/TS (CANDIDATOS BORRADO)")
        print("-" * 30)
        
        query4 = """
        SELECT COUNT(*) as total 
        FROM streams 
        WHERE type = 2 AND movie_symlink = 1
        AND (stream_display_name LIKE '%CAM%' OR stream_display_name LIKE '%TS%')
        """
        result4 = await db_manager.execute_query(query4)
        print(f"   Symlink CAM/TS: {result4[0]['total']:,}")
        
        # CONSULTA 4: Direct_source duplicados
        print("\n📁 CONSULTA 4: DIRECT_SOURCE DUPLICADOS")
        print("-" * 30)
        
        query5 = """
        SELECT COUNT(DISTINCT stream_display_name) as titulos_duplicados
        FROM streams 
        WHERE type = 2 AND direct_source = 1
        AND stream_display_name IN (
            SELECT stream_display_name 
            FROM streams 
            WHERE type = 2 AND direct_source = 1 
            GROUP BY stream_display_name 
            HAVING COUNT(*) > 1
        )
        """
        result5 = await db_manager.execute_query(query5)
        print(f"   Títulos con duplicados direct_source: {result5[0]['titulos_duplicados']:,}")
        
        # CONSULTA 5: Muestra de duplicados direct_source
        print("\n📋 CONSULTA 5: MUESTRA DUPLICADOS DIRECT_SOURCE")
        print("-" * 30)
        
        query6 = """
        SELECT 
            stream_display_name,
            COUNT(*) as copias
        FROM streams 
        WHERE type = 2 AND direct_source = 1
        GROUP BY stream_display_name
        HAVING COUNT(*) > 1
        ORDER BY COUNT(*) DESC
        LIMIT 5
        """
        result6 = await db_manager.execute_query(query6)
        for i, row in enumerate(result6, 1):
            print(f"   {i}. '{row['stream_display_name'][:40]}' - {row['copias']} copias")
        
        # CONSULTA 6: Muestra de symlink CAM/TS
        print("\n🎭 CONSULTA 6: MUESTRA SYMLINK CAM/TS")
        print("-" * 30)
        
        query7 = """
        SELECT 
            id,
            stream_display_name,
            year
        FROM streams 
        WHERE type = 2 AND movie_symlink = 1
        AND (stream_display_name LIKE '%CAM%' OR stream_display_name LIKE '%TS%')
        ORDER BY added DESC
        LIMIT 5
        """
        result7 = await db_manager.execute_query(query7)
        for i, row in enumerate(result7, 1):
            print(f"   {i}. ID {row['id']} - {row['stream_display_name'][:35]} ({row['year']})")
        
        # RESUMEN DE GESTIÓN
        print("\n💡 RESUMEN DE GESTIÓN")
        print("-" * 30)
        
        symlink_total = result1[0]['total']
        direct_total = result2[0]['total']
        symlink_4k = result3[0]['total']
        symlink_cam = result4[0]['total']
        direct_duplicates = result5[0]['titulos_duplicados']
        
        print(f"   🔗 SYMLINK (VIVE): {symlink_total:,}")
        print(f"      - 4K (PROTEGER): {symlink_4k:,}")
        print(f"      - CAM/TS (REVISAR): {symlink_cam:,}")
        
        print(f"   📁 DIRECT_SOURCE (PURGAR): {direct_total:,}")
        print(f"      - Con duplicados: {direct_duplicates:,}")
        
        print("\n🎯 ACCIONES RECOMENDADAS:")
        print("   1. Revisar manualmente symlink CAM/TS")
        print("   2. Purgar duplicados direct_source")
        print("   3. Proteger todo el contenido 4K symlink")
        print("   4. Borrado manual seleccionable únicamente")
        
        print("\n✅ Consultas básicas completadas")
        
    except Exception as e:
        print(f"\n❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        if 'db_manager' in locals():
            await db_manager.close()
    
    return True

if __name__ == "__main__":
    success = asyncio.run(basic_queries())
    if success:
        print("\n🚀 Análisis básico exitoso")
    else:
        print("\n💥 Error en el análisis")
        sys.exit(1)
