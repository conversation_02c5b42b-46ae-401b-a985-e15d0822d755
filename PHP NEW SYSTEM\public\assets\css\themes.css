/**
 * Themes CSS - IPTV XUI One Content Manager
 * =========================================
 * 
 * Theme variations and color schemes
 */

/* ======================
   DARK THEME (DEFAULT)
   ====================== */

:root {
    --primary-color: #3b82f6;
    --secondary-color: #1e40af;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --dark-bg: #0f172a;
    --dark-surface: #1e293b;
    --dark-border: #334155;
    --light-bg: #ffffff;
    --light-surface: #f8fafc;
    --light-border: #e2e8f0;
    --text-primary: #f1f5f9;
    --text-secondary: #94a3b8;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ======================
   LIGHT THEME
   ====================== */

[data-theme="light"] {
    --primary-color: #3b82f6;
    --secondary-color: #1e40af;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --dark-bg: #ffffff;
    --dark-surface: #f8fafc;
    --dark-border: #e2e8f0;
    --light-bg: #0f172a;
    --light-surface: #1e293b;
    --light-border: #334155;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
}

[data-theme="light"] body {
    background: var(--dark-bg);
    color: var(--text-primary);
}

/* ======================
   BLUE THEME
   ====================== */

[data-theme="blue"] {
    --primary-color: #2563eb;
    --secondary-color: #1d4ed8;
    --success-color: #059669;
    --warning-color: #d97706;
    --error-color: #dc2626;
}

/* ======================
   GREEN THEME
   ====================== */

[data-theme="green"] {
    --primary-color: #059669;
    --secondary-color: #047857;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
}

/* ======================
   PURPLE THEME
   ====================== */

[data-theme="purple"] {
    --primary-color: #7c3aed;
    --secondary-color: #6d28d9;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
}

/* ======================
   RED THEME
   ====================== */

[data-theme="red"] {
    --primary-color: #dc2626;
    --secondary-color: #b91c1c;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
}

/* ======================
   ORANGE THEME
   ====================== */

[data-theme="orange"] {
    --primary-color: #ea580c;
    --secondary-color: #c2410c;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
}

/* ======================
   THEME TOGGLE BUTTON
   ====================== */

.theme-toggle {
    background: var(--dark-border);
    border: 1px solid var(--dark-border);
    border-radius: 0.5rem;
    padding: 0.75rem;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.theme-toggle:hover {
    background: var(--dark-surface);
    color: var(--primary-color);
    border-color: var(--primary-color);
}

/* ======================
   THEME SPECIFIC ICONS
   ====================== */

.stat-icon.movies {
    background: linear-gradient(135deg, #3b82f6, #1e40af);
}

.stat-icon.series {
    background: linear-gradient(135deg, #10b981, #059669);
}

.stat-icon.quality {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.stat-icon.storage {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.action-icon.upload {
    background: linear-gradient(135deg, #3b82f6, #1e40af);
}

.action-icon.analyze {
    background: linear-gradient(135deg, #10b981, #059669);
}

.action-icon.export {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.action-icon.settings {
    background: linear-gradient(135deg, #6b7280, #4b5563);
}

.metric-icon.content {
    background: linear-gradient(135deg, #3b82f6, #1e40af);
}

.metric-icon.quality {
    background: linear-gradient(135deg, #10b981, #059669);
}

.metric-icon.performance {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.metric-icon.users {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.config-icon.database {
    background: linear-gradient(135deg, #3b82f6, #1e40af);
}

.config-icon.api {
    background: linear-gradient(135deg, #10b981, #059669);
}

.config-icon.storage {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.config-icon.security {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.insight-icon.trend {
    background: linear-gradient(135deg, #10b981, #059669);
}

.insight-icon.warning {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.insight-icon.info {
    background: linear-gradient(135deg, #3b82f6, #1e40af);
}

/* ======================
   RESPONSIVE THEME ADJUSTMENTS
   ====================== */

@media (max-width: 768px) {
    .theme-toggle {
        padding: 0.5rem;
    }
    
    .theme-toggle span {
        display: none;
    }
}

/* ======================
   THEME TRANSITIONS
   ====================== */

* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* ======================
   HIGH CONTRAST MODE
   ====================== */

@media (prefers-contrast: high) {
    :root {
        --dark-border: #64748b;
        --text-secondary: #cbd5e1;
    }
}

/* ======================
   REDUCED MOTION
   ====================== */

@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* ======================
   PRINT STYLES
   ====================== */

@media print {
    :root {
        --dark-bg: #ffffff;
        --dark-surface: #ffffff;
        --dark-border: #000000;
        --text-primary: #000000;
        --text-secondary: #666666;
    }
    
    .theme-toggle,
    .btn,
    .sidebar {
        display: none !important;
    }
    
    .main-content {
        margin-left: 0 !important;
        padding: 1rem !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #000000 !important;
    }
}
