<?php
/**
 * M3U Parser for IPTV XUI One Content Manager
 * ==========================================
 * 
 * Parses M3U/M3U8 files and extracts metadata for content management.
 * Supports batch processing and real-time progress tracking.
 */

require_once __DIR__ . '/../config/settings.php';

class M3UParser {
    private $logger;
    private $stats = [
        'total_entries' => 0,
        'valid_entries' => 0,
        'invalid_entries' => 0,
        'movies' => 0,
        'tv_shows' => 0,
        'live_tv' => 0,
        'processing_time' => 0
    ];

    public function __construct() {
        $this->logger = $this->initializeLogger();
    }

    /**
     * Parse M3U file and return structured data
     */
    public function parseFile($filePath, $progressCallback = null) {
        $startTime = microtime(true);
        $this->resetStats();

        try {
            // Validate file
            $this->validateFile($filePath);
            
            // Read file content
            $content = $this->readFile($filePath);
            
            // Parse content
            $entries = $this->parseContent($content, $progressCallback);
            
            // Calculate processing time
            $this->stats['processing_time'] = microtime(true) - $startTime;
            
            $this->log('info', "M3U parsing completed", [
                'file' => basename($filePath),
                'entries' => count($entries),
                'time' => $this->stats['processing_time']
            ]);

            return [
                'entries' => $entries,
                'stats' => $this->stats
            ];

        } catch (Exception $e) {
            $this->log('error', "M3U parsing failed: " . $e->getMessage(), ['file' => $filePath]);
            throw $e;
        }
    }

    /**
     * Validate M3U file
     */
    private function validateFile($filePath) {
        if (!file_exists($filePath)) {
            throw new Exception("File not found: {$filePath}");
        }

        $fileSize = filesize($filePath);
        $maxSize = M3U_CONFIG['max_file_size'];

        if ($fileSize > $maxSize) {
            throw new Exception("File too large: {$fileSize} bytes (max: {$maxSize})");
        }

        $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
        if (!in_array($extension, M3U_CONFIG['supported_formats'])) {
            throw new Exception("Unsupported file format: {$extension}");
        }
    }

    /**
     * Read file with encoding detection
     */
    private function readFile($filePath) {
        $content = file_get_contents($filePath);
        
        if ($content === false) {
            throw new Exception("Failed to read file: {$filePath}");
        }

        // Detect and convert encoding
        $encoding = mb_detect_encoding($content, ['UTF-8', 'ISO-8859-1', 'Windows-1252'], true);
        
        if ($encoding && $encoding !== 'UTF-8') {
            $content = mb_convert_encoding($content, 'UTF-8', $encoding);
            $this->log('info', "Converted encoding from {$encoding} to UTF-8");
        }

        return $content;
    }

    /**
     * Parse M3U content
     */
    private function parseContent($content, $progressCallback = null) {
        $lines = explode("\n", $content);
        $entries = [];
        $currentEntry = null;
        $totalLines = count($lines);
        $processedLines = 0;

        foreach ($lines as $lineNumber => $line) {
            $line = trim($line);
            $processedLines++;

            // Skip empty lines and comments (except EXTINF)
            if (empty($line) || ($line[0] === '#' && !str_starts_with($line, '#EXTINF'))) {
                continue;
            }

            // Process EXTINF line
            if (str_starts_with($line, '#EXTINF')) {
                $currentEntry = $this->parseExtinfLine($line);
                $this->stats['total_entries']++;
            }
            // Process URL line
            elseif ($currentEntry && filter_var($line, FILTER_VALIDATE_URL)) {
                $currentEntry['url'] = $line;
                $currentEntry = $this->enrichEntry($currentEntry);
                
                if ($this->validateEntry($currentEntry)) {
                    $entries[] = $currentEntry;
                    $this->stats['valid_entries']++;
                    $this->updateContentTypeStats($currentEntry['content_type']);
                } else {
                    $this->stats['invalid_entries']++;
                }
                
                $currentEntry = null;
            }

            // Progress callback
            if ($progressCallback && $processedLines % 100 === 0) {
                $progress = ($processedLines / $totalLines) * 100;
                call_user_func($progressCallback, $progress, $processedLines, $totalLines);
            }
        }

        return $entries;
    }

    /**
     * Parse EXTINF line
     */
    private function parseExtinfLine($line) {
        $entry = [
            'duration' => -1,
            'title' => '',
            'attributes' => [],
            'raw_line' => $line
        ];

        // Extract duration
        if (preg_match('/^#EXTINF:\s*(-?\d+(?:\.\d+)?)/', $line, $matches)) {
            $entry['duration'] = (float)$matches[1];
        }

        // Extract attributes
        $entry['attributes'] = $this->extractAttributes($line);

        // Extract title (after the comma)
        if (preg_match('/,(.+)$/', $line, $matches)) {
            $entry['title'] = trim($matches[1]);
        }

        return $entry;
    }

    /**
     * Extract attributes from EXTINF line
     */
    private function extractAttributes($line) {
        $attributes = [];
        
        // Common M3U attributes
        $patterns = [
            'tvg-id' => '/tvg-id="([^"]*)"/',
            'tvg-name' => '/tvg-name="([^"]*)"/',
            'tvg-logo' => '/tvg-logo="([^"]*)"/',
            'tvg-chno' => '/tvg-chno="([^"]*)"/',
            'tvg-shift' => '/tvg-shift="([^"]*)"/',
            'group-title' => '/group-title="([^"]*)"/',
            'radio' => '/radio="([^"]*)"/',
            'logo' => '/logo="([^"]*)"/'
        ];

        foreach ($patterns as $key => $pattern) {
            if (preg_match($pattern, $line, $matches)) {
                $attributes[$key] = $matches[1];
            }
        }

        return $attributes;
    }

    /**
     * Enrich entry with additional metadata
     */
    private function enrichEntry($entry) {
        // Detect content type
        $entry['content_type'] = $this->detectContentType($entry['title'], $entry['attributes']['group-title'] ?? '');
        
        // Extract quality information
        $entry['quality'] = $this->extractQuality($entry['title']);
        
        // Extract language
        $entry['language'] = $this->extractLanguage($entry['title']);
        
        // Extract year
        $entry['year'] = $this->extractYear($entry['title']);
        
        // Clean title for TMDB matching
        $entry['clean_title'] = $this->cleanTitleForTMDB($entry['title']);
        
        // Set logo from attributes
        $entry['logo'] = $entry['attributes']['tvg-logo'] ?? $entry['attributes']['logo'] ?? '';
        
        return $entry;
    }

    /**
     * Detect content type (movie, tv_show, live_tv)
     */
    private function detectContentType($title, $groupTitle = '') {
        $title = strtolower($title);
        $groupTitle = strtolower($groupTitle);

        // Live TV indicators
        $liveTvKeywords = ['live', 'tv', 'canal', 'channel', 'news', 'sport', 'music'];
        foreach ($liveTvKeywords as $keyword) {
            if (strpos($groupTitle, $keyword) !== false) {
                return 'live_tv';
            }
        }

        // Series indicators
        $seriesKeywords = ['s0', 'season', 'episode', 'ep', 'temporada', 'capitulo'];
        foreach ($seriesKeywords as $keyword) {
            if (strpos($title, $keyword) !== false) {
                return 'tv_show';
            }
        }

        // Movie indicators (default)
        return 'movie';
    }

    /**
     * Extract quality from title
     */
    private function extractQuality($title) {
        $qualities = [];
        
        foreach (CONTENT_CONFIG['quality_detection'] as $quality => $keywords) {
            foreach ($keywords as $keyword) {
                if (stripos($title, $keyword) !== false) {
                    $qualities[] = $quality;
                    break;
                }
            }
        }

        return $qualities;
    }

    /**
     * Extract language from title
     */
    private function extractLanguage($title) {
        $languages = [
            'es' => ['spanish', 'español', 'castellano', 'esp'],
            'en' => ['english', 'inglés', 'eng'],
            'fr' => ['french', 'français', 'fra'],
            'de' => ['german', 'deutsch', 'ger'],
            'it' => ['italian', 'italiano', 'ita'],
            'pt' => ['portuguese', 'português', 'por']
        ];

        $title = strtolower($title);
        
        foreach ($languages as $code => $keywords) {
            foreach ($keywords as $keyword) {
                if (strpos($title, $keyword) !== false) {
                    return $code;
                }
            }
        }

        return 'unknown';
    }

    /**
     * Extract year from title
     */
    private function extractYear($title) {
        if (preg_match('/\b(19|20)\d{2}\b/', $title, $matches)) {
            return (int)$matches[0];
        }
        return null;
    }

    /**
     * Clean title for TMDB matching
     */
    private function cleanTitleForTMDB($title) {
        $patterns = [
            '/\[.*?\]/',           // Remove [brackets]
            '/\(.*?\)/',           // Remove (parentheses)
            '/\d{4}/',             // Remove years
            '/\b(HD|FHD|4K|1080p|720p|480p|HDR|60fps|BluRay|DVDRip|WEBRip|x264|x265|HEVC)\b/i',
            '/\s+/'                // Multiple spaces to single
        ];

        $cleaned = $title;
        foreach ($patterns as $pattern) {
            $cleaned = preg_replace($pattern, ' ', $cleaned);
        }

        return trim($cleaned);
    }

    /**
     * Validate entry
     */
    private function validateEntry($entry) {
        return !empty($entry['title']) && 
               !empty($entry['url']) && 
               filter_var($entry['url'], FILTER_VALIDATE_URL);
    }

    /**
     * Update content type statistics
     */
    private function updateContentTypeStats($contentType) {
        switch ($contentType) {
            case 'movie':
                $this->stats['movies']++;
                break;
            case 'tv_show':
                $this->stats['tv_shows']++;
                break;
            case 'live_tv':
                $this->stats['live_tv']++;
                break;
        }
    }

    /**
     * Reset statistics
     */
    private function resetStats() {
        $this->stats = [
            'total_entries' => 0,
            'valid_entries' => 0,
            'invalid_entries' => 0,
            'movies' => 0,
            'tv_shows' => 0,
            'live_tv' => 0,
            'processing_time' => 0
        ];
    }

    /**
     * Get parsing statistics
     */
    public function getStats() {
        return $this->stats;
    }

    /**
     * Initialize logger
     */
    private function initializeLogger() {
        return new class {
            public function log($level, $message, $context = []) {
                if (LOGGING_CONFIG['enabled']) {
                    $logEntry = sprintf(
                        "[%s] M3UParser %s: %s %s\n",
                        date('Y-m-d H:i:s'),
                        strtoupper($level),
                        $message,
                        !empty($context) ? json_encode($context) : ''
                    );
                    file_put_contents(LOGGING_CONFIG['file_path'], $logEntry, FILE_APPEND | LOCK_EX);
                }
            }
        };
    }

    private function log($level, $message, $context = []) {
        $this->logger->log($level, $message, $context);
    }
}
