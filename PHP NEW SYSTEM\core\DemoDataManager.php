<?php
/**
 * Demo Data Manager - IPTV XUI One Content Manager
 * ================================================
 * 
 * Provides demo data when database is not available
 * Useful for testing and demonstration purposes
 */

class DemoDataManager {
    
    private static $instance = null;
    private $demoData;
    
    private function __construct() {
        $this->initializeDemoData();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function initializeDemoData() {
        $this->demoData = [
            'content_stats' => [
                'total_movies' => 1250,
                'total_series' => 340,
                'total_episodes' => 8920,
                'total_size' => '2.5TB',
                'symlink_movies' => 890,
                'direct_movies' => 360,
                'live_tv' => 450
            ],
            
            'quality_stats' => [
                '4K' => 450,
                'FHD' => 680,
                'HD' => 320,
                'SD' => 140,
                '60fps' => 280,
                'HDR' => 120
            ],
            
            'recent_content' => [
                [
                    'id' => 1,
                    'title' => 'Dune: Part Two',
                    'type' => 'movie',
                    'quality' => '4K',
                    'year' => 2024,
                    'rating' => 8.5,
                    'added' => '2025-01-08',
                    'category' => 'Sci-Fi',
                    'poster' => 'https://image.tmdb.org/t/p/w500/1pdfLvkbY9ohJlCjQH2CZjjYVvJ.jpg'
                ],
                [
                    'id' => 2,
                    'title' => 'The Bear - Season 3',
                    'type' => 'series',
                    'quality' => 'FHD',
                    'year' => 2024,
                    'rating' => 9.2,
                    'added' => '2025-01-07',
                    'category' => 'Comedy-Drama',
                    'poster' => 'https://image.tmdb.org/t/p/w500/sHFlbKS3WLqMnp9t2ghADIJFnuQ.jpg'
                ],
                [
                    'id' => 3,
                    'title' => 'Oppenheimer',
                    'type' => 'movie',
                    'quality' => '4K HDR',
                    'year' => 2023,
                    'rating' => 8.8,
                    'added' => '2025-01-06',
                    'category' => 'Biography',
                    'poster' => 'https://image.tmdb.org/t/p/w500/8Gxv8gSFCU0XGDykEGv7zR1n2ua.jpg'
                ],
                [
                    'id' => 4,
                    'title' => 'House of the Dragon - S2',
                    'type' => 'series',
                    'quality' => '4K',
                    'year' => 2024,
                    'rating' => 8.4,
                    'added' => '2025-01-05',
                    'category' => 'Fantasy',
                    'poster' => 'https://image.tmdb.org/t/p/w500/7QMsOTMUswlwxJP0rTTZfmz2tX2.jpg'
                ],
                [
                    'id' => 5,
                    'title' => 'Top Gun: Maverick',
                    'type' => 'movie',
                    'quality' => '4K 60fps',
                    'year' => 2022,
                    'rating' => 8.7,
                    'added' => '2025-01-04',
                    'category' => 'Action',
                    'poster' => 'https://image.tmdb.org/t/p/w500/62HCnUTziyWcpDaBO2i1DX17ljH.jpg'
                ]
            ],
            
            'popular_content' => [
                [
                    'id' => 1,
                    'title' => 'Breaking Bad',
                    'type' => 'series',
                    'views' => 15420,
                    'rating' => 9.5,
                    'category' => 'Crime Drama',
                    'poster' => 'https://image.tmdb.org/t/p/w500/3xnWaLQjelJDDF7LT1WBo6f4BRe.jpg'
                ],
                [
                    'id' => 2,
                    'title' => 'The Godfather',
                    'type' => 'movie',
                    'views' => 12850,
                    'rating' => 9.2,
                    'category' => 'Crime',
                    'poster' => 'https://image.tmdb.org/t/p/w500/3bhkrj58Vtu7enYsRolD1fZdja1.jpg'
                ],
                [
                    'id' => 3,
                    'title' => 'Stranger Things',
                    'type' => 'series',
                    'views' => 11200,
                    'rating' => 8.7,
                    'category' => 'Sci-Fi Horror',
                    'poster' => 'https://image.tmdb.org/t/p/w500/49WJfeN0moxb9IPfGn8AIqMGskD.jpg'
                ],
                [
                    'id' => 4,
                    'title' => 'Inception',
                    'type' => 'movie',
                    'views' => 9750,
                    'rating' => 8.8,
                    'category' => 'Sci-Fi',
                    'poster' => 'https://image.tmdb.org/t/p/w500/9gk7adHYeDvHkCSEqAvQNLV5Uge.jpg'
                ],
                [
                    'id' => 5,
                    'title' => 'The Last of Us',
                    'type' => 'series',
                    'views' => 8900,
                    'rating' => 8.9,
                    'category' => 'Post-Apocalyptic',
                    'poster' => 'https://image.tmdb.org/t/p/w500/uKvVjHNqB5VmOrdxqAt2F7J78ED.jpg'
                ]
            ],
            
            'categories' => [
                ['id' => 1, 'name' => 'Action', 'count' => 245],
                ['id' => 2, 'name' => 'Comedy', 'count' => 189],
                ['id' => 3, 'name' => 'Drama', 'count' => 312],
                ['id' => 4, 'name' => 'Sci-Fi', 'count' => 156],
                ['id' => 5, 'name' => 'Horror', 'count' => 98],
                ['id' => 6, 'name' => 'Romance', 'count' => 134],
                ['id' => 7, 'name' => 'Thriller', 'count' => 167],
                ['id' => 8, 'name' => 'Documentary', 'count' => 89]
            ],
            
            'analytics_data' => [
                'content_by_year' => [
                    ['year' => 2024, 'count' => 145, 'avg_rating' => 7.8],
                    ['year' => 2023, 'count' => 234, 'avg_rating' => 7.6],
                    ['year' => 2022, 'count' => 198, 'avg_rating' => 7.4],
                    ['year' => 2021, 'count' => 167, 'avg_rating' => 7.2],
                    ['year' => 2020, 'count' => 189, 'avg_rating' => 7.1]
                ],
                
                'quality_distribution' => [
                    ['quality' => '4K', 'count' => 450, 'percentage' => 28.5],
                    ['quality' => 'FHD', 'count' => 680, 'percentage' => 43.2],
                    ['quality' => 'HD', 'count' => 320, 'percentage' => 20.3],
                    ['quality' => 'SD', 'count' => 140, 'percentage' => 8.0]
                ],
                
                'monthly_additions' => [
                    ['month' => 'Jan 2025', 'count' => 45],
                    ['month' => 'Dec 2024', 'count' => 67],
                    ['month' => 'Nov 2024', 'count' => 52],
                    ['month' => 'Oct 2024', 'count' => 78],
                    ['month' => 'Sep 2024', 'count' => 61],
                    ['month' => 'Aug 2024', 'count' => 84]
                ]
            ]
        ];
    }
    
    public function getContentStats() {
        return $this->demoData['content_stats'];
    }
    
    public function getQualityStats() {
        return $this->demoData['quality_stats'];
    }
    
    public function getRecentAdditions($limit = 5) {
        return array_slice($this->demoData['recent_content'], 0, $limit);
    }
    
    public function getPopularContent($limit = 5) {
        return array_slice($this->demoData['popular_content'], 0, $limit);
    }
    
    public function getCategories() {
        return $this->demoData['categories'];
    }
    
    public function getAnalyticsData() {
        return $this->demoData['analytics_data'];
    }
    
    public function testConnection() {
        return true; // Always return true for demo mode
    }
    
    public function searchContent($query, $limit = 50, $offset = 0) {
        // Simple search simulation
        $results = array_filter($this->demoData['recent_content'], function($item) use ($query) {
            return stripos($item['title'], $query) !== false;
        });
        
        return array_slice($results, $offset, $limit);
    }
    
    public function getContentByCategory($categoryId, $limit = 50, $offset = 0) {
        // Simulate category filtering
        return array_slice($this->demoData['recent_content'], $offset, $limit);
    }
    
    public function getContentByQuality($quality, $limit = 50, $offset = 0) {
        // Simulate quality filtering
        $results = array_filter($this->demoData['recent_content'], function($item) use ($quality) {
            return stripos($item['quality'], $quality) !== false;
        });
        
        return array_slice($results, $offset, $limit);
    }
    
    public function getDuplicateAnalysis($limit = 50, $offset = 0) {
        // Simulate duplicate analysis
        return [
            [
                'title' => 'The Matrix',
                'duplicate_count' => 3,
                'symlink_count' => 2,
                'direct_count' => 1,
                'qualities' => ['4K', 'FHD', 'HD']
            ],
            [
                'title' => 'Avengers: Endgame',
                'duplicate_count' => 2,
                'symlink_count' => 1,
                'direct_count' => 1,
                'qualities' => ['4K', 'FHD']
            ]
        ];
    }
    
    public function getSystemStatus() {
        return [
            'database_status' => 'Demo Mode',
            'total_connections' => 1,
            'active_queries' => 0,
            'cache_status' => 'Active',
            'last_update' => date('Y-m-d H:i:s')
        ];
    }
}
?>
