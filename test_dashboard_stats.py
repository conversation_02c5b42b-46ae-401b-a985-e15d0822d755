"""
Script simple para probar las estadísticas del dashboard
"""

import asyncio
import sys
import os

# Agregar el directorio raíz al path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.settings import Settings
from core.database_manager import DatabaseManager

async def test_dashboard_stats():
    """Probar las estadísticas que usa el dashboard"""
    try:
        # Inicializar configuración
        settings = Settings()
        print(f"✓ Settings inicializados - Tema: {settings.theme}")
        print(f"✓ Colores disponibles: {list(settings.colors.keys())}")
        
        # Inicializar database manager
        db_manager = DatabaseManager(settings)
        
        # Probar conexión
        if not await db_manager.test_connection():
            print("✗ Error de conexión")
            return
            
        if not await db_manager.verify_tables():
            print("✗ Error en verificación de tablas")
            return
            
        print("✓ Base de datos conectada y verificada")
        
        # Obtener estadísticas
        stats = await db_manager.get_content_stats()
        print(f"✓ Estadísticas obtenidas: {stats}")
        
        # Simular lo que hace el dashboard
        print("\\nSimulando actualización de tarjetas del dashboard:")
        updates = {
            "movies": str(stats.get("total_movies", 0)),
            "series": str(stats.get("total_series", 0)),
            "m3u_entries": str(stats.get("total_live", 0)),
            "duplicates": str(stats.get("without_tmdb", 0))
        }
        
        for key, value in updates.items():
            print(f"  {key}: {value}")
            
        print("\\n✓ Las estadísticas se están procesando correctamente")
        
    except Exception as e:
        print(f"✗ Error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_dashboard_stats())
