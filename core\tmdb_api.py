"""
Cliente API de TMDB
===================

Maneja todas las operaciones con la API de The Movie Database (TMDB)
para obtener metadatos de películas y series de TV.
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import aiohttp
from dataclasses import dataclass

@dataclass
class TMDBContent:
    """Clase para contenido de TMDB"""
    id: int
    title: str
    original_title: str
    overview: str
    release_date: str
    vote_average: float
    vote_count: int
    popularity: float
    poster_path: str
    backdrop_path: str
    genre_ids: List[int]
    adult: bool
    original_language: str

class TMDBClient:
    """Cliente para la API de TMDB"""
    
    def __init__(self, settings, db_manager):
        self.settings = settings
        self.db_manager = db_manager
        self.logger = logging.getLogger("iptv_manager.tmdb")
        self.base_url = settings.tmdb_base_url
        self.image_base_url = settings.tmdb_image_base_url
        self.headers = settings.get_tmdb_headers()
        self.session = None
        
    async def __aenter__(self):
        """Inicializar sesión HTTP"""
        self.session = aiohttp.ClientSession(
            headers=self.headers,
            timeout=aiohttp.ClientTimeout(total=self.settings.timeout)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Cerrar sesión HTTP"""
        if self.session:
            await self.session.close()
    
    async def _make_request(self, endpoint: str, params: Dict | None = None) -> Dict:
        """Realizar petición HTTP a TMDB"""
        if not self.session:
            raise RuntimeError("Session not initialized. Use 'async with' context.")
        
        url = f"{self.base_url}/{endpoint}"
        
        # Verificar cache si está habilitado
        cache_key = f"tmdb_{endpoint}_{json.dumps(params or {}, sort_keys=True)}"
        
        if self.settings.cache_enabled:
            cached_result = await self.db_manager.get_cache(cache_key)
            if cached_result:
                return json.loads(cached_result)
        
        try:
            async with self.session.get(url, params=params) as response:
                response.raise_for_status()
                data = await response.json()
                
                # Guardar en cache
                if self.settings.cache_enabled:
                    await self.db_manager.set_cache(
                        cache_key, 
                        json.dumps(data), 
                        self.settings.cache_duration
                    )
                
                return data
                
        except aiohttp.ClientError as e:
            self.logger.error(f"Error en petición TMDB: {str(e)}")
            raise
        except Exception as e:
            self.logger.error(f"Error inesperado en TMDB: {str(e)}")
            raise
    
    async def search_movies(self, query: str, page: int = 1, year: int | None = None) -> List[Dict]:
        """Buscar películas"""
        params = {
            "query": query,
            "page": page,
            "include_adult": "false",
            "language": "es-ES"
        }
        
        if year:
            params["year"] = year
        
        try:
            data = await self._make_request("search/movie", params)
            return data.get("results", [])
        except Exception as e:
            self.logger.error(f"Error al buscar películas: {str(e)}")
            return []
    
    async def search_tv_shows(self, query: str, page: int = 1, year: int | None = None) -> List[Dict]:
        """Buscar series de TV"""
        params = {
            "query": query,
            "page": page,
            "include_adult": "false",
            "language": "es-ES"
        }
        
        if year:
            params["first_air_date_year"] = year
        
        try:
            data = await self._make_request("search/tv", params)
            return data.get("results", [])
        except Exception as e:
            self.logger.error(f"Error al buscar series: {str(e)}")
            return []
    
    async def get_movie_details(self, movie_id: int) -> Optional[Dict]:
        """Obtener detalles de película"""
        try:
            params = {"language": "es-ES"}
            return await self._make_request(f"movie/{movie_id}", params)
        except Exception as e:
            self.logger.error(f"Error al obtener detalles de película {movie_id}: {str(e)}")
            return None
    
    async def get_tv_show_details(self, tv_id: int) -> Optional[Dict]:
        """Obtener detalles de serie de TV"""
        try:
            params = {"language": "es-ES"}
            return await self._make_request(f"tv/{tv_id}", params)
        except Exception as e:
            self.logger.error(f"Error al obtener detalles de serie {tv_id}: {str(e)}")
            return None
    
    async def get_popular_movies(self, page: int = 1) -> List[Dict]:
        """Obtener películas populares"""
        try:
            params = {"page": page, "language": "es-ES"}
            data = await self._make_request("movie/popular", params)
            return data.get("results", [])
        except Exception as e:
            self.logger.error(f"Error al obtener películas populares: {str(e)}")
            return []
    
    async def get_popular_tv_shows(self, page: int = 1) -> List[Dict]:
        """Obtener series populares"""
        try:
            params = {"page": page, "language": "es-ES"}
            data = await self._make_request("tv/popular", params)
            return data.get("results", [])
        except Exception as e:
            self.logger.error(f"Error al obtener series populares: {str(e)}")
            return []
    
    async def get_trending_movies(self, time_window: str = "week") -> List[Dict]:
        """Obtener películas en tendencia"""
        try:
            params = {"language": "es-ES"}
            data = await self._make_request(f"trending/movie/{time_window}", params)
            return data.get("results", [])
        except Exception as e:
            self.logger.error(f"Error al obtener películas en tendencia: {str(e)}")
            return []
    
    async def get_trending_tv_shows(self, time_window: str = "week") -> List[Dict]:
        """Obtener series en tendencia"""
        try:
            params = {"language": "es-ES"}
            data = await self._make_request(f"trending/tv/{time_window}", params)
            return data.get("results", [])
        except Exception as e:
            self.logger.error(f"Error al obtener series en tendencia: {str(e)}")
            return []
    
    async def get_genres(self, content_type: str = "movie") -> List[Dict]:
        """Obtener géneros"""
        try:
            params = {"language": "es-ES"}
            data = await self._make_request(f"genre/{content_type}/list", params)
            return data.get("genres", [])
        except Exception as e:
            self.logger.error(f"Error al obtener géneros: {str(e)}")
            return []
    
    async def match_content(self, title: str, content_type: str = "movie", year: int | None = None) -> Optional[Dict]:
        """Encontrar mejor coincidencia para contenido"""
        try:
            # Limpiar título
            clean_title = self._clean_title(title)
            
            # Buscar contenido
            if content_type == "movie":
                results = await self.search_movies(clean_title, year=year)
            else:
                results = await self.search_tv_shows(clean_title, year=year)
            
            if not results:
                return None
            
            # Encontrar mejor coincidencia
            best_match = self._find_best_match(clean_title, results)
            
            if best_match:
                self.logger.info(f"Coincidencia encontrada para '{title}': {best_match.get('title', best_match.get('name'))}")
                return best_match
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error al buscar coincidencia para '{title}': {str(e)}")
            return None
    
    def _clean_title(self, title: str) -> str:
        """Limpiar título para búsqueda"""
        # Remover caracteres especiales y normalizar
        import re
        
        # Remover año entre paréntesis
        title = re.sub(r'\(\d{4}\)', '', title)
        
        # Remover calidad (HD, 4K, etc.)
        title = re.sub(r'\b(HD|4K|1080p|720p|480p|DVDRip|BRRip|WEBRip)\b', '', title, flags=re.IGNORECASE)
        
        # Remover idioma
        title = re.sub(r'\b(SPANISH|LATINO|ENGLISH|SUBTITULADO|DUBBED)\b', '', title, flags=re.IGNORECASE)
        
        # Remover caracteres especiales
        title = re.sub(r'[^\w\s]', ' ', title)
        
        # Normalizar espacios
        title = ' '.join(title.split())
        
        return title.strip()
    
    def _find_best_match(self, search_title: str, results: List[Dict]) -> Optional[Dict]:
        """Encontrar mejor coincidencia usando similitud de texto"""
        if not results:
            return None
        
        # Función simple de similitud
        def similarity(a: str, b: str) -> float:
            a, b = a.lower(), b.lower()
            if a == b:
                return 1.0
            
            # Similitud basada en palabras comunes
            words_a = set(a.split())
            words_b = set(b.split())
            
            if not words_a or not words_b:
                return 0.0
            
            intersection = words_a & words_b
            union = words_a | words_b
            
            return len(intersection) / len(union)
        
        best_match = None
        best_score = 0.0
        
        for result in results:
            title = result.get("title", result.get("name", ""))
            original_title = result.get("original_title", result.get("original_name", ""))
            
            # Calcular similitud con título y título original
            score1 = similarity(search_title, title)
            score2 = similarity(search_title, original_title)
            
            max_score = max(score1, score2)
            
            if max_score > best_score:
                best_score = max_score
                best_match = result
        
        # Retornar solo si la similitud es razonablemente alta
        return best_match if best_score > 0.5 else None
    
    def get_image_url(self, path: str, size: str = "w500") -> str:
        """Obtener URL completa de imagen"""
        if not path:
            return ""
        
        if path.startswith("http"):
            return path
        
        return f"https://image.tmdb.org/t/p/{size}{path}"
    
    async def sync_popular_content(self, max_pages: int = 5) -> Dict[str, int]:
        """Sincronizar contenido popular de TMDB"""
        stats = {"movies": 0, "tv_shows": 0}
        
        try:
            # Sincronizar películas populares
            for page in range(1, max_pages + 1):
                movies = await self.get_popular_movies(page)
                for movie in movies:
                    await self.db_manager.insert_movie(movie)
                    stats["movies"] += 1
                
                # Pequeña pausa entre páginas
                await asyncio.sleep(0.1)
            
            # Sincronizar series populares
            for page in range(1, max_pages + 1):
                tv_shows = await self.get_popular_tv_shows(page)
                for tv_show in tv_shows:
                    # Adaptar datos de TV para inserción
                    tv_data = {
                        "tmdb_id": tv_show.get("id"),
                        "name": tv_show.get("name"),
                        "original_name": tv_show.get("original_name"),
                        "overview": tv_show.get("overview"),
                        "first_air_date": tv_show.get("first_air_date"),
                        "vote_average": tv_show.get("vote_average"),
                        "vote_count": tv_show.get("vote_count"),
                        "popularity": tv_show.get("popularity"),
                        "poster_path": tv_show.get("poster_path"),
                        "backdrop_path": tv_show.get("backdrop_path"),
                        "genre_ids": json.dumps(tv_show.get("genre_ids", [])),
                        "adult": tv_show.get("adult", False),
                        "original_language": tv_show.get("original_language")
                    }
                    
                    # Insertar en base de datos (necesitaríamos un método para TV shows)
                    # await self.db_manager.insert_tv_show(tv_data)
                    stats["tv_shows"] += 1
                
                await asyncio.sleep(0.1)
            
            self.logger.info(f"Sincronización completada: {stats}")
            return stats
            
        except Exception as e:
            self.logger.error(f"Error en sincronización: {str(e)}")
            raise
