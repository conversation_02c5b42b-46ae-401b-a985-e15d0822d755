# 🌐 Hosting Setup Guide - IPTV XUI One Content Manager

## 🚀 Quick Hosting Deployment

### 📋 Pre-Upload Checklist

1. **Hosting Requirements:**
   - PHP 8.0 or higher
   - MySQL/MariaDB database
   - At least 512MB RAM
   - 1GB storage space

2. **Database Information:**
   - Database name
   - Database username
   - Database password
   - Database host (usually `localhost`)

3. **TMDB API Key:**
   - Get free API key from [themoviedb.org](https://www.themoviedb.org/settings/api)

---

## 📤 Upload Instructions

### Step 1: Upload Files
1. **Compress the entire "PHP NEW SYSTEM" folder** into a ZIP file
2. **Upload to your hosting** (via cPanel File Manager, FTP, etc.)
3. **Extract in your domain's root directory** (public_html, www, or htdocs)

### Step 2: Set Permissions
Set these folder permissions to **755** (or **777** if 755 doesn't work):
```
logs/
uploads/
uploads/temp/
```

### Step 3: Configure Database
1. **Edit `config/database.php`**
2. **Update these lines:**
```php
'host' => 'localhost',           // Your DB host
'database' => 'your_db_name',    // Your database name
'username' => 'your_db_user',    // Your DB username
'password' => 'your_db_pass',    // Your DB password
```

### Step 4: Configure TMDB (Optional)
1. **Edit `config/tmdb_config.php`**
2. **Add your API key:**
```php
'api_key' => 'your_tmdb_api_key_here'
```

---

## 🔍 Testing Your Installation

### Step 1: System Check
1. **Visit:** `https://yourdomain.com/system-check.php`
2. **Verify all items show "OK"**
3. **Fix any issues before proceeding**

### Step 2: Launch Application
1. **Visit:** `https://yourdomain.com/`
2. **You should see the dashboard**
3. **Test uploading a small M3U file**

---

## 🛠️ Common Hosting Issues & Solutions

### Issue 1: "File not found" errors
**Solution:** Check that `.htaccess` file was uploaded and is working
```apache
# Add this to .htaccess if missing:
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [L,QSA]
```

### Issue 2: "Permission denied" errors
**Solution:** Set correct folder permissions
```bash
# Via SSH (if available):
chmod 755 logs/ uploads/ uploads/temp/

# Via cPanel File Manager:
# Right-click folder → Change Permissions → Set to 755
```

### Issue 3: "Database connection failed"
**Solution:** Verify database credentials
1. Check database name, username, password
2. Ensure database user has full permissions
3. Test connection from hosting control panel

### Issue 4: "PHP extension missing"
**Solution:** Contact hosting provider or enable in cPanel
- Most shared hosting includes required extensions
- Check PHP version is 8.0 or higher

### Issue 5: Upload size limits
**Solution:** Increase PHP limits (if allowed)
```php
# Add to .htaccess:
php_value upload_max_filesize 50M
php_value post_max_size 50M
php_value memory_limit 512M
```

---

## 🔧 Hosting-Specific Instructions

### cPanel Hosting
1. **Upload via File Manager**
2. **Extract ZIP in public_html**
3. **Use MySQL Databases to create DB**
4. **Set permissions via File Manager**

### Shared Hosting (Generic)
1. **Upload to www/ or htdocs/**
2. **Create database via control panel**
3. **Set folder permissions to 755**
4. **Test with system-check.php**

### VPS/Dedicated Server
1. **Upload anywhere accessible by web server**
2. **Configure virtual host to point to project root**
3. **Set proper ownership and permissions**
4. **Configure database manually**

---

## 📱 Mobile-Friendly Testing

The interface is fully responsive. Test on:
- **Desktop browsers** (Chrome, Firefox, Safari, Edge)
- **Mobile devices** (iOS Safari, Chrome Mobile)
- **Tablets** (iPad, Android tablets)

---

## 🔐 Security Recommendations

### For Production Use:
1. **Change default passwords**
2. **Enable HTTPS** (SSL certificate)
3. **Restrict database access**
4. **Regular backups**
5. **Keep PHP updated**

### Optional Security Headers:
Add to `.htaccess`:
```apache
Header always set X-Frame-Options "SAMEORIGIN"
Header always set X-Content-Type-Options "nosniff"
Header always set X-XSS-Protection "1; mode=block"
```

---

## 📞 Troubleshooting

### If Something Goes Wrong:

1. **Check system-check.php first**
2. **Look at error logs** (cPanel → Error Logs)
3. **Verify file permissions**
4. **Test database connection separately**
5. **Check PHP error log** in logs/ folder

### Getting Help:

1. **Run system-check.php** and note any red items
2. **Check browser console** for JavaScript errors
3. **Look at server error logs**
4. **Verify all files uploaded correctly**

---

## 🎯 Quick Start Checklist

- [ ] Files uploaded and extracted
- [ ] Folder permissions set (755)
- [ ] Database configured in config/database.php
- [ ] TMDB API key added (optional)
- [ ] system-check.php shows all green
- [ ] Main application loads at yourdomain.com
- [ ] Test M3U upload works
- [ ] Dashboard shows statistics

---

## 🎉 Success!

Once everything is working:

1. **Upload your M3U files** via the drag & drop interface
2. **Enable TMDB enrichment** for better metadata
3. **Explore the analytics** to see your content statistics
4. **Use the content browser** to manage your library
5. **Export your organized content** in various formats

**🎬 Your IPTV content management system is now live and ready to use!**

---

**Need help?** Check the main documentation in `README_PHP.md` or `INSTALLATION.md`
