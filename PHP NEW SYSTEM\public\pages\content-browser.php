<?php
/**
 * Content Browser Page - IPTV XUI One Content Manager
 * ==================================================
 * 
 * Browse, search, and manage content with advanced filtering
 */

// Include core classes
require_once __DIR__ . '/../../core/DatabaseManager.php';

// Get parameters
$page = (int)($_GET['p'] ?? 1);
$limit = (int)($_GET['limit'] ?? 50);
$search = $_GET['search'] ?? '';
$filter = $_GET['filter'] ?? 'all';
$sort = $_GET['sort'] ?? 'added';
$order = $_GET['order'] ?? 'desc';

$offset = ($page - 1) * $limit;

try {
    $db = new DatabaseManager();
    
    // Get content based on filter
    switch ($filter) {
        case 'symlink':
            $content = $db->getSymlinkContent($limit, $offset);
            $title = 'Symlink Content (Protected)';
            break;
        case 'direct':
            $content = $db->getDirectSourceContent($limit, $offset);
            $title = 'Direct Source Content';
            break;
        case '4k':
            $content = $db->get4KContent($limit, $offset);
            $title = '4K Content';
            break;
        case '60fps':
            $content = $db->get60FpsContent($limit, $offset);
            $title = '60fps Content';
            break;
        case 'missing_tmdb':
            $content = $db->getMissingTMDBContent($limit, $offset);
            $title = 'Missing TMDB Data';
            break;
        default:
            if ($search) {
                $content = $db->searchContent($search, $limit, $offset);
                $title = "Search Results for: " . htmlspecialchars($search);
            } else {
                $content = $db->getMovies($limit, $offset);
                $title = 'All Content';
            }
    }
    
    $categories = $db->getCategories();
    
} catch (Exception $e) {
    $error = $e->getMessage();
    $content = [];
    $categories = [];
    $title = 'Content Browser';
}
?>

<style>
.browser-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.browser-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.browser-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.search-box {
    position: relative;
}

.search-input {
    background: var(--dark-bg);
    border: 1px solid var(--dark-border);
    border-radius: 0.5rem;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    color: var(--text-primary);
    width: 300px;
    transition: var(--transition);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
}

.filter-select {
    background: var(--dark-bg);
    border: 1px solid var(--dark-border);
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
    color: var(--text-primary);
    min-width: 150px;
}

.content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.content-card {
    background: var(--dark-surface);
    border: 1px solid var(--dark-border);
    border-radius: 1rem;
    overflow: hidden;
    transition: var(--transition);
    position: relative;
}

.content-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
    border-color: var(--primary-color);
}

.content-poster {
    height: 200px;
    background: linear-gradient(135deg, var(--dark-bg), var(--dark-surface));
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    font-size: 3rem;
    position: relative;
    overflow: hidden;
}

.content-poster::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(59, 130, 246, 0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s;
}

.content-card:hover .content-poster::before {
    transform: translateX(100%);
}

.content-info {
    padding: 1.5rem;
}

.content-title {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.content-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.meta-tag {
    background: var(--dark-bg);
    color: var(--text-secondary);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.meta-tag.quality {
    background: var(--primary-color);
    color: white;
}

.meta-tag.symlink {
    background: var(--success-color);
    color: white;
}

.meta-tag.direct {
    background: var(--warning-color);
    color: white;
}

.content-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-small {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    border-radius: 0.375rem;
}

.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    margin-top: 2rem;
}

.pagination-btn {
    padding: 0.5rem 1rem;
    background: var(--dark-surface);
    border: 1px solid var(--dark-border);
    color: var(--text-primary);
    text-decoration: none;
    border-radius: 0.375rem;
    transition: var(--transition);
}

.pagination-btn:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.pagination-btn.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.stats-bar {
    background: var(--dark-surface);
    border: 1px solid var(--dark-border);
    border-radius: 0.5rem;
    padding: 1rem 1.5rem;
    margin-bottom: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.stats-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.stats-value {
    color: var(--text-primary);
    font-weight: 600;
}

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: var(--text-secondary);
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.table-view {
    background: var(--dark-surface);
    border: 1px solid var(--dark-border);
    border-radius: 1rem;
    overflow: hidden;
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--dark-border);
}

.table th {
    background: var(--dark-bg);
    color: var(--text-secondary);
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.table td {
    color: var(--text-primary);
}

.table tr:hover {
    background: rgba(59, 130, 246, 0.05);
}

.view-toggle {
    display: flex;
    background: var(--dark-bg);
    border: 1px solid var(--dark-border);
    border-radius: 0.5rem;
    overflow: hidden;
}

.view-btn {
    padding: 0.5rem 1rem;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
}

.view-btn.active {
    background: var(--primary-color);
    color: white;
}
</style>

<?php if (isset($error)): ?>
<div class="error-message">
    <i class="fas fa-exclamation-triangle"></i>
    <strong>Error:</strong> <?= htmlspecialchars($error) ?>
</div>
<?php endif; ?>

<!-- Browser Header -->
<div class="browser-header">
    <div class="browser-title"><?= $title ?></div>
    <div class="browser-controls">
        <div class="search-box">
            <i class="fas fa-search search-icon"></i>
            <input type="text" class="search-input" placeholder="Search content..." 
                   value="<?= htmlspecialchars($search) ?>" id="searchInput">
        </div>
        
        <select class="filter-select" id="filterSelect">
            <option value="all" <?= $filter === 'all' ? 'selected' : '' ?>>All Content</option>
            <option value="symlink" <?= $filter === 'symlink' ? 'selected' : '' ?>>Symlink (Protected)</option>
            <option value="direct" <?= $filter === 'direct' ? 'selected' : '' ?>>Direct Source</option>
            <option value="4k" <?= $filter === '4k' ? 'selected' : '' ?>>4K Content</option>
            <option value="60fps" <?= $filter === '60fps' ? 'selected' : '' ?>>60fps Content</option>
            <option value="missing_tmdb" <?= $filter === 'missing_tmdb' ? 'selected' : '' ?>>Missing TMDB</option>
        </select>
        
        <div class="view-toggle">
            <button class="view-btn active" id="gridViewBtn">
                <i class="fas fa-th"></i>
            </button>
            <button class="view-btn" id="listViewBtn">
                <i class="fas fa-list"></i>
            </button>
        </div>
        
        <button class="btn btn-primary" onclick="refreshContent()">
            <i class="fas fa-sync-alt"></i>
            Refresh
        </button>
    </div>
</div>

<!-- Stats Bar -->
<div class="stats-bar">
    <div class="stats-item">
        <i class="fas fa-film"></i>
        <span>Showing:</span>
        <span class="stats-value"><?= count($content) ?></span>
        <span>items</span>
    </div>
    <div class="stats-item">
        <i class="fas fa-filter"></i>
        <span>Filter:</span>
        <span class="stats-value"><?= ucfirst(str_replace('_', ' ', $filter)) ?></span>
    </div>
    <?php if ($search): ?>
    <div class="stats-item">
        <i class="fas fa-search"></i>
        <span>Search:</span>
        <span class="stats-value"><?= htmlspecialchars($search) ?></span>
    </div>
    <?php endif; ?>
</div>

<!-- Content Grid -->
<div id="contentContainer">
    <?php if (empty($content)): ?>
    <div class="empty-state">
        <div class="empty-icon">
            <i class="fas fa-inbox"></i>
        </div>
        <h3>No content found</h3>
        <p>Try adjusting your search or filter criteria</p>
    </div>
    <?php else: ?>
    <div class="content-grid" id="contentGrid">
        <?php foreach ($content as $item): ?>
        <div class="content-card">
            <div class="content-poster">
                <i class="fas fa-film"></i>
            </div>
            <div class="content-info">
                <div class="content-title"><?= htmlspecialchars($item['stream_display_name']) ?></div>
                <div class="content-meta">
                    <?php if ($item['year']): ?>
                    <span class="meta-tag"><?= $item['year'] ?></span>
                    <?php endif; ?>
                    
                    <?php if ($item['rating'] > 0): ?>
                    <span class="meta-tag">★ <?= number_format($item['rating'], 1) ?></span>
                    <?php endif; ?>
                    
                    <?php if ($item['movie_symlink']): ?>
                    <span class="meta-tag symlink">Symlink</span>
                    <?php elseif ($item['direct_source']): ?>
                    <span class="meta-tag direct">Direct</span>
                    <?php endif; ?>
                    
                    <?php if (stripos($item['stream_display_name'], '4K') !== false): ?>
                    <span class="meta-tag quality">4K</span>
                    <?php endif; ?>
                    
                    <?php if (stripos($item['stream_display_name'], '60fps') !== false): ?>
                    <span class="meta-tag quality">60fps</span>
                    <?php endif; ?>
                </div>
                <div class="content-actions">
                    <button class="btn btn-secondary btn-small" onclick="viewDetails(<?= $item['id'] ?>)">
                        <i class="fas fa-eye"></i>
                        View
                    </button>
                    <button class="btn btn-primary btn-small" onclick="editContent(<?= $item['id'] ?>)">
                        <i class="fas fa-edit"></i>
                        Edit
                    </button>
                </div>
            </div>
        </div>
        <?php endforeach; ?>
    </div>
    <?php endif; ?>
</div>

<!-- Pagination -->
<?php if (!empty($content)): ?>
<div class="pagination">
    <?php if ($page > 1): ?>
    <a href="?page=content-browser&p=<?= $page - 1 ?>&limit=<?= $limit ?>&search=<?= urlencode($search) ?>&filter=<?= $filter ?>" 
       class="pagination-btn">
        <i class="fas fa-chevron-left"></i>
        Previous
    </a>
    <?php endif; ?>
    
    <span class="pagination-btn active">Page <?= $page ?></span>
    
    <?php if (count($content) === $limit): ?>
    <a href="?page=content-browser&p=<?= $page + 1 ?>&limit=<?= $limit ?>&search=<?= urlencode($search) ?>&filter=<?= $filter ?>" 
       class="pagination-btn">
        Next
        <i class="fas fa-chevron-right"></i>
    </a>
    <?php endif; ?>
</div>
<?php endif; ?>

<script>
// Search functionality
document.getElementById('searchInput').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        performSearch();
    }
});

// Filter functionality
document.getElementById('filterSelect').addEventListener('change', function() {
    applyFilter();
});

// View toggle
document.getElementById('gridViewBtn').addEventListener('click', function() {
    setView('grid');
});

document.getElementById('listViewBtn').addEventListener('click', function() {
    setView('list');
});

function performSearch() {
    const search = document.getElementById('searchInput').value;
    const filter = document.getElementById('filterSelect').value;
    window.location.href = `?page=content-browser&search=${encodeURIComponent(search)}&filter=${filter}`;
}

function applyFilter() {
    const filter = document.getElementById('filterSelect').value;
    const search = document.getElementById('searchInput').value;
    window.location.href = `?page=content-browser&filter=${filter}&search=${encodeURIComponent(search)}`;
}

function setView(view) {
    const gridBtn = document.getElementById('gridViewBtn');
    const listBtn = document.getElementById('listViewBtn');
    const contentGrid = document.getElementById('contentGrid');
    
    if (view === 'grid') {
        gridBtn.classList.add('active');
        listBtn.classList.remove('active');
        contentGrid.className = 'content-grid';
    } else {
        listBtn.classList.add('active');
        gridBtn.classList.remove('active');
        contentGrid.className = 'table-view';
        // TODO: Implement table view
    }
}

function refreshContent() {
    showNotification('Refreshing content...', 'info');
    location.reload();
}

function viewDetails(id) {
    showNotification(`Viewing details for content ID: ${id}`, 'info');
    // TODO: Implement content details modal
}

function editContent(id) {
    showNotification(`Editing content ID: ${id}`, 'info');
    // TODO: Implement content editing modal
}

// Auto-search with debounce
let searchTimeout;
document.getElementById('searchInput').addEventListener('input', function() {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
        if (this.value.length > 2 || this.value.length === 0) {
            performSearch();
        }
    }, 500);
});
</script>
