"""
Script para detectar errores en config/database.py
"""

try:
    print("Intentando importar config.database...")
    from config.database import CONNECTION_CONFIG
    print("✓ CONNECTION_CONFIG importado")
    
    from config.database import QUERIES
    print(f"✓ QUERIES importado con {len(QUERIES)} consultas")
    
    print("✓ config.database importado correctamente")
    
except Exception as e:
    print(f"✗ Error al importar config.database: {str(e)}")
    import traceback
    traceback.print_exc()

try:
    print("\nIntentando importar core.database_manager...")
    from core.database_manager import DatabaseManager
    print("✓ DatabaseManager importado correctamente")
    
except Exception as e:
    print(f"✗ Error al importar DatabaseManager: {str(e)}")
    import traceback
    traceback.print_exc()
