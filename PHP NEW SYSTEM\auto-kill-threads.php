<?php
/**
 * AUTO KILL LONG THREADS - Background Script
 * ==========================================
 * 
 * This script runs automatically to prevent database lockups
 * Can be called via AJAX or run as a background process
 */

// Include path management
require_once __DIR__ . '/config/paths.php';
require_once __DIR__ . '/kill-long-threads.php';

// Set JSON response headers
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

// CORS headers for AJAX requests
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

try {
    $killer = new ThreadKiller();
    
    // Get current status
    $processes = $killer->getRunningProcesses();
    $status = $killer->getDatabaseStatus();
    
    // Kill long threads (more aggressive for auto-kill)
    $killed = $killer->killLongThreads(12);
    
    // Optimize settings if needed
    $optimized = false;
    if (count($processes) > 20 || count($killed) > 0) {
        $killer->optimizeSettings();
        $optimized = true;
    }
    
    // Prepare response
    $response = [
        'success' => true,
        'timestamp' => date('Y-m-d H:i:s'),
        'total_processes' => count($processes),
        'killed_threads' => count($killed),
        'killed_details' => $killed,
        'optimized' => $optimized,
        'status' => [
            'threads_connected' => $status['Threads_connected'] ?? 0,
            'threads_running' => $status['Threads_running'] ?? 0,
            'threads_cached' => $status['Threads_cached'] ?? 0
        ]
    ];
    
    // Log if threads were killed
    if (count($killed) > 0) {
        $logFile = __DIR__ . '/logs/thread-killer.log';
        $logDir = dirname($logFile);
        
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        $logEntry = date('Y-m-d H:i:s') . " - Killed " . count($killed) . " threads\n";
        foreach ($killed as $thread) {
            $logEntry .= "  - Thread {$thread['id']} ({$thread['time']}s): " . substr($thread['info'] ?? 'Unknown', 0, 100) . "\n";
        }
        $logEntry .= "\n";
        
        file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }
    
    echo json_encode($response, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_PRETTY_PRINT);
}
?>
