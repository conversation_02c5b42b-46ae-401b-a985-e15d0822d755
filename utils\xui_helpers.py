"""
Utilidades para trabajar con datos de XUI One
============================================

Funciones helper para parsear y trabajar con los datos específicos
de la estructura XUI One, especialmente movie_properties JSON.
"""

import json
import re
from typing import Dict, List, Optional, Any
from datetime import datetime

def parse_movie_properties(movie_properties: str) -> Dict[str, Any]:
    """
    Parsear el JSON de movie_properties de XUI One
    
    Args:
        movie_properties: String JSON con propiedades de la película
        
    Returns:
        Dict con propiedades parseadas
    """
    try:
        if not movie_properties:
            return {}
        
        # Limpiar el JSON si tiene caracteres extraños
        cleaned = movie_properties.strip()
        if not cleaned:
            return {}
            
        properties = json.loads(cleaned)
        
        # Normalizar campos comunes
        normalized = {
            'tmdb_id': properties.get('tmdb_id'),
            'title': properties.get('name', properties.get('title', '')),
            'original_title': properties.get('o_name', properties.get('original_title', '')),
            'description': properties.get('plot', properties.get('description', '')),
            'poster_url': properties.get('movie_image', properties.get('cover_big', '')),
            'backdrop_url': get_backdrop_url(properties.get('backdrop_path', [])),
            'release_date': properties.get('release_date', ''),
            'runtime': properties.get('episode_run_time', properties.get('duration_secs', 0)),
            'rating': float(properties.get('rating', 0)),
            'director': properties.get('director', ''),
            'cast': properties.get('cast', properties.get('actors', '')),
            'genre': properties.get('genre', ''),
            'country': properties.get('country', ''),
            'year': properties.get('year', extract_year_from_date(properties.get('release_date', ''))),
            'youtube_trailer': properties.get('youtube_trailer'),
            'duration': properties.get('duration', format_duration(properties.get('duration_secs', 0))),
            'age_rating': properties.get('mpaa_rating', ''),
            'kinopoisk_url': properties.get('kinopoisk_url', ''),
            'tmdb_url': f"https://www.themoviedb.org/movie/{properties.get('tmdb_id', '')}" if properties.get('tmdb_id') else ''
        }
        
        return normalized
        
    except (json.JSONDecodeError, TypeError, ValueError) as e:
        print(f"Error parsing movie_properties: {e}")
        return {}

def get_backdrop_url(backdrop_path: Any) -> str:
    """
    Extraer URL del backdrop desde el array backdrop_path
    
    Args:
        backdrop_path: Array o string con URLs de backdrop
        
    Returns:
        URL del backdrop o string vacío
    """
    if not backdrop_path:
        return ""
        
    if isinstance(backdrop_path, list) and len(backdrop_path) > 0:
        return backdrop_path[0]
    elif isinstance(backdrop_path, str):
        return backdrop_path
    
    return ""

def extract_year_from_date(date_str: str) -> int:
    """
    Extraer año de una fecha en formato YYYY-MM-DD
    
    Args:
        date_str: Fecha en formato string
        
    Returns:
        Año como entero o 0 si no se puede extraer
    """
    if not date_str:
        return 0
        
    try:
        # Buscar patrón YYYY al inicio
        match = re.match(r'^(\d{4})', date_str)
        if match:
            return int(match.group(1))
    except (ValueError, AttributeError):
        pass
    
    return 0

def format_duration(seconds: int) -> str:
    """
    Formatear duración en segundos a HH:MM:SS
    
    Args:
        seconds: Duración en segundos
        
    Returns:
        Duración formateada como string
    """
    if not seconds or seconds <= 0:
        return "00:00:00"
    
    hours = seconds // 3600
    minutes = (seconds % 3600) // 60
    seconds = seconds % 60
    
    return f"{hours:02d}:{minutes:02d}:{seconds:02d}"

def parse_stream_source(stream_source: str) -> List[str]:
    """
    Parsear stream_source que viene como JSON array
    
    Args:
        stream_source: String JSON con URLs de streams
        
    Returns:
        Lista de URLs
    """
    try:
        if not stream_source:
            return []
            
        # Limpiar string
        cleaned = stream_source.strip()
        if not cleaned:
            return []
            
        # Parsear JSON
        sources = json.loads(cleaned)
        
        if isinstance(sources, list):
            return sources
        elif isinstance(sources, str):
            return [sources]
        else:
            return []
            
    except (json.JSONDecodeError, TypeError):
        return []

def parse_category_ids(category_id: str) -> List[int]:
    """
    Parsear category_id que viene como JSON array
    
    Args:
        category_id: String JSON con IDs de categorías
        
    Returns:
        Lista de IDs de categorías
    """
    try:
        if not category_id:
            return []
            
        # Limpiar string
        cleaned = category_id.strip()
        if not cleaned:
            return []
            
        # Parsear JSON
        categories = json.loads(cleaned)
        
        if isinstance(categories, list):
            return [int(cat) for cat in categories if str(cat).isdigit()]
        elif isinstance(categories, (int, str)) and str(categories).isdigit():
            return [int(categories)]
        else:
            return []
            
    except (json.JSONDecodeError, TypeError, ValueError):
        return []

def create_movie_properties_json(movie_data: Dict) -> str:
    """
    Crear JSON de movie_properties desde datos de película
    
    Args:
        movie_data: Diccionario con datos de película
        
    Returns:
        String JSON para movie_properties
    """
    properties = {
        'tmdb_id': movie_data.get('tmdb_id'),
        'name': movie_data.get('title', ''),
        'o_name': movie_data.get('original_title', ''),
        'plot': movie_data.get('overview', ''),
        'description': movie_data.get('overview', ''),
        'movie_image': movie_data.get('poster_path', ''),
        'cover_big': movie_data.get('poster_path', ''),
        'backdrop_path': [movie_data.get('backdrop_path', '')] if movie_data.get('backdrop_path') else [],
        'release_date': movie_data.get('release_date', ''),
        'rating': movie_data.get('vote_average', 0),
        'director': movie_data.get('director', ''),
        'cast': movie_data.get('cast', ''),
        'actors': movie_data.get('cast', ''),
        'genre': movie_data.get('genres', ''),
        'country': movie_data.get('production_countries', ''),
        'year': movie_data.get('year', extract_year_from_date(movie_data.get('release_date', ''))),
        'episode_run_time': movie_data.get('runtime', 0),
        'duration_secs': movie_data.get('runtime', 0) * 60 if movie_data.get('runtime') else 0,
        'youtube_trailer': movie_data.get('youtube_trailer'),
        'mpaa_rating': movie_data.get('certification', ''),
        'kinopoisk_url': f"https://www.themoviedb.org/movie/{movie_data.get('tmdb_id', '')}" if movie_data.get('tmdb_id') else ''
    }
    
    # Remover campos None
    properties = {k: v for k, v in properties.items() if v is not None}
    
    try:
        return json.dumps(properties, ensure_ascii=False)
    except (TypeError, ValueError):
        return "{}"

def get_stream_type_name(type_id: int) -> str:
    """
    Obtener nombre del tipo de stream basado en el ID
    
    Args:
        type_id: ID del tipo de stream
        
    Returns:
        Nombre del tipo de stream
    """
    type_mapping = {
        1: "Live TV",
        2: "Movies", 
        3: "Series",
        4: "Radio"
    }
    
    return type_mapping.get(type_id, "Unknown")

def is_movie_stream(stream_type: int) -> bool:
    """
    Verificar si un stream es de tipo película
    
    Args:
        stream_type: ID del tipo de stream
        
    Returns:
        True si es película, False caso contrario
    """
    return stream_type == 2

def is_series_stream(stream_type: int) -> bool:
    """
    Verificar si un stream es de tipo serie
    
    Args:
        stream_type: ID del tipo de stream
        
    Returns:
        True si es serie, False caso contrario
    """
    return stream_type == 3

def is_live_stream(stream_type: int) -> bool:
    """
    Verificar si un stream es de tipo live TV
    
    Args:
        stream_type: ID del tipo de stream
        
    Returns:
        True si es live TV, False caso contrario
    """
    return stream_type == 1

def clean_stream_name(stream_name: str) -> str:
    """
    Limpiar nombre de stream removiendo caracteres especiales
    
    Args:
        stream_name: Nombre original del stream
        
    Returns:
        Nombre limpio
    """
    if not stream_name:
        return ""
    
    # Remover caracteres especiales comunes
    cleaned = re.sub(r'[^\w\s\-\.\(\)]+', '', stream_name)
    
    # Remover espacios múltiples
    cleaned = re.sub(r'\s+', ' ', cleaned)
    
    return cleaned.strip()

def extract_quality_from_name(stream_name: str) -> str:
    """
    Extraer calidad del nombre del stream
    
    Args:
        stream_name: Nombre del stream
        
    Returns:
        Calidad extraída o "Unknown"
    """
    if not stream_name:
        return "Unknown"
    
    # Buscar patrones de calidad comunes
    quality_patterns = [
        r'4K|UHD|2160p',
        r'1080p|FHD',
        r'720p|HD',
        r'480p|SD',
        r'360p',
        r'240p'
    ]
    
    for pattern in quality_patterns:
        match = re.search(pattern, stream_name, re.IGNORECASE)
        if match:
            return match.group(0).upper()
    
    return "Unknown"

def format_timestamp(timestamp: int) -> str:
    """
    Formatear timestamp Unix a fecha legible
    
    Args:
        timestamp: Timestamp Unix
        
    Returns:
        Fecha formateada
    """
    if not timestamp:
        return ""
    
    try:
        dt = datetime.fromtimestamp(timestamp)
        return dt.strftime("%Y-%m-%d %H:%M:%S")
    except (ValueError, OSError):
        return ""

def parse_similar_content(similar_data: str) -> List[int]:
    """
    Parsear datos de contenido similar
    
    Args:
        similar_data: String JSON con contenido similar
        
    Returns:
        Lista de IDs de contenido similar
    """
    try:
        if not similar_data:
            return []
            
        similar = json.loads(similar_data)
        
        if isinstance(similar, dict):
            # Extraer valores que parecen IDs
            ids = []
            for value in similar.values():
                if isinstance(value, (int, str)) and str(value).isdigit():
                    ids.append(int(value))
            return ids
        elif isinstance(similar, list):
            return [int(item) for item in similar if str(item).isdigit()]
        
        return []
        
    except (json.JSONDecodeError, TypeError, ValueError):
        return []
