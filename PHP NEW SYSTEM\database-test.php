<?php
/**
 * Database Test - IPTV XUI One Content Manager
 * ===========================================
 * 
 * Test database connection and queries
 */

// Include path management
require_once __DIR__ . '/config/paths.php';

// Initialize session management
require_once getConfigPath('session.php');

// Include configuration
require_once getConfigPath('database.php');

// Test basic PDO connection first
function testBasicConnection() {
    try {
        $config = DB_CONFIG;
        $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}";
        
        $pdo = new PDO($dsn, $config['username'], $config['password'], $config['options']);
        
        // Test a simple query
        $stmt = $pdo->query("SELECT 1 as test");
        $result = $stmt->fetch();
        
        return [
            'success' => true,
            'message' => 'Basic PDO connection successful',
            'test_result' => $result['test']
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Basic PDO connection failed',
            'error' => $e->getMessage()
        ];
    }
}

// Test table existence
function testTableExistence() {
    try {
        $config = DB_CONFIG;
        $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}";
        
        $pdo = new PDO($dsn, $config['username'], $config['password'], $config['options']);
        
        $tables = ['streams', 'streams_categories', 'streams_series', 'streams_episodes'];
        $results = [];
        
        foreach ($tables as $table) {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            $exists = $stmt->fetch() !== false;
            $results[$table] = $exists;
        }
        
        return [
            'success' => true,
            'tables' => $results
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

// Test DatabaseManager
function testDatabaseManager() {
    try {
        require_once getCorePath('DatabaseManager.php');
        
        $db = new DatabaseManager();
        
        // Test connection
        $connectionTest = $db->testConnection();
        
        // Test a simple query
        $stats = $db->getContentStats();
        
        return [
            'success' => true,
            'connection' => $connectionTest,
            'stats' => $stats
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ];
    }
}

// Run tests
$basicTest = testBasicConnection();
$tableTest = testTableExistence();
$managerTest = testDatabaseManager();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Test - IPTV Manager</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #0f172a;
            color: #f1f5f9;
            margin: 0;
            padding: 2rem;
            line-height: 1.6;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .card {
            background: #1e293b;
            border: 1px solid #334155;
            border-radius: 1rem;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .success {
            background: rgba(16, 185, 129, 0.1);
            border-color: #10b981;
        }
        .error {
            background: rgba(239, 68, 68, 0.1);
            border-color: #ef4444;
        }
        .info {
            background: rgba(59, 130, 246, 0.1);
            border-color: #3b82f6;
        }
        pre {
            background: #0f172a;
            padding: 1rem;
            border-radius: 0.5rem;
            overflow-x: auto;
            border: 1px solid #334155;
            white-space: pre-wrap;
        }
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #334155;
        }
        .test-item:last-child {
            border-bottom: none;
        }
        .status {
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: bold;
        }
        .status-ok { background: #10b981; color: white; }
        .status-error { background: #ef4444; color: white; }
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #3b82f6;
            color: white;
            text-decoration: none;
            border-radius: 0.5rem;
            margin: 0.5rem 0.5rem 0.5rem 0;
        }
        .btn:hover {
            background: #1e40af;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗄️ Database Test - IPTV Manager</h1>
        
        <!-- Database Configuration -->
        <div class="card info">
            <h3>Database Configuration</h3>
            <div class="test-item">
                <span><strong>Host:</strong> <?= DB_CONFIG['host'] ?></span>
            </div>
            <div class="test-item">
                <span><strong>Port:</strong> <?= DB_CONFIG['port'] ?></span>
            </div>
            <div class="test-item">
                <span><strong>Database:</strong> <?= DB_CONFIG['database'] ?></span>
            </div>
            <div class="test-item">
                <span><strong>Username:</strong> <?= DB_CONFIG['username'] ?></span>
            </div>
            <div class="test-item">
                <span><strong>Charset:</strong> <?= DB_CONFIG['charset'] ?></span>
            </div>
        </div>

        <!-- Basic Connection Test -->
        <div class="card <?= $basicTest['success'] ? 'success' : 'error' ?>">
            <h3>Basic PDO Connection Test</h3>
            <div class="test-item">
                <span><strong>Status:</strong> <?= $basicTest['success'] ? 'SUCCESS' : 'FAILED' ?></span>
                <span class="status <?= $basicTest['success'] ? 'status-ok' : 'status-error' ?>">
                    <?= $basicTest['success'] ? 'OK' : 'ERROR' ?>
                </span>
            </div>
            <div class="test-item">
                <span><strong>Message:</strong> <?= htmlspecialchars($basicTest['message']) ?></span>
            </div>
            <?php if (isset($basicTest['test_result'])): ?>
            <div class="test-item">
                <span><strong>Test Query Result:</strong> <?= $basicTest['test_result'] ?></span>
            </div>
            <?php endif; ?>
            <?php if (isset($basicTest['error'])): ?>
            <div style="margin-top: 1rem;">
                <strong>Error Details:</strong>
                <pre><?= htmlspecialchars($basicTest['error']) ?></pre>
            </div>
            <?php endif; ?>
        </div>

        <!-- Table Existence Test -->
        <div class="card <?= $tableTest['success'] ? 'success' : 'error' ?>">
            <h3>XUI One Tables Test</h3>
            <?php if ($tableTest['success']): ?>
                <?php foreach ($tableTest['tables'] as $table => $exists): ?>
                <div class="test-item">
                    <span><strong><?= $table ?>:</strong></span>
                    <span class="status <?= $exists ? 'status-ok' : 'status-error' ?>">
                        <?= $exists ? 'EXISTS' : 'NOT FOUND' ?>
                    </span>
                </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div style="color: #ef4444;">
                    <strong>Error:</strong> <?= htmlspecialchars($tableTest['error']) ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- DatabaseManager Test -->
        <div class="card <?= $managerTest['success'] ? 'success' : 'error' ?>">
            <h3>DatabaseManager Test</h3>
            <?php if ($managerTest['success']): ?>
                <div class="test-item">
                    <span><strong>Connection Test:</strong></span>
                    <span class="status <?= $managerTest['connection'] ? 'status-ok' : 'status-error' ?>">
                        <?= $managerTest['connection'] ? 'OK' : 'FAILED' ?>
                    </span>
                </div>
                <?php if (isset($managerTest['stats'])): ?>
                <div style="margin-top: 1rem;">
                    <strong>Content Statistics:</strong>
                    <pre><?= htmlspecialchars(print_r($managerTest['stats'], true)) ?></pre>
                </div>
                <?php endif; ?>
            <?php else: ?>
                <div style="color: #ef4444; margin-bottom: 1rem;">
                    <strong>Error:</strong> <?= htmlspecialchars($managerTest['error']) ?>
                </div>
                <?php if (isset($managerTest['trace'])): ?>
                <div>
                    <strong>Stack Trace:</strong>
                    <pre><?= htmlspecialchars($managerTest['trace']) ?></pre>
                </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>

        <!-- Actions -->
        <div class="card">
            <h3>Test Actions</h3>
            <a href="?refresh=1" class="btn">🔄 Refresh Tests</a>
            <a href="system-check.php" class="btn">🔍 System Check</a>
            <a href="index.php" class="btn">🏠 Go to Main App</a>
        </div>

        <!-- Troubleshooting -->
        <?php if (!$basicTest['success'] || !$tableTest['success'] || !$managerTest['success']): ?>
        <div class="card error">
            <h3>🔧 Troubleshooting</h3>
            <ul>
                <li><strong>Connection failed:</strong> Check host, port, username, and password</li>
                <li><strong>Access denied:</strong> Verify database user permissions</li>
                <li><strong>Database not found:</strong> Ensure database name is correct</li>
                <li><strong>Tables not found:</strong> This might not be a XUI One database</li>
                <li><strong>SQL syntax error:</strong> Check MariaDB/MySQL version compatibility</li>
            </ul>
            
            <h4>Common Solutions:</h4>
            <ul>
                <li>Verify database credentials in your .env file</li>
                <li>Check if the database server is running</li>
                <li>Ensure the database user has SELECT, INSERT, UPDATE, DELETE permissions</li>
                <li>Test connection from command line: <code>mysql -h <?= DB_CONFIG['host'] ?> -u <?= DB_CONFIG['username'] ?> -p <?= DB_CONFIG['database'] ?></code></li>
            </ul>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>
