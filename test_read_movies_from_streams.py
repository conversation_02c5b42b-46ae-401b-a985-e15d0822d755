"""
Script de prueba para leer películas desde la tabla 'streams' de XUI One
Las películas son los registros donde type = 2
"""

import mysql.connector
import os
from dotenv import load_dotenv

# Cargar variables del archivo .env
load_dotenv()

# Configuración de conexión desde .env
DB_CONFIG = {
    'host': os.getenv('DB_HOST'),
    'user': os.getenv('DB_USER'),
    'password': os.getenv('DB_PASSWORD'),
    'database': os.getenv('DB_NAME'),
    'port': int(os.getenv('DB_PORT', 3306)),
    'charset': 'utf8mb4',
}

def main():
    conn = mysql.connector.connect(**DB_CONFIG)
    cursor = conn.cursor()
    
    # Primero ver qué columnas tiene la tabla streams
    cursor.execute("SHOW COLUMNS FROM streams")
    columns = cursor.fetchall()
    print("Columnas de la tabla 'streams':")
    for col in columns:
        print(f"  - {col[0]} ({col[1]})")
    print()
    
    # Leer películas (type = 2) usando columnas que sabemos que existen
    query = """
        SELECT * FROM streams WHERE type = 2 ORDER BY id DESC LIMIT 10
    """
    cursor.execute(query)
    movies = cursor.fetchall()
    print(f"Total películas encontradas: {len(movies)}\n")
    
    # Mostrar los primeros registros para ver qué datos contienen
    if movies:
        print("Primeros registros de películas:")
        for i, movie in enumerate(movies[:3]):  # Solo mostrar 3 para no saturar
            print(f"Registro {i+1}: {movie}")
            print()
    
    cursor.close()
    conn.close()

if __name__ == "__main__":
    main()
