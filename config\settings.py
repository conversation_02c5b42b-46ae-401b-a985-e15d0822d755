"""
Configuración de la aplicación
==============================

Maneja la configuración global de la aplicación,
incluyendo configuración de base de datos, API y UI.
"""

import os
from pathlib import Path
from dotenv import load_dotenv
from typing import Optional

class Settings:
    """Configuración global de la aplicación"""
    
    def __init__(self):
        # Cargar variables de entorno
        load_dotenv()
        
        # Rutas
        self.base_dir = Path(__file__).parent.parent
        self.assets_dir = self.base_dir / "assets"
        self.logs_dir = self.base_dir / "logs"
        
        # Crear directorios si no existen
        self.logs_dir.mkdir(exist_ok=True)
        self.assets_dir.mkdir(exist_ok=True)
        
        # Configuración de la aplicación
        self.app_name = os.getenv("APP_NAME", "IPTV XUI Manager")
        self.app_version = os.getenv("APP_VERSION", "1.0.0")
        self.debug = os.getenv("DEBUG", "False").lower() == "true"
        
        # Configuración de base de datos
        self.db_host = os.getenv("DB_HOST", "localhost")
        self.db_port = int(os.getenv("DB_PORT", "3306"))
        self.db_name = os.getenv("DB_NAME", "iptv_xui")
        self.db_user = os.getenv("DB_USER", "root")
        self.db_password = os.getenv("DB_PASSWORD", "")
        
        # Configuración de TMDB
        self.tmdb_api_key = os.getenv("TMDB_API_KEY", "201066b4b17391d478e55247f43eed64")
        self.tmdb_base_url = "https://api.themoviedb.org/3"
        self.tmdb_image_base_url = "https://image.tmdb.org/t/p/w500"
        
        # Configuración de UI
        self.theme = os.getenv("THEME", "dark")
        self.window_width = int(os.getenv("WINDOW_WIDTH", "1200"))
        self.window_height = int(os.getenv("WINDOW_HEIGHT", "800"))
        self.animation_speed = float(os.getenv("ANIMATION_SPEED", "0.3"))
        
        # Configuración de cache
        self.cache_enabled = os.getenv("CACHE_ENABLED", "True").lower() == "true"
        self.cache_duration = int(os.getenv("CACHE_DURATION", "3600"))
        
        # Configuración de logging
        self.log_level = os.getenv("LOG_LEVEL", "INFO")
        self.log_file = self.logs_dir / os.getenv("LOG_FILE", "app.log")
        
        # Colores del tema
        self.colors = self._get_theme_colors()
        
        # Configuración de la aplicación
        self.max_concurrent_requests = 10
        self.timeout = 30
        self.retry_attempts = 3
        
    def _get_theme_colors(self) -> dict:
        """Obtener colores del tema"""
        if self.theme == "dark":
            return {
                "bg_primary": "#1a1a1a",
                "bg_secondary": "#2d2d2d",
                "bg_tertiary": "#3d3d3d",
                "text_primary": "#ffffff",
                "text_secondary": "#cccccc",
                "accent": "#007acc",
                "success": "#28a745",
                "warning": "#ffc107",
                "error": "#dc3545",
                "info": "#17a2b8"
            }
        else:
            return {
                "bg_primary": "#ffffff",
                "bg_secondary": "#f8f9fa",
                "bg_tertiary": "#e9ecef",
                "text_primary": "#212529",
                "text_secondary": "#6c757d",
                "accent": "#007bff",
                "success": "#28a745",
                "warning": "#ffc107",
                "error": "#dc3545",
                "info": "#17a2b8"
            }
    
    def get_db_config(self) -> dict:
        """Obtener configuración de base de datos"""
        return {
            "host": self.db_host,
            "port": self.db_port,
            "database": self.db_name,
            "user": self.db_user,
            "password": self.db_password,
            "autocommit": True,
            "charset": "utf8mb4"
        }
    
    def get_tmdb_headers(self) -> dict:
        """Obtener headers para TMDB API"""
        return {
            "Authorization": f"Bearer {self.tmdb_api_key}",
            "Content-Type": "application/json;charset=utf-8"
        }
    
    def validate_config(self) -> tuple[bool, list[str]]:
        """Validar configuración"""
        errors = []
        
        # Validar base de datos
        if not self.db_host:
            errors.append("DB_HOST no configurado")
        if not self.db_name:
            errors.append("DB_NAME no configurado")
        if not self.db_user:
            errors.append("DB_USER no configurado")
            
        # Validar TMDB
        if not self.tmdb_api_key or self.tmdb_api_key == "your_api_key_here":
            errors.append("TMDB_API_KEY no configurado correctamente")
            
        return len(errors) == 0, errors
