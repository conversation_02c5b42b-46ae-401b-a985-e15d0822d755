<?php
/**
 * API Bridge Manager
 * ==================
 * 
 * Gestor que conecta a tu base XUI a través de una API bridge
 * Soluciona problemas de conectividad de red
 */

class APIBridgeManager {
    private $api_url;
    private $cache = [];
    private $stats = [
        'api_calls' => 0,
        'cache_hits' => 0,
        'errors' => 0
    ];

    public function __construct($api_url = null) {
        // URL de tu API bridge (puedes cambiar esto)
        $this->api_url = $api_url ?: 'https://tu-hosting.com/xui-api.php';
    }

    private function makeAPICall($action, $params = []) {
        $this->stats['api_calls']++;
        
        try {
            $url = $this->api_url . '?action=' . urlencode($action);
            
            // Agregar parámetros GET
            foreach ($params as $key => $value) {
                $url .= '&' . urlencode($key) . '=' . urlencode($value);
            }
            
            // Configurar contexto para la petición
            $context = stream_context_create([
                'http' => [
                    'method' => 'GET',
                    'timeout' => 30,
                    'header' => [
                        'User-Agent: IPTV-XUI-Manager/1.0',
                        'Accept: application/json'
                    ]
                ]
            ]);
            
            $response = file_get_contents($url, false, $context);
            
            if ($response === false) {
                throw new Exception("Error en petición HTTP a API bridge");
            }
            
            $data = json_decode($response, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception("Respuesta JSON inválida de API bridge");
            }
            
            if (!$data['success']) {
                throw new Exception($data['error'] ?? 'Error desconocido en API bridge');
            }
            
            return $data['data'];
            
        } catch (Exception $e) {
            $this->stats['errors']++;
            throw new Exception("API Bridge Error: " . $e->getMessage());
        }
    }

    private function getCacheKey($action, $params = []) {
        return md5($action . serialize($params));
    }

    private function getFromCache($key, $ttl = 300) {
        if (!isset($this->cache[$key])) {
            return null;
        }
        
        $item = $this->cache[$key];
        if ($item['expires'] < time()) {
            unset($this->cache[$key]);
            return null;
        }
        
        $this->stats['cache_hits']++;
        return $item['data'];
    }

    private function setCache($key, $data, $ttl = 300) {
        $this->cache[$key] = [
            'data' => $data,
            'expires' => time() + $ttl
        ];
    }

    public function testConnection() {
        try {
            $result = $this->makeAPICall('test');
            return $result['status'] === 'connected';
        } catch (Exception $e) {
            return false;
        }
    }

    public function getConnectionInfo() {
        try {
            $result = $this->makeAPICall('test');
            return [
                'type' => 'api_bridge',
                'status' => 'connected',
                'api_url' => $this->api_url,
                'mysql_version' => $result['version'],
                'server_time' => $result['time'],
                'stats' => $this->stats
            ];
        } catch (Exception $e) {
            return [
                'type' => 'api_bridge',
                'status' => 'error',
                'api_url' => $this->api_url,
                'error' => $e->getMessage(),
                'stats' => $this->stats
            ];
        }
    }

    public function getContentStats() {
        $cacheKey = $this->getCacheKey('stats');
        $cached = $this->getFromCache($cacheKey);
        
        if ($cached !== null) {
            return $cached;
        }
        
        $result = $this->makeAPICall('stats');
        $this->setCache($cacheKey, $result, 300);
        
        return $result;
    }

    public function getQualityStats() {
        $cacheKey = $this->getCacheKey('quality');
        $cached = $this->getFromCache($cacheKey);
        
        if ($cached !== null) {
            return $cached;
        }
        
        $result = $this->makeAPICall('quality');
        $this->setCache($cacheKey, $result, 300);
        
        return $result;
    }

    public function getRecentAdditions($limit = 10) {
        $cacheKey = $this->getCacheKey('recent', ['limit' => $limit]);
        $cached = $this->getFromCache($cacheKey, 60); // Cache más corto para contenido reciente
        
        if ($cached !== null) {
            return $cached;
        }
        
        $result = $this->makeAPICall('recent', ['limit' => $limit]);
        $this->setCache($cacheKey, $result, 60);
        
        return $result;
    }

    public function getPopularContent($limit = 10) {
        $cacheKey = $this->getCacheKey('popular', ['limit' => $limit]);
        $cached = $this->getFromCache($cacheKey);
        
        if ($cached !== null) {
            return $cached;
        }
        
        $result = $this->makeAPICall('popular', ['limit' => $limit]);
        $this->setCache($cacheKey, $result, 300);
        
        return $result;
    }

    public function searchContent($searchTerm, $limit = 50, $offset = 0) {
        // No cachear búsquedas para resultados en tiempo real
        return $this->makeAPICall('search', [
            'term' => $searchTerm,
            'limit' => $limit
        ]);
    }

    public function get4KContent($limit = 50, $offset = 0) {
        $cacheKey = $this->getCacheKey('4k-content', ['limit' => $limit]);
        $cached = $this->getFromCache($cacheKey);
        
        if ($cached !== null) {
            return $cached;
        }
        
        $result = $this->makeAPICall('4k-content', ['limit' => $limit]);
        $this->setCache($cacheKey, $result, 600); // Cache más largo para contenido 4K
        
        return $result;
    }

    public function getCategories($type = null) {
        $cacheKey = $this->getCacheKey('categories', ['type' => $type]);
        $cached = $this->getFromCache($cacheKey, 3600); // Cache largo para categorías
        
        if ($cached !== null) {
            return $cached;
        }
        
        $result = $this->makeAPICall('categories');
        
        // Filtrar por tipo si se especifica
        if ($type) {
            $result = array_filter($result, function($cat) use ($type) {
                return $cat['category_type'] === $type;
            });
        }
        
        $this->setCache($cacheKey, $result, 3600);
        
        return $result;
    }

    public function getDuplicateAnalysis($limit = 50, $offset = 0) {
        $cacheKey = $this->getCacheKey('duplicates', ['limit' => $limit]);
        $cached = $this->getFromCache($cacheKey, 1800); // Cache medio para duplicados
        
        if ($cached !== null) {
            return $cached;
        }
        
        $result = $this->makeAPICall('duplicates', ['limit' => $limit]);
        $this->setCache($cacheKey, $result, 1800);
        
        return $result;
    }

    public function getTableInfo() {
        $cacheKey = $this->getCacheKey('tables');
        $cached = $this->getFromCache($cacheKey, 3600);
        
        if ($cached !== null) {
            return $cached;
        }
        
        $result = $this->makeAPICall('tables');
        $this->setCache($cacheKey, $result, 3600);
        
        return $result;
    }

    public function getStats() {
        return $this->stats;
    }

    public function setAPIUrl($url) {
        $this->api_url = $url;
    }

    public function getAPIUrl() {
        return $this->api_url;
    }

    public function clearCache() {
        $this->cache = [];
    }

    // Métodos de compatibilidad con otros gestores
    public function query($sql, $params = [], $cacheKey = null, $cacheTtl = null) {
        throw new Exception("Consultas SQL directas no soportadas en API Bridge. Use métodos específicos.");
    }

    public function execute($sql, $params = []) {
        throw new Exception("Ejecución SQL directa no soportada en API Bridge. Use métodos específicos.");
    }
}
?>
