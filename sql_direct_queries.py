"""
Consultas SQL Directas - Estilo HeidiSQL
========================================

Script que ejecuta consultas SQL directas para obtener resultados
exactos y precisos sin procesamiento complejo.
"""

import asyncio
import sys
import os

# Agregar el directorio raíz al path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.settings import Settings
from core.database_manager import DatabaseManager

async def execute_direct_queries():
    """Ejecutar consultas SQL directas tipo HeidiSQL"""
    print("🔍 Consultas SQL Directas - Estilo HeidiSQL")
    print("=" * 60)
    
    try:
        # Inicializar componentes
        settings = Settings()
        db_manager = DatabaseManager(settings)
        
        # Probar conexión
        print("1. Probando conexión...")
        if not await db_manager.test_connection():
            print("❌ Error de conexión")
            return
        print("✅ Conexión exitosa\n")
        
        # ========================================
        # CONSULTA 1: ESTADÍSTICAS BÁSICAS
        # ========================================
        print("📊 CONSULTA 1: ESTADÍSTICAS BÁSICAS")
        print("-" * 50)
        
        query1 = """
        SELECT 
            'Total Streams' as Descripcion,
            COUNT(*) as Cantidad
        FROM streams
        UNION ALL
        SELECT 
            'Películas (type=2)' as Descripcion,
            COUNT(*) as Cantidad
        FROM streams WHERE type = 2
        UNION ALL
        SELECT 
            'Series (type=3)' as Descripcion,
            COUNT(*) as Cantidad
        FROM streams WHERE type = 3
        UNION ALL
        SELECT 
            'TV en Vivo (type=1)' as Descripcion,
            COUNT(*) as Cantidad
        FROM streams WHERE type = 1
        UNION ALL
        SELECT 
            'Con TMDB ID' as Descripcion,
            COUNT(*) as Cantidad
        FROM streams WHERE tmdb_id IS NOT NULL AND tmdb_id != ''
        UNION ALL
        SELECT 
            'Sin TMDB ID' as Descripcion,
            COUNT(*) as Cantidad
        FROM streams WHERE tmdb_id IS NULL OR tmdb_id = ''
        """
        
        results1 = await db_manager.execute_query(query1)
        for row in results1:
            print(f"   {row['Descripcion']}: {row['Cantidad']:,}")
        
        # ========================================
        # CONSULTA 2: ANÁLISIS DE SYMLINK Y DIRECT_SOURCE
        # ========================================
        print("\n🔗 CONSULTA 2: ANÁLISIS DE SYMLINK Y DIRECT_SOURCE")
        print("-" * 50)
        
        query2 = """
        SELECT 
            CASE 
                WHEN movie_symlink = 1 THEN 'Symlink'
                WHEN direct_source = 1 THEN 'Direct Source'
                ELSE 'Stream URL'
            END as Tipo_Fuente,
            COUNT(*) as Cantidad,
            ROUND((COUNT(*) * 100.0 / (SELECT COUNT(*) FROM streams WHERE type = 2)), 2) as Porcentaje
        FROM streams 
        WHERE type = 2
        GROUP BY 
            CASE 
                WHEN movie_symlink = 1 THEN 'Symlink'
                WHEN direct_source = 1 THEN 'Direct Source'
                ELSE 'Stream URL'
            END
        ORDER BY Cantidad DESC
        """
        
        results2 = await db_manager.execute_query(query2)
        for row in results2:
            print(f"   {row['Tipo_Fuente']}: {row['Cantidad']:,} ({row['Porcentaje']}%)")
        
        # ========================================
        # CONSULTA 3: DISTRIBUCIÓN DE CALIDADES
        # ========================================
        print("\n🎯 CONSULTA 3: DISTRIBUCIÓN DE CALIDADES")
        print("-" * 50)
        
        query3 = """
        SELECT 
            CASE 
                WHEN stream_display_name LIKE '%4K%' OR stream_display_name LIKE '%2160p%' THEN '4K/2160p'
                WHEN stream_display_name LIKE '%1080p%' OR stream_display_name LIKE '%FHD%' THEN '1080p/FHD'
                WHEN stream_display_name LIKE '%720p%' OR stream_display_name LIKE '%HD%' THEN '720p/HD'
                WHEN stream_display_name LIKE '%480p%' THEN '480p'
                WHEN stream_display_name LIKE '%CAM%' OR stream_display_name LIKE '%TS%' OR stream_display_name LIKE '%SCREENER%' THEN 'CAM/TS/SCREENER'
                ELSE 'Sin especificar'
            END as Calidad,
            COUNT(*) as Cantidad,
            ROUND((COUNT(*) * 100.0 / (SELECT COUNT(*) FROM streams WHERE type = 2)), 2) as Porcentaje
        FROM streams 
        WHERE type = 2
        GROUP BY 
            CASE 
                WHEN stream_display_name LIKE '%4K%' OR stream_display_name LIKE '%2160p%' THEN '4K/2160p'
                WHEN stream_display_name LIKE '%1080p%' OR stream_display_name LIKE '%FHD%' THEN '1080p/FHD'
                WHEN stream_display_name LIKE '%720p%' OR stream_display_name LIKE '%HD%' THEN '720p/HD'
                WHEN stream_display_name LIKE '%480p%' THEN '480p'
                WHEN stream_display_name LIKE '%CAM%' OR stream_display_name LIKE '%TS%' OR stream_display_name LIKE '%SCREENER%' THEN 'CAM/TS/SCREENER'
                ELSE 'Sin especificar'
            END
        ORDER BY Cantidad DESC
        """
        
        results3 = await db_manager.execute_query(query3)
        for row in results3:
            print(f"   {row['Calidad']}: {row['Cantidad']:,} ({row['Porcentaje']}%)")
        
        # ========================================
        # CONSULTA 4: TOP DUPLICADOS
        # ========================================
        print("\n🔄 CONSULTA 4: TOP 10 DUPLICADOS")
        print("-" * 50)
        
        query4 = """
        SELECT 
            stream_display_name as Nombre,
            COUNT(*) as Veces_Repetido,
            GROUP_CONCAT(DISTINCT CASE 
                WHEN movie_symlink = 1 THEN 'Symlink'
                WHEN direct_source = 1 THEN 'Direct'
                ELSE 'Stream'
            END) as Tipos_Fuente,
            GROUP_CONCAT(DISTINCT tmdb_id) as TMDB_IDs
        FROM streams 
        WHERE type = 2
        GROUP BY stream_display_name 
        HAVING COUNT(*) > 1
        ORDER BY COUNT(*) DESC
        LIMIT 10
        """
        
        results4 = await db_manager.execute_query(query4)
        for i, row in enumerate(results4, 1):
            print(f"   {i:2d}. {row['Nombre']}")
            print(f"       Repetido: {row['Veces_Repetido']} veces")
            print(f"       Fuentes: {row['Tipos_Fuente']}")
            print(f"       TMDB IDs: {row['TMDB_IDs']}")
            print()
        
        # ========================================
        # CONSULTA 5: PELÍCULAS POR CALIDAD Y FUENTE
        # ========================================
        print("🎬 CONSULTA 5: PELÍCULAS POR CALIDAD Y FUENTE (MUESTRA)")
        print("-" * 50)
        
        query5 = """
        SELECT 
            stream_display_name as Nombre,
            CASE 
                WHEN stream_display_name LIKE '%4K%' OR stream_display_name LIKE '%2160p%' THEN '4K'
                WHEN stream_display_name LIKE '%1080p%' OR stream_display_name LIKE '%FHD%' THEN '1080p'
                WHEN stream_display_name LIKE '%720p%' OR stream_display_name LIKE '%HD%' THEN '720p'
                WHEN stream_display_name LIKE '%480p%' THEN '480p'
                WHEN stream_display_name LIKE '%CAM%' OR stream_display_name LIKE '%TS%' THEN 'CAM/TS'
                ELSE 'Unknown'
            END as Calidad,
            CASE 
                WHEN movie_symlink = 1 THEN 'Symlink'
                WHEN direct_source = 1 THEN 'Direct'
                ELSE 'Stream'
            END as Tipo_Fuente,
            movie_symlink as Symlink,
            direct_source as Direct,
            tmdb_id as TMDB,
            year as Año,
            rating as Rating
        FROM streams 
        WHERE type = 2
        ORDER BY added DESC
        LIMIT 15
        """
        
        results5 = await db_manager.execute_query(query5)
        for i, row in enumerate(results5, 1):
            symlink_icon = "✅" if row['Symlink'] == 1 else "❌"
            direct_icon = "✅" if row['Direct'] == 1 else "❌"
            print(f"   {i:2d}. {row['Nombre'][:50]}")
            print(f"       Calidad: {row['Calidad']} | Fuente: {row['Tipo_Fuente']}")
            print(f"       Symlink: {symlink_icon} | Direct: {direct_icon} | TMDB: {row['TMDB']} | Año: {row['Año']} | Rating: {row['Rating']}")
            print()
        
        # ========================================
        # CONSULTA 6: CATEGORÍAS MÁS USADAS
        # ========================================
        print("📁 CONSULTA 6: TOP 10 CATEGORÍAS MÁS USADAS")
        print("-" * 50)
        
        query6 = """
        SELECT 
            sc.category_name as Categoria,
            sc.category_type as Tipo,
            COUNT(s.id) as Cantidad_Streams
        FROM streams_categories sc
        LEFT JOIN streams s ON FIND_IN_SET(sc.id, s.category_id)
        WHERE sc.category_name IS NOT NULL
        GROUP BY sc.id, sc.category_name, sc.category_type
        HAVING COUNT(s.id) > 0
        ORDER BY COUNT(s.id) DESC
        LIMIT 10
        """
        
        results6 = await db_manager.execute_query(query6)
        for i, row in enumerate(results6, 1):
            print(f"   {i:2d}. {row['Categoria']} ({row['Tipo']}): {row['Cantidad_Streams']:,} streams")
        
        # ========================================
        # CONSULTA 7: RESUMEN DE CALIDAD POR FUENTE
        # ========================================
        print("\n📊 CONSULTA 7: RESUMEN CALIDAD POR TIPO DE FUENTE")
        print("-" * 50)
        
        query7 = """
        SELECT 
            CASE 
                WHEN movie_symlink = 1 THEN 'Symlink'
                WHEN direct_source = 1 THEN 'Direct Source'
                ELSE 'Stream URL'
            END as Tipo_Fuente,
            CASE 
                WHEN stream_display_name LIKE '%4K%' OR stream_display_name LIKE '%2160p%' THEN '4K'
                WHEN stream_display_name LIKE '%1080p%' OR stream_display_name LIKE '%FHD%' THEN '1080p'
                WHEN stream_display_name LIKE '%720p%' OR stream_display_name LIKE '%HD%' THEN '720p'
                ELSE 'Otras'
            END as Calidad,
            COUNT(*) as Cantidad
        FROM streams 
        WHERE type = 2
        GROUP BY 
            CASE 
                WHEN movie_symlink = 1 THEN 'Symlink'
                WHEN direct_source = 1 THEN 'Direct Source'
                ELSE 'Stream URL'
            END,
            CASE 
                WHEN stream_display_name LIKE '%4K%' OR stream_display_name LIKE '%2160p%' THEN '4K'
                WHEN stream_display_name LIKE '%1080p%' OR stream_display_name LIKE '%FHD%' THEN '1080p'
                WHEN stream_display_name LIKE '%720p%' OR stream_display_name LIKE '%HD%' THEN '720p'
                ELSE 'Otras'
            END
        ORDER BY Tipo_Fuente, Cantidad DESC
        """
        
        results7 = await db_manager.execute_query(query7)
        current_source = None
        for row in results7:
            if current_source != row['Tipo_Fuente']:
                current_source = row['Tipo_Fuente']
                print(f"\n   📁 {current_source}:")
            print(f"      - {row['Calidad']}: {row['Cantidad']:,}")
        
        print("\n✅ Consultas SQL directas completadas exitosamente")
        print("\n🎯 RESULTADOS OBTENIDOS:")
        print("   ✅ Estadísticas exactas de la base de datos")
        print("   ✅ Distribución precisa de symlink vs direct_source")
        print("   ✅ Análisis detallado de calidades")
        print("   ✅ Identificación de duplicados principales")
        print("   ✅ Muestra de películas con metadatos completos")
        print("   ✅ Análisis de categorías más utilizadas")
        print("   ✅ Correlación entre tipo de fuente y calidad")
        
    except Exception as e:
        print(f"\n❌ Error durante las consultas: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        if 'db_manager' in locals():
            await db_manager.close()
    
    return True

if __name__ == "__main__":
    success = asyncio.run(execute_direct_queries())
    if success:
        print("\n🚀 Consultas SQL directas ejecutadas exitosamente")
        print("   - Resultados exactos y precisos")
        print("   - Sin procesamiento complejo")
        print("   - Información completa para toma de decisiones")
    else:
        print("\n💥 Error en las consultas")
        print("Revisa la configuración y conexión a la base de datos")
        sys.exit(1)
