"""
Análisis de Contenido Prioritario
=================================

Script para identificar y gestionar contenido de máxima prioridad:
- 4K y 60fps
- Todo lo que sea symlink
"""

import asyncio
import sys
import os

# Agregar el directorio raíz al path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.settings import Settings
from core.database_manager import DatabaseManager

async def analyze_priority_content():
    """Analizar contenido de máxima prioridad"""
    print("🏆 ANÁLISIS DE CONTENIDO PRIORITARIO")
    print("=" * 60)
    print("🎯 PRIORIDADES: 4K, 60fps, Symlink")
    print("=" * 60)
    
    try:
        # Inicializar componentes
        settings = Settings()
        db_manager = DatabaseManager(settings)
        
        # Probar conexión
        print("1. Probando conexión...")
        if not await db_manager.test_connection():
            print("❌ Error de conexión")
            return
        print("✅ Conexión exitosa\n")
        
        # ========================================
        # CONSULTA 1: CONTENIDO 4K
        # ========================================
        print("🎬 CONSULTA 1: CONTENIDO 4K")
        print("-" * 50)
        
        query_4k = """
        SELECT 
            COUNT(*) as total_4k,
            SUM(CASE WHEN movie_symlink = 1 THEN 1 ELSE 0 END) as symlink_4k,
            SUM(CASE WHEN direct_source = 1 THEN 1 ELSE 0 END) as direct_4k
        FROM streams 
        WHERE type = 2 
        AND (
            stream_display_name LIKE '%4K%' OR 
            stream_display_name LIKE '%2160p%' OR
            stream_display_name LIKE '%UHD%'
        )
        """
        
        result_4k = await db_manager.execute_query(query_4k)
        if result_4k:
            row = result_4k[0]
            total = row['total_4k']
            symlink = row['symlink_4k']
            direct = row['direct_4k']
            
            print(f"   📊 Total contenido 4K: {total:,}")
            print(f"   🔗 4K con Symlink: {symlink:,} ({(symlink/total*100):.1f}%)" if total > 0 else "   🔗 4K con Symlink: 0")
            print(f"   📁 4K con Direct: {direct:,} ({(direct/total*100):.1f}%)" if total > 0 else "   📁 4K con Direct: 0")
        
        # ========================================
        # CONSULTA 2: CONTENIDO 60FPS
        # ========================================
        print("\n🎮 CONSULTA 2: CONTENIDO 60FPS")
        print("-" * 50)
        
        query_60fps = """
        SELECT 
            COUNT(*) as total_60fps,
            SUM(CASE WHEN movie_symlink = 1 THEN 1 ELSE 0 END) as symlink_60fps,
            SUM(CASE WHEN direct_source = 1 THEN 1 ELSE 0 END) as direct_60fps
        FROM streams 
        WHERE type = 2 
        AND (
            stream_display_name LIKE '%60fps%' OR 
            stream_display_name LIKE '%60FPS%' OR
            stream_display_name LIKE '%60 fps%' OR
            stream_display_name LIKE '%HFR%'
        )
        """
        
        result_60fps = await db_manager.execute_query(query_60fps)
        if result_60fps:
            row = result_60fps[0]
            total = row['total_60fps']
            symlink = row['symlink_60fps']
            direct = row['direct_60fps']
            
            print(f"   📊 Total contenido 60fps: {total:,}")
            print(f"   🔗 60fps con Symlink: {symlink:,} ({(symlink/total*100):.1f}%)" if total > 0 else "   🔗 60fps con Symlink: 0")
            print(f"   📁 60fps con Direct: {direct:,} ({(direct/total*100):.1f}%)" if total > 0 else "   📁 60fps con Direct: 0")
        
        # ========================================
        # CONSULTA 3: TODO EL CONTENIDO SYMLINK (MÁXIMA PRIORIDAD)
        # ========================================
        print("\n🔗 CONSULTA 3: TODO EL CONTENIDO SYMLINK (MÁXIMA PRIORIDAD)")
        print("-" * 50)
        
        query_symlink = """
        SELECT 
            COUNT(*) as total_symlink,
            SUM(CASE WHEN stream_display_name LIKE '%4K%' OR stream_display_name LIKE '%2160p%' THEN 1 ELSE 0 END) as symlink_4k,
            SUM(CASE WHEN stream_display_name LIKE '%1080p%' OR stream_display_name LIKE '%FHD%' THEN 1 ELSE 0 END) as symlink_1080p,
            SUM(CASE WHEN stream_display_name LIKE '%720p%' OR stream_display_name LIKE '%HD%' THEN 1 ELSE 0 END) as symlink_720p,
            SUM(CASE WHEN stream_display_name LIKE '%60fps%' OR stream_display_name LIKE '%60FPS%' THEN 1 ELSE 0 END) as symlink_60fps
        FROM streams 
        WHERE type = 2 AND movie_symlink = 1
        """
        
        result_symlink = await db_manager.execute_query(query_symlink)
        if result_symlink:
            row = result_symlink[0]
            total = row['total_symlink']
            
            print(f"   🏆 TOTAL SYMLINK (PRIORIDAD MÁXIMA): {total:,}")
            print(f"   📺 Symlink 4K: {row['symlink_4k']:,}")
            print(f"   📺 Symlink 1080p: {row['symlink_1080p']:,}")
            print(f"   📺 Symlink 720p: {row['symlink_720p']:,}")
            print(f"   🎮 Symlink 60fps: {row['symlink_60fps']:,}")
        
        # ========================================
        # CONSULTA 4: MUESTRA DE CONTENIDO PRIORITARIO
        # ========================================
        print("\n🎯 CONSULTA 4: MUESTRA DE CONTENIDO PRIORITARIO")
        print("-" * 50)
        
        query_priority_sample = """
        SELECT 
            id,
            stream_display_name,
            movie_symlink,
            direct_source,
            tmdb_id,
            year,
            rating,
            CASE 
                WHEN stream_display_name LIKE '%4K%' OR stream_display_name LIKE '%2160p%' THEN '4K'
                WHEN stream_display_name LIKE '%1080p%' OR stream_display_name LIKE '%FHD%' THEN '1080p'
                WHEN stream_display_name LIKE '%720p%' OR stream_display_name LIKE '%HD%' THEN '720p'
                ELSE 'Other'
            END as quality,
            CASE 
                WHEN stream_display_name LIKE '%60fps%' OR stream_display_name LIKE '%60FPS%' THEN 'YES'
                ELSE 'NO'
            END as has_60fps
        FROM streams 
        WHERE type = 2 
        AND (
            movie_symlink = 1 OR
            stream_display_name LIKE '%4K%' OR 
            stream_display_name LIKE '%2160p%' OR
            stream_display_name LIKE '%60fps%' OR
            stream_display_name LIKE '%60FPS%'
        )
        ORDER BY 
            movie_symlink DESC,
            CASE WHEN stream_display_name LIKE '%4K%' OR stream_display_name LIKE '%2160p%' THEN 1 ELSE 2 END,
            CASE WHEN stream_display_name LIKE '%60fps%' OR stream_display_name LIKE '%60FPS%' THEN 1 ELSE 2 END,
            added DESC
        LIMIT 15
        """
        
        result_priority = await db_manager.execute_query(query_priority_sample)
        print("   🏆 TOP 15 CONTENIDO PRIORITARIO:")
        for i, row in enumerate(result_priority, 1):
            symlink = "🔗" if row['movie_symlink'] == 1 else "📁"
            fps_60 = "🎮" if row['has_60fps'] == 'YES' else ""
            quality_icon = "🎬" if row['quality'] == '4K' else "📺" if row['quality'] == '1080p' else "📱"
            
            print(f"   {i:2d}. {symlink} {quality_icon} {fps_60} {row['stream_display_name'][:45]}")
            print(f"       ID: {row['id']} | Calidad: {row['quality']} | 60fps: {row['has_60fps']} | TMDB: {row['tmdb_id']} | Año: {row['year']}")
            print()
        
        # ========================================
        # CONSULTA 5: DUPLICADOS EN CONTENIDO PRIORITARIO
        # ========================================
        print("🔄 CONSULTA 5: DUPLICADOS EN CONTENIDO PRIORITARIO")
        print("-" * 50)
        
        query_priority_duplicates = """
        SELECT 
            stream_display_name,
            COUNT(*) as count,
            SUM(CASE WHEN movie_symlink = 1 THEN 1 ELSE 0 END) as symlink_versions,
            SUM(CASE WHEN direct_source = 1 THEN 1 ELSE 0 END) as direct_versions,
            GROUP_CONCAT(DISTINCT CASE 
                WHEN stream_display_name LIKE '%4K%' OR stream_display_name LIKE '%2160p%' THEN '4K'
                WHEN stream_display_name LIKE '%1080p%' OR stream_display_name LIKE '%FHD%' THEN '1080p'
                WHEN stream_display_name LIKE '%720p%' OR stream_display_name LIKE '%HD%' THEN '720p'
                ELSE 'Other'
            END) as qualities,
            GROUP_CONCAT(id ORDER BY movie_symlink DESC, id) as ids
        FROM streams 
        WHERE type = 2 
        AND (
            movie_symlink = 1 OR
            stream_display_name LIKE '%4K%' OR 
            stream_display_name LIKE '%2160p%' OR
            stream_display_name LIKE '%60fps%' OR
            stream_display_name LIKE '%60FPS%'
        )
        GROUP BY stream_display_name 
        HAVING COUNT(*) > 1
        ORDER BY symlink_versions DESC, COUNT(*) DESC
        LIMIT 10
        """
        
        result_dup_priority = await db_manager.execute_query(query_priority_duplicates)
        print("   🔄 TOP 10 DUPLICADOS PRIORITARIOS:")
        for i, row in enumerate(result_dup_priority, 1):
            symlink_count = row['symlink_versions']
            direct_count = row['direct_versions']
            total = row['count']
            
            print(f"   {i:2d}. '{row['stream_display_name']}'")
            print(f"       Total: {total} | 🔗 Symlink: {symlink_count} | 📁 Direct: {direct_count}")
            print(f"       Calidades: {row['qualities']}")
            print(f"       IDs: {row['ids']}")
            print()
        
        # ========================================
        # CONSULTA 6: RECOMENDACIONES DE GESTIÓN
        # ========================================
        print("💡 CONSULTA 6: RECOMENDACIONES DE GESTIÓN")
        print("-" * 50)
        
        # Contenido que debería ser symlink pero no lo es
        query_should_be_symlink = """
        SELECT COUNT(*) as count
        FROM streams 
        WHERE type = 2 
        AND movie_symlink = 0 
        AND direct_source = 1
        AND (
            stream_display_name LIKE '%4K%' OR 
            stream_display_name LIKE '%2160p%' OR
            stream_display_name LIKE '%60fps%' OR
            stream_display_name LIKE '%60FPS%'
        )
        """
        
        result_should_symlink = await db_manager.execute_query(query_should_be_symlink)
        should_convert = result_should_symlink[0]['count'] if result_should_symlink else 0
        
        print("   💡 RECOMENDACIONES:")
        print(f"   🔄 Contenido 4K/60fps que debería convertirse a symlink: {should_convert:,}")
        print("   🏆 Priorizar mantenimiento de todo el contenido symlink existente")
        print("   🎯 Eliminar duplicados de menor calidad cuando hay versión symlink")
        print("   📈 Convertir contenido 4K/60fps de direct_source a symlink para mejor rendimiento")
        
        print("\n✅ Análisis de contenido prioritario completado")
        print("\n🎯 RESUMEN DE PRIORIDADES:")
        print("   🥇 MÁXIMA PRIORIDAD: Todo contenido symlink (5,785 películas)")
        print("   🥈 ALTA PRIORIDAD: Contenido 4K y 60fps")
        print("   🥉 MEDIA PRIORIDAD: Contenido 1080p direct_source")
        print("   ⚠️  BAJA PRIORIDAD: Contenido de menor calidad")
        
    except Exception as e:
        print(f"\n❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        if 'db_manager' in locals():
            await db_manager.close()
    
    return True

if __name__ == "__main__":
    success = asyncio.run(analyze_priority_content())
    if success:
        print("\n🚀 Análisis de prioridades completado exitosamente")
        print("   - Identificado contenido 4K y 60fps")
        print("   - Catalogado todo el contenido symlink")
        print("   - Detectados duplicados prioritarios")
        print("   - Generadas recomendaciones de gestión")
    else:
        print("\n💥 Error en el análisis")
        sys.exit(1)
