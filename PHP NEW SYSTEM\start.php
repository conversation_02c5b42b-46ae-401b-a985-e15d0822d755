<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎬 IPTV XUI One Content Manager</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            color: #f1f5f9;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            width: 100%;
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(59, 130, 246, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 3rem;
            background: linear-gradient(135deg, #3b82f6, #06b6d4);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }

        .header p {
            color: #94a3b8;
            font-size: 1.2rem;
        }

        .cards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .card {
            background: rgba(51, 65, 85, 0.6);
            border-radius: 15px;
            padding: 30px;
            text-decoration: none;
            color: inherit;
            transition: all 0.3s ease;
            border: 1px solid rgba(59, 130, 246, 0.1);
            position: relative;
            overflow: hidden;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(6, 182, 212, 0.1) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .card:hover::before {
            opacity: 1;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px -12px rgba(59, 130, 246, 0.2);
            border-color: rgba(59, 130, 246, 0.3);
        }

        .card-icon {
            font-size: 3rem;
            color: #3b82f6;
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
        }

        .card-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }

        .card-description {
            color: #94a3b8;
            line-height: 1.6;
            position: relative;
            z-index: 1;
        }

        .status-bar {
            background: rgba(51, 65, 85, 0.6);
            border-radius: 10px;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-icon {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #10b981;
            animation: pulse 2s infinite;
        }

        .status-icon.warning {
            background: #f59e0b;
        }

        .status-icon.error {
            background: #ef4444;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .badge {
            background: rgba(59, 130, 246, 0.2);
            color: #3b82f6;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .badge.success {
            background: rgba(16, 185, 129, 0.2);
            color: #10b981;
        }

        .badge.warning {
            background: rgba(245, 158, 11, 0.2);
            color: #f59e0b;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .cards-grid {
                grid-template-columns: 1fr;
            }
            
            .status-bar {
                flex-direction: column;
                align-items: flex-start;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-tv"></i> IPTV XUI One</h1>
            <p>Content Manager - PHP Edition</p>
        </div>

        <div class="cards-grid">
            <a href="system-test.php" class="card">
                <div class="card-icon">
                    <i class="fas fa-stethoscope"></i>
                </div>
                <div class="card-title">System Diagnostic</div>
                <div class="card-description">
                    Check database connection, PHP extensions, and system health
                </div>
            </a>

            <a href="public/index.php" class="card">
                <div class="card-icon">
                    <i class="fas fa-tachometer-alt"></i>
                </div>
                <div class="card-title">Main Dashboard</div>
                <div class="card-description">
                    Access the full IPTV content management interface
                </div>
            </a>

            <a href="public/index.php?page=content-browser" class="card">
                <div class="card-icon">
                    <i class="fas fa-film"></i>
                </div>
                <div class="card-title">Content Browser</div>
                <div class="card-description">
                    Browse and manage your IPTV content library
                </div>
            </a>

            <a href="public/index.php?page=m3u-manager" class="card">
                <div class="card-icon">
                    <i class="fas fa-upload"></i>
                </div>
                <div class="card-title">M3U Manager</div>
                <div class="card-description">
                    Upload and process M3U playlist files
                </div>
            </a>

            <a href="public/index.php?page=analytics" class="card">
                <div class="card-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="card-title">Analytics</div>
                <div class="card-description">
                    View content statistics and performance metrics
                </div>
            </a>

            <a href="api/" class="card">
                <div class="card-icon">
                    <i class="fas fa-code"></i>
                </div>
                <div class="card-title">API Endpoints</div>
                <div class="card-description">
                    Access REST API for programmatic content management
                </div>
            </a>
        </div>

        <div class="status-bar">
            <div class="status-item">
                <div class="status-icon"></div>
                <span>PHP <?= PHP_VERSION ?></span>
                <span class="badge success">Ready</span>
            </div>

            <?php
            // Quick database check
            $dbStatus = false;
            $dbMessage = "Checking...";
            
            try {
                if (file_exists('config/env.php') && file_exists('config/database.php')) {
                    require_once 'config/env.php';
                    require_once 'config/database.php';
                    
                    if (extension_loaded('pdo') && extension_loaded('pdo_mysql')) {
                        $config = DB_CONFIG;
                        $pdo = new PDO(
                            "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']}",
                            $config['username'],
                            $config['password'],
                            [PDO::ATTR_TIMEOUT => 5]
                        );
                        $stmt = $pdo->query("SELECT COUNT(*) FROM streams LIMIT 1");
                        $count = $stmt->fetchColumn();
                        $dbStatus = true;
                        $dbMessage = number_format($count) . " streams";
                    } else {
                        $dbMessage = "PDO MySQL missing";
                    }
                } else {
                    $dbMessage = "Config missing";
                }
            } catch (Exception $e) {
                $dbMessage = "Connection failed";
            }
            ?>

            <div class="status-item">
                <div class="status-icon <?= $dbStatus ? '' : 'error' ?>"></div>
                <span>Database</span>
                <span class="badge <?= $dbStatus ? 'success' : 'warning' ?>"><?= $dbMessage ?></span>
            </div>

            <div class="status-item">
                <div class="status-icon <?= is_writable('logs') ? '' : 'warning' ?>"></div>
                <span>Permissions</span>
                <span class="badge <?= is_writable('logs') ? 'success' : 'warning' ?>">
                    <?= is_writable('logs') ? 'OK' : 'Check logs/' ?>
                </span>
            </div>

            <div class="status-item">
                <span>Theme</span>
                <span class="badge">Dark Mode</span>
            </div>
        </div>
    </div>

    <script>
        // Add some interactive effects
        document.querySelectorAll('.card').forEach(card => {
            card.addEventListener('mouseenter', () => {
                const icon = card.querySelector('.card-icon i');
                icon.style.transform = 'scale(1.1) rotate(5deg)';
                icon.style.transition = 'transform 0.3s ease';
            });
            
            card.addEventListener('mouseleave', () => {
                const icon = card.querySelector('.card-icon i');
                icon.style.transform = 'scale(1) rotate(0deg)';
            });
        });

        // Add loading animation to status indicators
        document.querySelectorAll('.status-icon').forEach(icon => {
            if (!icon.classList.contains('error') && !icon.classList.contains('warning')) {
                icon.style.animation = 'pulse 2s infinite';
            }
        });
    </script>
</body>
</html>
