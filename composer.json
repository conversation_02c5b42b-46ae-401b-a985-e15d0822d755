{"name": "iptv-manager/xui-one-content-manager", "description": "Modern web-based IPTV XUI One Content Manager with enhanced UI/UX", "type": "project", "version": "2.0.0", "keywords": ["iptv", "xui-one", "content-manager", "m3u", "tmdb", "php", "web-application"], "homepage": "https://github.com/iptv-manager/xui-one-content-manager", "license": "MIT", "authors": [{"name": "IPTV Management Team", "email": "<EMAIL>", "role": "Developer"}], "require": {"php": ">=8.0", "ext-pdo": "*", "ext-json": "*", "ext-mbstring": "*", "ext-curl": "*", "ext-fileinfo": "*"}, "require-dev": {"phpunit/phpunit": "^9.0", "phpstan/phpstan": "^1.0", "squizlabs/php_codesniffer": "^3.6"}, "autoload": {"psr-4": {"IPTVManager\\Core\\": "core/", "IPTVManager\\Config\\": "config/", "IPTVManager\\API\\": "api/"}}, "autoload-dev": {"psr-4": {"IPTVManager\\Tests\\": "tests/"}}, "scripts": {"test": "phpunit", "test-coverage": "phpunit --coverage-html coverage", "phpstan": "phpstan analyse", "cs-check": "phpcs", "cs-fix": "phpcbf", "quality": ["@cs-check", "@phpstan", "@test"]}, "config": {"optimize-autoloader": true, "sort-packages": true, "allow-plugins": {"composer/package-versions-deprecated": true}}, "minimum-stability": "stable", "prefer-stable": true}