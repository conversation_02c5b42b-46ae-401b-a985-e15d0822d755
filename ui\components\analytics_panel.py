"""
Panel de análisis
================

Panel para mostrar análisis y estadísticas del contenido con gráficos mejorados.
"""

import tkinter as tk
import customtkinter as ctk
import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta

# Importar matplotlib para gráficos
try:
    import matplotlib
    matplotlib.use('TkAgg')
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
    from matplotlib.figure import Figure
    import matplotlib.dates as mdates
    import pandas as pd
    import numpy as np
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False

class AnalyticsPanel:
    """Panel de análisis y estadísticas mejorado"""
    
    def __init__(self, parent, settings, db_manager):
        self.parent = parent
        self.settings = settings
        self.db_manager = db_manager
        self.logger = logging.getLogger("iptv_manager.analytics")
        
        # Configurar estilo de matplotlib
        if MATPLOTLIB_AVAILABLE:
            plt.style.use('dark_background')
        
        # Crear interfaz
        self._create_layout()
        self._create_summary_cards()
        self._create_analysis_tabs()
        
        # Cargar datos
        asyncio.create_task(self._load_analytics_data())
    
    def _create_layout(self):
        """Crear layout principal"""
        # Frame principal
        self.main_frame = ctk.CTkScrollableFrame(self.parent)
        self.main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Título
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="📊 Panel de Análisis",
            font=("Arial", 24, "bold")
        )
        title_label.pack(pady=(0, 20))
        
        # Frame de tarjetas de resumen
        self.summary_frame = ctk.CTkFrame(self.main_frame)
        self.summary_frame.pack(fill="x", pady=(0, 20))
        
        # Frame de análisis
        self.analysis_frame = ctk.CTkFrame(self.main_frame)
        self.analysis_frame.pack(fill="both", expand=True)
    
    def _create_summary_cards(self):
        """Crear tarjetas de resumen"""
        # Configurar grid
        self.summary_frame.grid_columnconfigure((0, 1, 2, 3, 4), weight=1)
        
        # Tarjetas de resumen
        self.summary_cards = {}
        
        cards_config = [
            ("total_content", "📊", "Total Contenido", "0", self.settings.colors["info"]),
            ("new_this_week", "🆕", "Nuevos (Semana)", "0", self.settings.colors["success"]),
            ("duplicates", "🔄", "Duplicados", "0", self.settings.colors["warning"]),
            ("missing_tmdb", "❓", "Sin TMDB", "0", self.settings.colors["error"]),
            ("quality_4k", "🎯", "Calidad 4K", "0", self.settings.colors["accent"])
        ]
        
        for i, (key, icon, title, value, color) in enumerate(cards_config):
            card = self._create_summary_card(self.summary_frame, icon, title, value, color)
            card.grid(row=0, column=i, padx=10, pady=15, sticky="ew")
            self.summary_cards[key] = card
    
    def _create_summary_card(self, parent, icon: str, title: str, value: str, color: str):
        """Crear tarjeta de resumen"""
        # Frame de la tarjeta
        card_frame = ctk.CTkFrame(parent, corner_radius=15)
        
        # Contenido
        content_frame = ctk.CTkFrame(card_frame, fg_color="transparent")
        content_frame.pack(fill="both", expand=True, padx=15, pady=15)
        
        # Icono
        icon_label = ctk.CTkLabel(
            content_frame,
            text=icon,
            font=("Arial", 24),
            text_color=color
        )
        icon_label.pack(pady=(0, 5))
        
        # Valor
        value_label = ctk.CTkLabel(
            content_frame,
            text=value,
            font=("Arial", 18, "bold"),
            text_color=color
        )
        value_label.pack()
        
        # Título
        title_label = ctk.CTkLabel(
            content_frame,
            text=title,
            font=("Arial", 10),
            text_color=self.settings.colors["text_secondary"]
        )
        title_label.pack()
        
        # Guardar referencia al label de valor para actualizar
        card_frame.value_label = value_label
        
        return card_frame
        
        # Título
        title_label = ctk.CTkLabel(
            content_frame,
            text=title,
            font=("Arial", 10),
            text_color=self.settings.colors["text_secondary"]
        )
        title_label.pack(pady=(2, 0))
        
        # Guardar referencia al valor
        card_frame.value_label = value_label
        
        return card_frame
    
    def _create_analysis_tabs(self):
        """Crear pestañas de análisis"""
        # Crear notebook para las pestañas
        self.notebook = ctk.CTkTabview(self.analysis_frame)
        self.notebook.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Pestañas
        self.notebook.add("Tendencias")
        self.notebook.add("Distribución")
        self.notebook.add("Rendimiento")
        self.notebook.add("Duplicados")
        self.notebook.add("Reportes")
        
        # Configurar cada pestaña
        self._setup_trends_tab()
        self._setup_distribution_tab()
        self._setup_performance_tab()
        self._setup_duplicates_tab()
        self._setup_reports_tab()
    
    def _setup_trends_tab(self):
        """Configurar pestaña de tendencias"""
        trends_frame = self.notebook.tab("Tendencias")
        
        # Frame para controles
        controls_frame = ctk.CTkFrame(trends_frame)
        controls_frame.pack(fill="x", padx=10, pady=10)
        
        # Selector de período
        period_label = ctk.CTkLabel(controls_frame, text="Período:")
        period_label.pack(side="left", padx=10)
        
        self.period_var = ctk.StringVar(value="7 días")
        period_combo = ctk.CTkComboBox(
            controls_frame,
            variable=self.period_var,
            values=["7 días", "30 días", "90 días", "1 año"],
            command=self._update_trends_chart
        )
        period_combo.pack(side="left", padx=10)
        
        # Botón actualizar
        refresh_btn = ctk.CTkButton(
            controls_frame,
            text="🔄 Actualizar",
            command=self._update_trends_chart
        )
        refresh_btn.pack(side="right", padx=10)
        
        # Frame para gráfico
        self.trends_chart_frame = ctk.CTkFrame(trends_frame)
        self.trends_chart_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        if MATPLOTLIB_AVAILABLE:
            self._create_trends_chart()
        else:
            self._create_placeholder_chart(self.trends_chart_frame, "Tendencias de Contenido")
    
    def _setup_distribution_tab(self):
        """Configurar pestaña de distribución"""
        dist_frame = self.notebook.tab("Distribución")
        
        # Frame para controles
        controls_frame = ctk.CTkFrame(dist_frame)
        controls_frame.pack(fill="x", padx=10, pady=10)
        
        # Selector de tipo de distribución
        dist_label = ctk.CTkLabel(controls_frame, text="Tipo:")
        dist_label.pack(side="left", padx=10)
        
        self.distribution_var = ctk.StringVar(value="Géneros")
        dist_combo = ctk.CTkComboBox(
            controls_frame,
            variable=self.distribution_var,
            values=["Géneros", "Calidad", "Idiomas", "Grupos"],
            command=self._update_distribution_chart
        )
        dist_combo.pack(side="left", padx=10)
        
        # Frame para gráfico
        self.distribution_chart_frame = ctk.CTkFrame(dist_frame)
        self.distribution_chart_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        if MATPLOTLIB_AVAILABLE:
            self._create_distribution_chart()
        else:
            self._create_placeholder_chart(self.distribution_chart_frame, "Distribución de Contenido")
    
    def _setup_performance_tab(self):
        """Configurar pestaña de rendimiento"""
        perf_frame = self.notebook.tab("Rendimiento")
        
        # Frame para métricas
        metrics_frame = ctk.CTkFrame(perf_frame)
        metrics_frame.pack(fill="x", padx=10, pady=10)
        
        # Métricas de rendimiento
        self.performance_metrics = {}
        
        metrics_config = [
            ("import_speed", "⚡", "Velocidad Importación", "0 items/s"),
            ("db_response", "🗄️", "Respuesta BD", "0 ms"),
            ("tmdb_requests", "🌐", "Requests TMDB", "0/min"),
            ("cache_hit_rate", "📊", "Cache Hit Rate", "0%")
        ]
        
        for i, (key, icon, title, value) in enumerate(metrics_config):
            metric_frame = ctk.CTkFrame(metrics_frame)
            metric_frame.grid(row=0, column=i, padx=10, pady=10, sticky="ew")
            
            icon_label = ctk.CTkLabel(metric_frame, text=icon, font=("Arial", 20))
            icon_label.pack(pady=5)
            
            value_label = ctk.CTkLabel(metric_frame, text=value, font=("Arial", 14, "bold"))
            value_label.pack()
            
            title_label = ctk.CTkLabel(metric_frame, text=title, font=("Arial", 10))
            title_label.pack()
            
            self.performance_metrics[key] = value_label
        
        # Configurar grid
        metrics_frame.grid_columnconfigure((0, 1, 2, 3), weight=1)
        
        # Frame para gráfico de rendimiento
        self.performance_chart_frame = ctk.CTkFrame(perf_frame)
        self.performance_chart_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        if MATPLOTLIB_AVAILABLE:
            self._create_performance_chart()
        else:
            self._create_placeholder_chart(self.performance_chart_frame, "Métricas de Rendimiento")
    
    def _setup_duplicates_tab(self):
        """Configurar pestaña de duplicados"""
        dup_frame = self.notebook.tab("Duplicados")
        
        # Frame para controles
        controls_frame = ctk.CTkFrame(dup_frame)
        controls_frame.pack(fill="x", padx=10, pady=10)
        
        # Selector de método de detección
        method_label = ctk.CTkLabel(controls_frame, text="Método:")
        method_label.pack(side="left", padx=10)
        
        self.duplicate_method_var = ctk.StringVar(value="URL")
        method_combo = ctk.CTkComboBox(
            controls_frame,
            variable=self.duplicate_method_var,
            values=["URL", "Título", "Hash"]
        )
        method_combo.pack(side="left", padx=10)
        
        # Botón buscar duplicados
        search_btn = ctk.CTkButton(
            controls_frame,
            text="🔍 Buscar Duplicados",
            command=self._search_duplicates
        )
        search_btn.pack(side="left", padx=10)
        
        # Botón limpiar duplicados
        clean_btn = ctk.CTkButton(
            controls_frame,
            text="🧹 Limpiar Duplicados",
            command=self._clean_duplicates
        )
        clean_btn.pack(side="right", padx=10)
        
        # Frame para lista de duplicados
        self.duplicates_frame = ctk.CTkScrollableFrame(dup_frame)
        self.duplicates_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Lista de duplicados
        self._create_duplicates_list()
    
    def _setup_reports_tab(self):
        """Configurar pestaña de reportes"""
        reports_frame = self.notebook.tab("Reportes")
        
        # Frame para controles
        controls_frame = ctk.CTkFrame(reports_frame)
        controls_frame.pack(fill="x", padx=10, pady=10)
        
        # Botones de reportes
        report_buttons = [
            ("📊 Reporte Completo", self._generate_full_report),
            ("📈 Reporte de Tendencias", self._generate_trends_report),
            ("🔍 Reporte de Duplicados", self._generate_duplicates_report),
            ("📋 Reporte de Contenido", self._generate_content_report)
        ]
        
        for i, (text, command) in enumerate(report_buttons):
            btn = ctk.CTkButton(
                controls_frame,
                text=text,
                command=command,
                width=200
            )
            btn.grid(row=i//2, column=i%2, padx=10, pady=5, sticky="ew")
        
        # Configurar grid
        controls_frame.grid_columnconfigure((0, 1), weight=1)
        
        # Frame para vista previa del reporte
        self.report_preview_frame = ctk.CTkScrollableFrame(reports_frame)
        self.report_preview_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Texto de vista previa
        self.report_text = ctk.CTkTextbox(self.report_preview_frame)
        self.report_text.pack(fill="both", expand=True, padx=10, pady=10)
    
    def _create_trends_chart(self):
        """Crear gráfico de tendencias"""
        if not MATPLOTLIB_AVAILABLE:
            return
        
        # Crear figura
        fig = Figure(figsize=(12, 6), dpi=100)
        fig.patch.set_facecolor('#2b2b2b')
        
        ax = fig.add_subplot(111)
        ax.set_facecolor('#2b2b2b')
        
        # Datos de ejemplo (reemplazar con datos reales)
        dates = pd.date_range(start='2024-01-01', end='2024-01-30', freq='D')
        content_added = np.random.randint(10, 100, len(dates))
        
        # Crear gráfico
        ax.plot(dates, content_added, color='#1f77b4', linewidth=2, marker='o', markersize=4)
        ax.set_title('Tendencia de Contenido Agregado', color='white', fontsize=14, fontweight='bold')
        ax.set_xlabel('Fecha', color='white')
        ax.set_ylabel('Contenido Agregado', color='white')
        
        # Configurar ejes
        ax.tick_params(colors='white')
        ax.grid(True, alpha=0.3)
        
        # Formatear fechas
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%d/%m'))
        ax.xaxis.set_major_locator(mdates.DayLocator(interval=5))
        
        # Crear canvas
        canvas = FigureCanvasTkAgg(fig, self.trends_chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill="both", expand=True)
    
    def _create_distribution_chart(self):
        """Crear gráfico de distribución"""
        if not MATPLOTLIB_AVAILABLE:
            return
        
        # Crear figura
        fig = Figure(figsize=(10, 8), dpi=100)
        fig.patch.set_facecolor('#2b2b2b')
        
        ax = fig.add_subplot(111)
        ax.set_facecolor('#2b2b2b')
        
        # Datos de ejemplo
        labels = ['Acción', 'Comedia', 'Drama', 'Terror', 'Ciencia Ficción', 'Documentales']
        sizes = [25, 20, 15, 12, 18, 10]
        colors = ['#ff9999', '#66b3ff', '#99ff99', '#ffcc99', '#ff99cc', '#c2c2f0']
        
        # Crear gráfico de pastel
        wedges, texts, autotexts = ax.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%',
                                         startangle=90, textprops={'color': 'white'})
        
        ax.set_title('Distribución por Géneros', color='white', fontsize=14, fontweight='bold')
        
        # Crear canvas
        canvas = FigureCanvasTkAgg(fig, self.distribution_chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill="both", expand=True)
    
    def _create_performance_chart(self):
        """Crear gráfico de rendimiento"""
        if not MATPLOTLIB_AVAILABLE:
            return
        
        # Crear figura
        fig = Figure(figsize=(12, 6), dpi=100)
        fig.patch.set_facecolor('#2b2b2b')
        
        ax = fig.add_subplot(111)
        ax.set_facecolor('#2b2b2b')
        
        # Datos de ejemplo
        times = np.arange(0, 100, 5)
        response_times = np.random.normal(50, 10, len(times))
        
        # Crear gráfico
        ax.plot(times, response_times, color='#ff7f0e', linewidth=2)
        ax.fill_between(times, response_times, alpha=0.3, color='#ff7f0e')
        
        ax.set_title('Tiempo de Respuesta de la Base de Datos', color='white', fontsize=14, fontweight='bold')
        ax.set_xlabel('Tiempo (min)', color='white')
        ax.set_ylabel('Tiempo de Respuesta (ms)', color='white')
        
        # Configurar ejes
        ax.tick_params(colors='white')
        ax.grid(True, alpha=0.3)
        
        # Crear canvas
        canvas = FigureCanvasTkAgg(fig, self.performance_chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill="both", expand=True)
    
    def _create_placeholder_chart(self, parent, title: str):
        """Crear placeholder cuando matplotlib no está disponible"""
        placeholder_frame = ctk.CTkFrame(parent)
        placeholder_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Icono
        icon_label = ctk.CTkLabel(
            placeholder_frame,
            text="📊",
            font=("Arial", 48)
        )
        icon_label.pack(pady=20)
        
        # Título
        title_label = ctk.CTkLabel(
            placeholder_frame,
            text=title,
            font=("Arial", 18, "bold")
        )
        title_label.pack(pady=10)
        
        # Mensaje
        message_label = ctk.CTkLabel(
            placeholder_frame,
            text="Instala matplotlib para ver gráficos avanzados",
            font=("Arial", 12),
            text_color=self.settings.colors["text_secondary"]
        )
        message_label.pack(pady=10)
        
        # Botón instalar
        install_btn = ctk.CTkButton(
            placeholder_frame,
            text="📦 Instalar matplotlib",
            command=self._install_matplotlib
        )
        install_btn.pack(pady=10)
    
    def _create_duplicates_list(self):
        """Crear lista de duplicados"""
        # Título
        title_label = ctk.CTkLabel(
            self.duplicates_frame,
            text="Lista de Duplicados",
            font=("Arial", 16, "bold")
        )
        title_label.pack(pady=10)
        
        # Aquí se mostrarán los duplicados encontrados
        self.duplicates_list_frame = ctk.CTkFrame(self.duplicates_frame)
        self.duplicates_list_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Mensaje inicial
        no_duplicates_label = ctk.CTkLabel(
            self.duplicates_list_frame,
            text="🔍 Haz clic en 'Buscar Duplicados' para encontrar contenido duplicado",
            font=("Arial", 12),
            text_color=self.settings.colors["text_secondary"]
        )
        no_duplicates_label.pack(pady=50)
    
    async def _load_analytics_data(self):
        """Cargar datos de análisis"""
        try:
            if not self.db_manager:
                return
            
            # Obtener estadísticas generales
            stats = await self.db_manager.get_content_stats()
            
            # Actualizar tarjetas de resumen
            self._update_summary_cards(stats)
            
            # Cargar datos de gráficos
            await self._load_chart_data()
            
        except Exception as e:
            self.logger.error(f"Error al cargar datos de análisis: {str(e)}")
    
    def _update_summary_cards(self, stats: Dict[str, Any]):
        """Actualizar tarjetas de resumen"""
        try:
            # Actualizar valores
            updates = {
                "total_content": str(stats.get("total_movies", 0) + stats.get("total_tv_shows", 0)),
                "new_this_week": str(stats.get("new_this_week", 0)),
                "duplicates": str(stats.get("duplicates_count", 0)),
                "missing_tmdb": str(stats.get("unmatched_content", 0)),
                "quality_4k": str(stats.get("quality_4k", 0))
            }
            
            for key, value in updates.items():
                if key in self.summary_cards:
                    self.summary_cards[key].value_label.configure(text=value)
                    
        except Exception as e:
            self.logger.error(f"Error al actualizar tarjetas: {str(e)}")
    
    async def _load_chart_data(self):
        """Cargar datos para gráficos"""
        # Implementar carga de datos reales
        pass
    
    def _update_trends_chart(self):
        """Actualizar gráfico de tendencias"""
        # Implementar actualización de gráfico
        pass
    
    def _update_distribution_chart(self):
        """Actualizar gráfico de distribución"""
        # Implementar actualización de gráfico
        pass
    
    async def _search_duplicates(self):
        """Buscar duplicados"""
        try:
            # Limpiar lista actual
            for widget in self.duplicates_list_frame.winfo_children():
                widget.destroy()
            
            # Mostrar indicador de carga
            loading_label = ctk.CTkLabel(
                self.duplicates_list_frame,
                text="🔍 Buscando duplicados...",
                font=("Arial", 12)
            )
            loading_label.pack(pady=20)
            
            # Buscar duplicados en la base de datos
            if self.db_manager:
                duplicates = await self.db_manager.get_duplicates()
                
                # Limpiar indicador de carga
                loading_label.destroy()
                
                if duplicates:
                    # Mostrar duplicados encontrados
                    self._display_duplicates(duplicates)
                else:
                    # No hay duplicados
                    no_dup_label = ctk.CTkLabel(
                        self.duplicates_list_frame,
                        text="✅ No se encontraron duplicados",
                        font=("Arial", 12),
                        text_color=self.settings.colors["success"]
                    )
                    no_dup_label.pack(pady=20)
                    
        except Exception as e:
            self.logger.error(f"Error al buscar duplicados: {str(e)}")
    
    def _display_duplicates(self, duplicates: List[Dict]):
        """Mostrar lista de duplicados"""
        for i, duplicate in enumerate(duplicates[:20]):  # Mostrar máximo 20
            dup_frame = ctk.CTkFrame(self.duplicates_list_frame)
            dup_frame.pack(fill="x", padx=10, pady=5)
            
            # Información del duplicado
            info_label = ctk.CTkLabel(
                dup_frame,
                text=f"📺 {duplicate.get('title', 'Sin título')} ({duplicate.get('count', 0)} duplicados)",
                font=("Arial", 12)
            )
            info_label.pack(side="left", padx=10, pady=5)
            
            # Botón eliminar
            delete_btn = ctk.CTkButton(
                dup_frame,
                text="🗑️ Eliminar",
                width=100,
                command=lambda d=duplicate: self._delete_duplicate(d)
            )
            delete_btn.pack(side="right", padx=10, pady=5)
    
    def _delete_duplicate(self, duplicate: Dict):
        """Eliminar duplicado específico"""
        # Implementar eliminación de duplicado
        pass
    
    async def _clean_duplicates(self):
        """Limpiar todos los duplicados"""
        # Implementar limpieza de duplicados
        pass
    
    def _generate_full_report(self):
        """Generar reporte completo"""
        report = """
📊 REPORTE COMPLETO DE CONTENIDO IPTV
=====================================

📅 Fecha: {date}
⏰ Hora: {time}

📈 ESTADÍSTICAS GENERALES
------------------------
• Total de contenido: {total_content}
• Películas: {movies}
• Series de TV: {tv_shows}
• Contenido nuevo esta semana: {new_week}
• Duplicados encontrados: {duplicates}

🎯 DISTRIBUCIÓN POR CALIDAD
--------------------------
• 4K: {quality_4k}
• HD: {quality_hd}
• SD: {quality_sd}

🌍 DISTRIBUCIÓN POR IDIOMA
-------------------------
• Español: {spanish}
• Inglés: {english}
• Otros: {other_languages}

⚡ MÉTRICAS DE RENDIMIENTO
------------------------
• Velocidad de importación: {import_speed}
• Tiempo de respuesta BD: {db_response}
• Tasa de éxito TMDB: {tmdb_success}

🔍 RECOMENDACIONES
-----------------
• Revisar duplicados encontrados
• Actualizar contenido sin metadata TMDB
• Optimizar categorización automática
        """.format(
            date=datetime.now().strftime("%Y-%m-%d"),
            time=datetime.now().strftime("%H:%M:%S"),
            total_content="Calculando...",
            movies="Calculando...",
            tv_shows="Calculando...",
            new_week="Calculando...",
            duplicates="Calculando...",
            quality_4k="Calculando...",
            quality_hd="Calculando...",
            quality_sd="Calculando...",
            spanish="Calculando...",
            english="Calculando...",
            other_languages="Calculando...",
            import_speed="Calculando...",
            db_response="Calculando...",
            tmdb_success="Calculando..."
        )
        
        self.report_text.delete("1.0", tk.END)
        self.report_text.insert("1.0", report)
    
    def _generate_trends_report(self):
        """Generar reporte de tendencias"""
        # Implementar reporte de tendencias
        pass
    
    def _generate_duplicates_report(self):
        """Generar reporte de duplicados"""
        # Implementar reporte de duplicados
        pass
    
    def _generate_content_report(self):
        """Generar reporte de contenido"""
        # Implementar reporte de contenido
        pass
    
    def _install_matplotlib(self):
        """Instalar matplotlib"""
        import subprocess
        import sys
        
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "matplotlib", "pandas", "numpy", "seaborn"])
            
            # Mostrar mensaje de éxito
            success_dialog = ctk.CTkToplevel()
            success_dialog.title("Instalación Exitosa")
            success_dialog.geometry("300x150")
            
            message = ctk.CTkLabel(
                success_dialog,
                text="✅ matplotlib instalado correctamente.\nReinicia la aplicación para ver los gráficos.",
                font=("Arial", 12)
            )
            message.pack(pady=20)
            
            ok_btn = ctk.CTkButton(
                success_dialog,
                text="OK",
                command=success_dialog.destroy
            )
            ok_btn.pack(pady=10)
            
        except Exception as e:
            self.logger.error(f"Error al instalar matplotlib: {str(e)}")
    
    def refresh_data(self):
        """Actualizar datos del panel"""
        asyncio.create_task(self._load_analytics_data())
