"""
Script para probar la obtención de series desde streams_series
"""

import asyncio
import sys
import os

# Agregar el directorio raíz al path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.database_manager import DatabaseManager
from config.settings import Settings

async def test_series():
    """Probar la obtención de series"""
    
    # Inicializar configuración y database manager
    settings = Settings()
    db_manager = DatabaseManager(settings)
    
    try:
        # Probar conexión
        print("1. Probando conexión a la base de datos...")
        if await db_manager.test_connection():
            print("✓ Conexión exitosa")
        else:
            print("✗ Error en conexión")
            return
        
        # Obtener series
        print("\n2. Obteniendo series desde streams_series...")
        series = await db_manager.get_tv_shows(limit=10)
        print(f"Total series obtenidas: {len(series)}")
        
        if series:
            print("\nPrimeras 3 series:")
            for i, serie in enumerate(series[:3]):
                print(f"\n  Serie {i+1}:")
                print(f"    ID: {serie.get('id')}")
                print(f"    Título: {serie.get('title')}")
                print(f"    Año: {serie.get('year')}")
                print(f"    Rating: {serie.get('rating')}")
                print(f"    TMDB ID: {serie.get('tmdb_id')}")
                print(f"    Categoría: {serie.get('category_name', 'N/A')}")
                print(f"    Géneros: {serie.get('genre', 'N/A')}")
                
                # Obtener episodios de la primera serie
                if i == 0:
                    print(f"\n    Obteniendo episodios de '{serie.get('title')}'...")
                    episodes = await db_manager.get_series_episodes(serie.get('id'))
                    print(f"    Total episodios: {len(episodes)}")
                    
                    if episodes:
                        print("    Primeros 3 episodios:")
                        for j, ep in enumerate(episodes[:3]):
                            print(f"      Episodio {j+1}: T{ep.get('season_num')}E{ep.get('episode_num')} - {ep.get('stream_display_name')}")
        
        # Buscar series
        print("\n3. Buscando series (término: 'dragon')...")
        search_results = await db_manager.search_tv_shows("dragon", limit=5)
        print(f"Resultados de búsqueda: {len(search_results)}")
        for serie in search_results:
            print(f"  - {serie.get('title')} ({serie.get('year', 'N/A')}) - Rating: {serie.get('rating', 'N/A')}")
        
        print("\n✓ Todas las pruebas de series completadas exitosamente")
        
    except Exception as e:
        print(f"✗ Error durante las pruebas: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Cerrar conexiones
        await db_manager.close()

if __name__ == "__main__":
    asyncio.run(test_series())
