# IPTV XUI One Content Manager - Apache Configuration
# ==================================================

# Enable URL rewriting
RewriteEngine On

# Security Headers
<IfModule mod_headers.c>
    # Prevent clickjacking
    Header always set X-Frame-Options "SAMEORIGIN"
    
    # Prevent MIME type sniffing
    Header always set X-Content-Type-Options "nosniff"
    
    # XSS Protection
    Header always set X-XSS-Protection "1; mode=block"
    
    # Referrer Policy
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Content Security Policy (basic)
    Header always set Content-Security-Policy "default-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdnjs.cloudflare.com https://cdn.jsdelivr.net https://api.themoviedb.org; img-src 'self' data: https://image.tmdb.org;"
</IfModule>

# Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE text/javascript
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/json
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/atom+xml
    AddOutputFilterByType DEFLATE image/svg+xml
</IfModule>

# Cache Control for Assets
<IfModule mod_expires.c>
    ExpiresActive On
    
    # CSS and JavaScript
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType text/javascript "access plus 1 year"
    
    # Images
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType image/x-icon "access plus 1 year"
    
    # Fonts
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
    
    # HTML and PHP (no cache)
    ExpiresByType text/html "access plus 0 seconds"
    ExpiresByType application/json "access plus 0 seconds"
</IfModule>

# URL Rewriting Rules
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d

# API Routes
RewriteRule ^api/(.*)$ api/$1 [L,QSA]

# Asset Routes (CSS, JS, Images)
RewriteRule ^assets/(.*)$ public/assets/$1 [L,QSA]

# Main Application Routes
RewriteRule ^(.*)$ index.php [L,QSA]

# Protect sensitive files
<FilesMatch "\.(env|log|ini|conf|sql|bak|backup)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Protect configuration directories
<DirectoryMatch "^.*(config|logs|uploads/temp).*$">
    Order Allow,Deny
    Deny from all
</DirectoryMatch>

# Allow uploads directory but not PHP execution
<Directory "uploads">
    <FilesMatch "\.php$">
        Order Allow,Deny
        Deny from all
    </FilesMatch>
</Directory>

# PHP Configuration (if allowed by hosting)
<IfModule mod_php.c>
    # Increase upload limits
    php_value upload_max_filesize 50M
    php_value post_max_size 50M
    php_value max_execution_time 300
    php_value memory_limit 512M
    
    # Security settings
    php_flag display_errors Off
    php_flag log_errors On
    php_value error_log logs/php_errors.log
    
    # Session settings
    php_value session.cookie_httponly 1
    php_value session.cookie_secure 0
    php_value session.use_strict_mode 1
</IfModule>

# Error Pages
ErrorDocument 404 /index.php
ErrorDocument 403 /index.php
ErrorDocument 500 /index.php
