"""
Test específico para Symlink y Direct Source
============================================

Script para probar específicamente la funcionalidad de análisis
de symlink y direct_source en las películas.
"""

import asyncio
import sys
import os

# Agregar el directorio raíz al path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.settings import Settings
from core.database_manager import DatabaseManager

async def test_symlink_direct():
    """Probar funcionalidades de symlink y direct_source"""
    print("🔗 Test de Symlink y Direct Source")
    print("=" * 50)
    
    try:
        # Inicializar componentes
        settings = Settings()
        db_manager = DatabaseManager(settings)
        
        # Probar conexión
        print("1. Probando conexión...")
        if not await db_manager.test_connection():
            print("❌ Error de conexión")
            return
        print("✅ Conexión exitosa")
        
        # ========================================
        # ANÁLISIS DE TIPOS DE FUENTE
        # ========================================
        print("\n🔗 ANÁLISIS DE TIPOS DE FUENTE")
        print("-" * 40)
        
        print("2. Analizando distribución de tipos de fuente...")
        source_dist = await db_manager.analyze_source_type_distribution()
        print("📊 Distribución de tipos de fuente:")
        total_movies = sum(source_dist.values())
        
        for source_type, count in source_dist.items():
            percentage = (count / total_movies) * 100 if total_movies > 0 else 0
            print(f"   - {source_type}: {count} ({percentage:.1f}%)")
        
        print(f"\n   📈 Total de películas analizadas: {total_movies}")
        
        # ========================================
        # MUESTRA DETALLADA DE PELÍCULAS
        # ========================================
        print("\n🎬 MUESTRA DETALLADA DE PELÍCULAS")
        print("-" * 40)
        
        print("3. Obteniendo muestra de películas con información detallada...")
        movies_detailed = await db_manager.get_movies_by_source_and_quality(limit=15)
        print(f"🎭 Películas con información detallada: {len(movies_detailed)}")
        
        # Agrupar por tipo de fuente para mostrar ejemplos
        symlink_movies = [m for m in movies_detailed if m.get('movie_symlink') == 1]
        direct_movies = [m for m in movies_detailed if m.get('direct_source') == 1]
        stream_movies = [m for m in movies_detailed if m.get('movie_symlink') != 1 and m.get('direct_source') != 1]
        
        print(f"\n   🔗 Películas con Symlink: {len(symlink_movies)}")
        for i, movie in enumerate(symlink_movies[:3]):
            print(f"      {i+1}. {movie.get('stream_display_name', 'Sin nombre')}")
            print(f"         - Calidad: {movie.get('quality', 'Unknown')}")
            print(f"         - Rating: {movie.get('rating', 'N/A')}")
            print(f"         - Año: {movie.get('year', 'N/A')}")
            print(f"         - TMDB ID: {movie.get('tmdb_id', 'N/A')}")
        
        print(f"\n   📁 Películas con Direct Source: {len(direct_movies)}")
        for i, movie in enumerate(direct_movies[:3]):
            print(f"      {i+1}. {movie.get('stream_display_name', 'Sin nombre')}")
            print(f"         - Calidad: {movie.get('quality', 'Unknown')}")
            print(f"         - Rating: {movie.get('rating', 'N/A')}")
            print(f"         - Año: {movie.get('year', 'N/A')}")
            print(f"         - TMDB ID: {movie.get('tmdb_id', 'N/A')}")
        
        print(f"\n   🌐 Películas con Stream URL: {len(stream_movies)}")
        for i, movie in enumerate(stream_movies[:3]):
            print(f"      {i+1}. {movie.get('stream_display_name', 'Sin nombre')}")
            print(f"         - Calidad: {movie.get('quality', 'Unknown')}")
            print(f"         - Rating: {movie.get('rating', 'N/A')}")
            print(f"         - Año: {movie.get('year', 'N/A')}")
            print(f"         - TMDB ID: {movie.get('tmdb_id', 'N/A')}")
        
        # ========================================
        # ANÁLISIS DE CALIDAD POR TIPO DE FUENTE
        # ========================================
        print("\n📊 ANÁLISIS DE CALIDAD POR TIPO DE FUENTE")
        print("-" * 40)
        
        print("4. Analizando calidad por tipo de fuente...")
        
        # Contar calidades por tipo de fuente
        quality_by_source = {}
        for movie in movies_detailed:
            source_type = movie.get('source_type', 'Unknown')
            quality = movie.get('quality', 'Unknown')
            
            if source_type not in quality_by_source:
                quality_by_source[source_type] = {}
            
            if quality not in quality_by_source[source_type]:
                quality_by_source[source_type][quality] = 0
            
            quality_by_source[source_type][quality] += 1
        
        for source_type, qualities in quality_by_source.items():
            print(f"\n   📁 {source_type}:")
            total_for_source = sum(qualities.values())
            for quality, count in sorted(qualities.items(), key=lambda x: x[1], reverse=True):
                percentage = (count / total_for_source) * 100 if total_for_source > 0 else 0
                print(f"      - {quality}: {count} ({percentage:.1f}%)")
        
        # ========================================
        # CONSULTA ESPECÍFICA DE DUPLICADOS CON SYMLINK
        # ========================================
        print("\n🔄 DUPLICADOS CON INFORMACIÓN DE SYMLINK")
        print("-" * 40)
        
        print("5. Buscando duplicados con información de symlink...")
        
        # Consulta simplificada para duplicados
        simple_duplicates_query = """
            SELECT 
                stream_display_name,
                COUNT(*) as count,
                GROUP_CONCAT(id ORDER BY id) as ids,
                GROUP_CONCAT(movie_symlink ORDER BY id) as symlinks,
                GROUP_CONCAT(direct_source ORDER BY id) as direct_sources
            FROM streams 
            WHERE type = 2 
            GROUP BY stream_display_name 
            HAVING COUNT(*) > 1
            ORDER BY count DESC
            LIMIT 5
        """
        
        duplicates_with_source = await db_manager.execute_query(simple_duplicates_query)
        print(f"🔄 Duplicados con información de fuente: {len(duplicates_with_source)}")
        
        for i, dup in enumerate(duplicates_with_source):
            print(f"\n   {i+1}. '{dup.get('stream_display_name', 'Sin nombre')}'")
            print(f"      - Aparece {dup.get('count', 0)} veces")
            
            ids = dup.get('ids', '').split(',')
            symlinks = dup.get('symlinks', '').split(',')
            direct_sources = dup.get('direct_sources', '').split(',')
            
            for j, (id_val, symlink, direct) in enumerate(zip(ids, symlinks, direct_sources)):
                symlink_status = "✅ Symlink" if symlink == '1' else "❌"
                direct_status = "✅ Direct" if direct == '1' else "❌"
                print(f"         ID {id_val}: {symlink_status} | {direct_status}")
        
        print("\n✅ Test de symlink y direct_source completado exitosamente")
        print("\n🎯 RESUMEN DE CAPACIDADES:")
        print("   ✅ Análisis de distribución de tipos de fuente")
        print("   ✅ Clasificación por Symlink, Direct Source y Stream URL")
        print("   ✅ Análisis de calidad por tipo de fuente")
        print("   ✅ Detección de duplicados con información de fuente")
        print("   ✅ Información detallada para gestión de calidad")
        
    except Exception as e:
        print(f"\n❌ Error durante el test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        if 'db_manager' in locals():
            await db_manager.close()
    
    return True

if __name__ == "__main__":
    success = asyncio.run(test_symlink_direct())
    if success:
        print("\n🚀 Sistema listo para gestión avanzada de calidad")
        print("   - Análisis de symlink vs direct_source vs stream URL")
        print("   - Gestión inteligente de duplicados por tipo de fuente")
        print("   - Optimización de almacenamiento y rendimiento")
    else:
        print("\n💥 Test falló")
        print("Revisa la configuración y conexión a la base de datos")
        sys.exit(1)
