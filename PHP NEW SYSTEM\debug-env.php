<?php
/**
 * Environment Debug - IPTV XUI One Content Manager
 * ===============================================
 * 
 * Debug environment variable loading
 */

echo "<h1>Environment Debug</h1>";

// Check current working directory
echo "<h2>Current Working Directory</h2>";
echo "<p>" . getcwd() . "</p>";

// Check if .env file exists
$envFile = __DIR__ . '/.env';
echo "<h2>.env File Check</h2>";
echo "<p>Looking for: {$envFile}</p>";
echo "<p>Exists: " . (file_exists($envFile) ? 'YES' : 'NO') . "</p>";

if (file_exists($envFile)) {
    echo "<h3>.env File Contents:</h3>";
    echo "<pre>" . htmlspecialchars(file_get_contents($envFile)) . "</pre>";
}

// Load environment
require_once __DIR__ . '/config/env.php';

echo "<h2>Environment Variables</h2>";
$envVars = ['DB_HOST', 'DB_PORT', 'DB_NAME', 'DB_USER', 'DB_PASSWORD'];

foreach ($envVars as $var) {
    $value = env($var, 'NOT SET');
    if ($var === 'DB_PASSWORD' && $value !== 'NOT SET') {
        $value = '***' . substr($value, -4);
    }
    echo "<p><strong>{$var}:</strong> {$value}</p>";
}

echo "<h2>All $_ENV Variables</h2>";
echo "<pre>";
foreach ($_ENV as $key => $value) {
    if (strpos($key, 'DB_') === 0) {
        if ($key === 'DB_PASSWORD') {
            $value = '***' . substr($value, -4);
        }
        echo "{$key} = {$value}\n";
    }
}
echo "</pre>";

echo "<h2>PHP Extensions</h2>";
$extensions = ['pdo', 'pdo_mysql', 'json', 'curl'];
foreach ($extensions as $ext) {
    $loaded = extension_loaded($ext) ? 'YES' : 'NO';
    echo "<p><strong>{$ext}:</strong> {$loaded}</p>";
}
?>
