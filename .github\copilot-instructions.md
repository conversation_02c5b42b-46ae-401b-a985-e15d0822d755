# Copilot Instructions for IPTV XUI One Content Manager

<!-- Use this file to provide workspace-specific custom instructions to <PERSON><PERSON><PERSON>. For more details, visit https://code.visualstudio.com/docs/copilot/copilot-customization#_use-a-githubcopilotinstructionsmd-file -->

## Project Overview
This is a modern IPTV XUI One content management system built with Python. The application provides:

- **Database Integration**: MariaDB/MySQL for content storage
- **TMDB API Integration**: Movie/TV show metadata and comparison
- **M3U Playlist Support**: Import and manage IPTV playlists
- **Modern UI**: CustomTkinter with icons, animations, and charts
- **Content Management**: Add, remove, edit, and organize content
- **Duplicate Detection**: Find and remove duplicate entries
- **Analytics**: Popularity charts and recommendations
- **Session Management**: User authentication and preferences

## Technical Stack
- **Backend**: Python with asyncio for async operations
- **Database**: MariaDB with mysql-connector-python
- **UI Framework**: CustomTkinter for modern interface
- **APIs**: TMDB API for metadata
- **Charts**: matplotlib for analytics visualization
- **File Processing**: M3U playlist parsing

## Code Style Guidelines
- Use async/await for database and API operations
- Implement proper error handling with try/catch blocks
- Follow PEP 8 style guidelines
- Use type hints for better code clarity
- Create modular, reusable components
- Implement proper logging for debugging

## Key Features to Remember
- Beautiful, modern UI with animations
- Real-time progress bars and loading indicators
- Icon-based navigation and actions
- Responsive design principles
- Database connection pooling
- Efficient batch operations for large datasets
- Session persistence between app launches
