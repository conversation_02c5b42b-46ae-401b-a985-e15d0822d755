<?php
/**
 * Session Management for IPTV XUI One Content Manager
 * ==================================================
 * 
 * Centralized session handling to avoid conflicts
 */

/**
 * Initialize session safely
 */
function initializeSession() {
    // Only start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        // Configure session settings
        ini_set('session.cookie_httponly', 1);
        ini_set('session.use_strict_mode', 1);
        ini_set('session.cookie_samesite', 'Strict');
        
        // Set session name
        session_name('IPTV_MANAGER_SESSION');
        
        // Start session
        session_start();
        
        // Regenerate session ID periodically for security
        if (!isset($_SESSION['last_regeneration'])) {
            $_SESSION['last_regeneration'] = time();
        } elseif (time() - $_SESSION['last_regeneration'] > 300) { // 5 minutes
            session_regenerate_id(true);
            $_SESSION['last_regeneration'] = time();
        }
    }
}

/**
 * Get session value safely
 */
function getSession($key, $default = null) {
    initializeSession();
    return $_SESSION[$key] ?? $default;
}

/**
 * Set session value
 */
function setSession($key, $value) {
    initializeSession();
    $_SESSION[$key] = $value;
}

/**
 * Check if user is authenticated (for future use)
 */
function isAuthenticated() {
    return getSession('authenticated', false);
}

/**
 * Set authentication status
 */
function setAuthenticated($status = true) {
    setSession('authenticated', $status);
    setSession('auth_time', time());
}

/**
 * Destroy session safely
 */
function destroySession() {
    if (session_status() === PHP_SESSION_ACTIVE) {
        session_unset();
        session_destroy();
        
        // Clear session cookie
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000,
                $params["path"], $params["domain"],
                $params["secure"], $params["httponly"]
            );
        }
    }
}

/**
 * Get session info for debugging
 */
function getSessionInfo() {
    return [
        'status' => session_status(),
        'id' => session_id(),
        'name' => session_name(),
        'data' => $_SESSION ?? []
    ];
}

// Initialize session when this file is included
initializeSession();
