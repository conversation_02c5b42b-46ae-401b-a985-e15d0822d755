"""
Explorador TMDB
===============

Panel para explorar y buscar contenido en TMDB.
"""

import tkinter as tk
import customtkinter as ctk
import asyncio
import logging
from typing import Dict, List, Optional, Any
import threading

from core.tmdb_api import TMDBClient

class TMDBBrowser:
    """Explorador de contenido TMDB"""
    
    def __init__(self, parent, settings, db_manager):
        self.parent = parent
        self.settings = settings
        self.db_manager = db_manager
        self.logger = logging.getLogger("iptv_manager.tmdb_browser")
        
        # Cliente TMDB
        self.tmdb_client = None
        
        # Estado
        self.current_results = []
        self.search_history = []
        
        # Crear interfaz
        self._create_layout()
        self._create_search_bar()
        self._create_content_area()
        
        # Cargar contenido popular
        asyncio.create_task(self._load_popular_content())
    
    def _create_layout(self):
        """Crear layout principal"""
        # Frame principal
        self.main_frame = ctk.CTkFrame(self.parent, corner_radius=0)
        self.main_frame.pack(fill="both", expand=True, padx=0, pady=0)
        
        # Título
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="Explorador TMDB",
            font=("Arial", 24, "bold")
        )
        title_label.pack(pady=(20, 10))
    
    def _create_search_bar(self):
        """Crear barra de búsqueda"""
        # Frame de búsqueda
        search_frame = ctk.CTkFrame(self.main_frame, height=60)
        search_frame.pack(fill="x", padx=20, pady=10)
        search_frame.pack_propagate(False)
        
        # Campo de búsqueda
        self.search_entry = ctk.CTkEntry(
            search_frame,
            placeholder_text="Buscar películas o series...",
            font=("Arial", 12),
            width=400,
            height=35
        )
        self.search_entry.pack(side="left", padx=10, pady=12)
        
        # Tipo de contenido
        self.content_type_var = ctk.StringVar(value="Películas")
        self.content_type_combo = ctk.CTkComboBox(
            search_frame,
            values=["Películas", "Series", "Ambos"],
            variable=self.content_type_var,
            width=120,
            height=35
        )
        self.content_type_combo.pack(side="left", padx=10, pady=12)
        
        # Botón de búsqueda
        search_button = ctk.CTkButton(
            search_frame,
            text="🔍 Buscar",
            font=("Arial", 12),
            height=35,
            command=self._search_content
        )
        search_button.pack(side="left", padx=10, pady=12)
        
        # Botón de contenido popular
        popular_button = ctk.CTkButton(
            search_frame,
            text="⭐ Popular",
            font=("Arial", 12),
            height=35,
            command=self._load_popular_content
        )
        popular_button.pack(side="left", padx=10, pady=12)
        
        # Bind Enter key
        self.search_entry.bind('<Return>', lambda e: self._search_content())
    
    def _create_content_area(self):
        """Crear área de contenido"""
        # Frame de contenido
        self.content_frame = ctk.CTkScrollableFrame(self.main_frame)
        self.content_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # Mensaje inicial
        self.status_label = ctk.CTkLabel(
            self.content_frame,
            text="Cargando contenido popular...",
            font=("Arial", 14),
            text_color=self.settings.colors["text_secondary"]
        )
        self.status_label.pack(pady=50)
    
    def _search_content(self):
        """Buscar contenido"""
        query = self.search_entry.get().strip()
        if not query:
            return
        
        content_type = self.content_type_var.get()
        
        # Actualizar estado
        self.status_label.configure(text="Buscando...")
        self.status_label.pack(pady=50)
        
        # Limpiar resultados actuales
        self._clear_results()
        
        # Buscar en hilo separado
        def search_thread():
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                results = loop.run_until_complete(self._perform_search(query, content_type))
                
                # Actualizar interfaz en el hilo principal
                self.parent.after(0, lambda: self._display_results(results))
                
                loop.close()
                
            except Exception as e:
                self.logger.error(f"Error en búsqueda: {str(e)}")
                self.parent.after(0, lambda: self._show_error(f"Error en búsqueda: {str(e)}"))
        
        threading.Thread(target=search_thread, daemon=True).start()
    
    async def _perform_search(self, query: str, content_type: str) -> List[Dict]:
        """Realizar búsqueda"""
        results = []
        
        async with TMDBClient(self.settings, self.db_manager) as client:
            if content_type == "Películas":
                results = await client.search_movies(query)
            elif content_type == "Series":
                results = await client.search_tv_shows(query)
            else:  # Ambos
                movies = await client.search_movies(query)
                series = await client.search_tv_shows(query)
                results = movies + series
        
        return results
    
    async def _load_popular_content(self):
        """Cargar contenido popular"""
        try:
            # Actualizar estado
            self.status_label.configure(text="Cargando contenido popular...")
            self.status_label.pack(pady=50)
            
            # Limpiar resultados actuales
            self._clear_results()
            
            # Cargar en hilo separado
            def load_thread():
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    
                    results = loop.run_until_complete(self._get_popular_content())
                    
                    # Actualizar interfaz en el hilo principal
                    self.parent.after(0, lambda: self._display_results(results))
                    
                    loop.close()
                    
                except Exception as e:
                    self.logger.error(f"Error al cargar contenido popular: {str(e)}")
                    self.parent.after(0, lambda: self._show_error(f"Error al cargar contenido: {str(e)}"))
            
            threading.Thread(target=load_thread, daemon=True).start()
            
        except Exception as e:
            self.logger.error(f"Error al iniciar carga de contenido popular: {str(e)}")
            self._show_error(f"Error: {str(e)}")
    
    async def _get_popular_content(self) -> List[Dict]:
        """Obtener contenido popular"""
        results = []
        
        async with TMDBClient(self.settings, self.db_manager) as client:
            # Obtener películas populares
            movies = await client.get_popular_movies(page=1)
            for movie in movies[:10]:  # Limitar a 10
                movie['content_type'] = 'movie'
                results.append(movie)
            
            # Obtener series populares
            series = await client.get_popular_tv_shows(page=1)
            for serie in series[:10]:  # Limitar a 10
                serie['content_type'] = 'tv'
                results.append(serie)
        
        return results
    
    def _display_results(self, results: List[Dict]):
        """Mostrar resultados"""
        # Ocultar mensaje de estado
        self.status_label.pack_forget()
        
        if not results:
            self.status_label.configure(text="No se encontraron resultados")
            self.status_label.pack(pady=50)
            return
        
        # Mostrar resultados
        self.current_results = results
        
        # Crear grid de resultados
        self._create_results_grid(results)
    
    def _create_results_grid(self, results: List[Dict]):
        """Crear grid de resultados"""
        # Frame para el grid
        grid_frame = ctk.CTkFrame(self.content_frame)
        grid_frame.pack(fill="both", expand=True, pady=10)
        
        # Configurar grid (3 columnas)
        for i in range(3):
            grid_frame.grid_columnconfigure(i, weight=1)
        
        # Crear tarjetas
        for i, item in enumerate(results):
            row = i // 3
            col = i % 3
            
            card = self._create_content_card(grid_frame, item)
            card.grid(row=row, column=col, padx=10, pady=10, sticky="ew")
    
    def _create_content_card(self, parent, item: Dict):
        """Crear tarjeta de contenido"""
        # Frame de la tarjeta
        card_frame = ctk.CTkFrame(parent, corner_radius=15)
        
        # Título
        title = item.get('title', item.get('name', 'Sin título'))
        title_label = ctk.CTkLabel(
            card_frame,
            text=title,
            font=("Arial", 14, "bold"),
            wraplength=200
        )
        title_label.pack(pady=(15, 5))
        
        # Fecha
        date = item.get('release_date', item.get('first_air_date', ''))
        if date:
            date_label = ctk.CTkLabel(
                card_frame,
                text=f"Fecha: {date}",
                font=("Arial", 10),
                text_color=self.settings.colors["text_secondary"]
            )
            date_label.pack(pady=2)
        
        # Puntuación
        rating = item.get('vote_average', 0)
        if rating:
            rating_label = ctk.CTkLabel(
                card_frame,
                text=f"⭐ {rating:.1f}",
                font=("Arial", 10),
                text_color=self.settings.colors["warning"]
            )
            rating_label.pack(pady=2)
        
        # Descripción (truncada)
        overview = item.get('overview', '')
        if overview:
            truncated_overview = overview[:100] + "..." if len(overview) > 100 else overview
            overview_label = ctk.CTkLabel(
                card_frame,
                text=truncated_overview,
                font=("Arial", 9),
                text_color=self.settings.colors["text_secondary"],
                wraplength=200
            )
            overview_label.pack(pady=5)
        
        # Botones de acción
        buttons_frame = ctk.CTkFrame(card_frame, fg_color="transparent")
        buttons_frame.pack(fill="x", pady=(5, 15))
        
        # Botón de detalles
        details_button = ctk.CTkButton(
            buttons_frame,
            text="👁️ Detalles",
            font=("Arial", 10),
            width=80,
            height=30,
            command=lambda: self._show_details(item)
        )
        details_button.pack(side="left", padx=5)
        
        # Botón de agregar
        add_button = ctk.CTkButton(
            buttons_frame,
            text="➕ Agregar",
            font=("Arial", 10),
            width=80,
            height=30,
            fg_color=self.settings.colors["success"],
            command=lambda: self._add_to_database(item)
        )
        add_button.pack(side="right", padx=5)
        
        return card_frame
    
    def _show_details(self, item: Dict):
        """Mostrar detalles del contenido"""
        # Crear ventana de detalles
        details_window = ctk.CTkToplevel(self.parent)
        details_window.title("Detalles del contenido")
        details_window.geometry("600x400")
        
        # Centrar ventana
        details_window.transient(self.parent)
        details_window.grab_set()
        
        # Contenido
        content_frame = ctk.CTkScrollableFrame(details_window)
        content_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Título
        title = item.get('title', item.get('name', 'Sin título'))
        title_label = ctk.CTkLabel(
            content_frame,
            text=title,
            font=("Arial", 20, "bold")
        )
        title_label.pack(pady=(0, 10))
        
        # Detalles
        details = [
            ("Título original", item.get('original_title', item.get('original_name', ''))),
            ("Fecha", item.get('release_date', item.get('first_air_date', ''))),
            ("Puntuación", f"{item.get('vote_average', 0):.1f} ({item.get('vote_count', 0)} votos)"),
            ("Popularidad", f"{item.get('popularity', 0):.1f}"),
            ("Idioma", item.get('original_language', '')),
            ("Adulto", "Sí" if item.get('adult', False) else "No")
        ]
        
        for label, value in details:
            if value:
                detail_frame = ctk.CTkFrame(content_frame)
                detail_frame.pack(fill="x", pady=2)
                
                label_widget = ctk.CTkLabel(
                    detail_frame,
                    text=f"{label}:",
                    font=("Arial", 12, "bold"),
                    anchor="w"
                )
                label_widget.pack(side="left", padx=10, pady=5)
                
                value_widget = ctk.CTkLabel(
                    detail_frame,
                    text=str(value),
                    font=("Arial", 12),
                    anchor="w"
                )
                value_widget.pack(side="right", padx=10, pady=5)
        
        # Descripción
        overview = item.get('overview', '')
        if overview:
            overview_label = ctk.CTkLabel(
                content_frame,
                text="Descripción:",
                font=("Arial", 12, "bold"),
                anchor="w"
            )
            overview_label.pack(pady=(10, 5), fill="x")
            
            overview_text = ctk.CTkTextbox(
                content_frame,
                height=100,
                wrap="word"
            )
            overview_text.pack(fill="x", pady=(0, 10))
            overview_text.insert("1.0", overview)
            overview_text.configure(state="disabled")
        
        # Botón cerrar
        close_button = ctk.CTkButton(
            content_frame,
            text="Cerrar",
            command=details_window.destroy
        )
        close_button.pack(pady=10)
    
    def _add_to_database(self, item: Dict):
        """Agregar contenido a la base de datos"""
        def add_thread():
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                # Agregar a la base de datos
                if item.get('content_type') == 'movie':
                    loop.run_until_complete(self.db_manager.insert_movie(item))
                    content_type = "película"
                else:
                    # Para series, necesitaríamos un método insert_tv_show
                    content_type = "serie"
                
                loop.close()
                
                # Mostrar mensaje de éxito
                self.parent.after(0, lambda: self._show_success(f"{content_type.capitalize()} agregada correctamente"))
                
            except Exception as e:
                self.logger.error(f"Error al agregar contenido: {str(e)}")
                self.parent.after(0, lambda: self._show_error(f"Error al agregar: {str(e)}"))
        
        threading.Thread(target=add_thread, daemon=True).start()
    
    def _clear_results(self):
        """Limpiar resultados actuales"""
        for widget in self.content_frame.winfo_children():
            if widget != self.status_label:
                widget.destroy()
    
    def _show_error(self, message: str):
        """Mostrar error"""
        self.status_label.configure(text=message)
        self.status_label.pack(pady=50)
    
    def _show_success(self, message: str):
        """Mostrar mensaje de éxito"""
        # Crear ventana de éxito temporal
        success_window = ctk.CTkToplevel(self.parent)
        success_window.title("Éxito")
        success_window.geometry("300x100")
        
        # Centrar ventana
        success_window.transient(self.parent)
        success_window.grab_set()
        
        # Mensaje
        message_label = ctk.CTkLabel(
            success_window,
            text=message,
            font=("Arial", 12)
        )
        message_label.pack(pady=20)
        
        # Botón OK
        ok_button = ctk.CTkButton(
            success_window,
            text="OK",
            command=success_window.destroy
        )
        ok_button.pack(pady=10)
        
        # Auto-cerrar después de 3 segundos
        success_window.after(3000, success_window.destroy)
    
    def destroy(self):
        """Destruir componente"""
        if hasattr(self, 'main_frame'):
            self.main_frame.destroy()
