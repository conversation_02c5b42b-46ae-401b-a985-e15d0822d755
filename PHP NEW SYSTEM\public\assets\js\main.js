/**
 * Main JavaScript for IPTV XUI One Content Manager
 * ===============================================
 * 
 * Core functionality and utilities
 */

// Global application state
window.IPTVManager = {
    config: {
        apiBase: 'api',
        theme: 'dark',
        notifications: true,
        autoRefresh: false
    },
    state: {
        currentPage: 'dashboard',
        isLoading: false,
        notifications: []
    }
};

// Utility functions
const Utils = {
    /**
     * Format file size in human readable format
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    /**
     * Format number with thousands separator
     */
    formatNumber(num) {
        return new Intl.NumberFormat().format(num);
    },

    /**
     * Debounce function calls
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    /**
     * Generate unique ID
     */
    generateId() {
        return Math.random().toString(36).substr(2, 9);
    },

    /**
     * Validate file type
     */
    isValidM3UFile(file) {
        const validExtensions = ['m3u', 'm3u8'];
        const extension = file.name.split('.').pop().toLowerCase();
        return validExtensions.includes(extension);
    },

    /**
     * Get file extension
     */
    getFileExtension(filename) {
        return filename.split('.').pop().toLowerCase();
    }
};

// API helper functions
const API = {
    /**
     * Make API request
     */
    async request(endpoint, options = {}) {
        const url = `${IPTVManager.config.apiBase}${endpoint}`;
        const config = {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };

        try {
            const response = await fetch(url, config);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || `HTTP ${response.status}`);
            }

            return data;
        } catch (error) {
            console.error('API Request failed:', error);
            throw error;
        }
    },

    /**
     * Upload M3U files
     */
    async uploadM3U(files, options = {}) {
        const formData = new FormData();
        
        // Add files
        for (let i = 0; i < files.length; i++) {
            formData.append('m3u_files[]', files[i]);
        }

        // Add options
        Object.keys(options).forEach(key => {
            formData.append(key, options[key]);
        });

        return await fetch(`${IPTVManager.config.apiBase}/upload-m3u.php`, {
            method: 'POST',
            body: formData
        }).then(response => response.json());
    },

    /**
     * Detect missing TMDB content
     */
    async detectMissing(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        return await this.request(`/detect-missing.php?${queryString}`);
    },

    /**
     * Export content
     */
    async exportContent(format, filters = {}) {
        const params = { format, ...filters };
        const queryString = new URLSearchParams(params).toString();
        return await this.request(`/export-data.php?${queryString}`);
    }
};

// Notification system
const Notifications = {
    container: null,
    maxNotifications: 5,

    init() {
        this.container = document.getElementById('notifications');
        if (!this.container) {
            this.container = document.createElement('div');
            this.container.id = 'notifications';
            this.container.style.cssText = `
                position: fixed;
                top: 2rem;
                right: 2rem;
                z-index: 1000;
                pointer-events: none;
            `;
            document.body.appendChild(this.container);
        }
    },

    show(message, type = 'info', duration = 3000) {
        if (!IPTVManager.config.notifications) return;

        this.init();

        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.style.cssText = `
            background: var(--${type === 'info' ? 'primary' : type}-color);
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 0.5rem;
            margin-bottom: 0.5rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            transform: translateX(100%);
            transition: transform 0.3s ease;
            pointer-events: auto;
            cursor: pointer;
            max-width: 400px;
            word-wrap: break-word;
        `;

        // Add icon based on type
        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };

        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 0.5rem;">
                <i class="${icons[type] || icons.info}"></i>
                <span>${message}</span>
            </div>
        `;

        // Click to dismiss
        notification.addEventListener('click', () => {
            this.remove(notification);
        });

        this.container.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // Auto dismiss
        if (duration > 0) {
            setTimeout(() => {
                this.remove(notification);
            }, duration);
        }

        // Limit number of notifications
        this.limitNotifications();

        return notification;
    },

    remove(notification) {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    },

    limitNotifications() {
        const notifications = this.container.children;
        while (notifications.length > this.maxNotifications) {
            this.remove(notifications[0]);
        }
    }
};

// Loading overlay
const Loading = {
    overlay: null,

    show(message = 'Loading...') {
        this.hide(); // Remove any existing overlay

        this.overlay = document.createElement('div');
        this.overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            backdrop-filter: blur(4px);
        `;

        this.overlay.innerHTML = `
            <div style="
                background: var(--dark-surface);
                border: 1px solid var(--dark-border);
                border-radius: 1rem;
                padding: 2rem;
                text-align: center;
                color: var(--text-primary);
            ">
                <div style="
                    width: 40px;
                    height: 40px;
                    border: 4px solid var(--dark-border);
                    border-top: 4px solid var(--primary-color);
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                    margin: 0 auto 1rem;
                "></div>
                <div>${message}</div>
            </div>
        `;

        document.body.appendChild(this.overlay);
    },

    hide() {
        if (this.overlay && this.overlay.parentNode) {
            this.overlay.parentNode.removeChild(this.overlay);
            this.overlay = null;
        }
    }
};

// Progress bar component
class ProgressBar {
    constructor(container, options = {}) {
        this.container = container;
        this.options = {
            height: '8px',
            backgroundColor: 'var(--dark-border)',
            progressColor: 'var(--primary-color)',
            borderRadius: '4px',
            animated: true,
            ...options
        };
        this.progress = 0;
        this.init();
    }

    init() {
        this.element = document.createElement('div');
        this.element.style.cssText = `
            width: 100%;
            height: ${this.options.height};
            background: ${this.options.backgroundColor};
            border-radius: ${this.options.borderRadius};
            overflow: hidden;
            position: relative;
        `;

        this.bar = document.createElement('div');
        this.bar.style.cssText = `
            height: 100%;
            background: ${this.options.progressColor};
            border-radius: ${this.options.borderRadius};
            transition: width 0.3s ease;
            width: 0%;
        `;

        if (this.options.animated) {
            this.bar.style.background = `
                linear-gradient(90deg, 
                    ${this.options.progressColor}, 
                    ${this.options.progressColor}aa, 
                    ${this.options.progressColor}
                )
            `;
            this.bar.style.backgroundSize = '200% 100%';
            this.bar.style.animation = 'progressShine 2s ease-in-out infinite';
        }

        this.element.appendChild(this.bar);
        this.container.appendChild(this.element);
    }

    setProgress(percent) {
        this.progress = Math.max(0, Math.min(100, percent));
        this.bar.style.width = `${this.progress}%`;
    }

    getProgress() {
        return this.progress;
    }

    destroy() {
        if (this.element && this.element.parentNode) {
            this.element.parentNode.removeChild(this.element);
        }
    }
}

// File upload handler
class FileUploader {
    constructor(options = {}) {
        this.options = {
            accept: '.m3u,.m3u8',
            multiple: true,
            maxSize: 50 * 1024 * 1024, // 50MB
            ...options
        };
        this.files = [];
        this.callbacks = {};
    }

    on(event, callback) {
        this.callbacks[event] = callback;
    }

    trigger(event, data) {
        if (this.callbacks[event]) {
            this.callbacks[event](data);
        }
    }

    handleFiles(files) {
        this.files = [];
        
        for (let file of files) {
            if (this.validateFile(file)) {
                this.files.push(file);
            }
        }

        this.trigger('filesSelected', this.files);
    }

    validateFile(file) {
        // Check file type
        if (!Utils.isValidM3UFile(file)) {
            this.trigger('error', `Invalid file type: ${file.name}`);
            return false;
        }

        // Check file size
        if (file.size > this.options.maxSize) {
            this.trigger('error', `File too large: ${file.name} (${Utils.formatFileSize(file.size)})`);
            return false;
        }

        return true;
    }

    async upload(options = {}) {
        if (this.files.length === 0) {
            throw new Error('No files selected');
        }

        this.trigger('uploadStart', this.files);

        try {
            const result = await API.uploadM3U(this.files, options);
            this.trigger('uploadComplete', result);
            return result;
        } catch (error) {
            this.trigger('uploadError', error);
            throw error;
        }
    }
}

// Theme manager
const ThemeManager = {
    init() {
        const savedTheme = localStorage.getItem('theme') || 'dark';
        this.setTheme(savedTheme);
    },

    setTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        localStorage.setItem('theme', theme);
        IPTVManager.config.theme = theme;
    },

    toggleTheme() {
        const currentTheme = IPTVManager.config.theme;
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        this.setTheme(newTheme);
        return newTheme;
    }
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize theme
    ThemeManager.init();
    
    // Initialize notifications
    Notifications.init();
    
    // Add CSS animations
    const style = document.createElement('style');
    style.textContent = `
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @keyframes progressShine {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }
    `;
    document.head.appendChild(style);
    
    console.log('IPTV Manager initialized');
});

// Global functions for backward compatibility
window.showNotification = Notifications.show.bind(Notifications);
window.showLoading = Loading.show.bind(Loading);
window.hideLoading = Loading.hide.bind(Loading);
