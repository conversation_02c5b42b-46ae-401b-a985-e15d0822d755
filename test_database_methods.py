"""
Script de prueba para verificar que los métodos del DatabaseManager
funcionan correctamente con la base de datos XUI One
"""

import asyncio
import sys
import os

# Agregar el directorio raíz al path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.database_manager import DatabaseManager
from config.settings import Settings

async def test_database_methods():
    """Probar todos los métodos principales del DatabaseManager"""
    
    # Inicializar configuración y database manager
    settings = Settings()
    db_manager = DatabaseManager(settings)
    
    try:
        # Probar conexión
        print("1. Probando conexión a la base de datos...")
        if await db_manager.test_connection():
            print("✓ Conexión exitosa")
        else:
            print("✗ Error en conexión")
            return
        
        # Verificar tablas
        print("\n2. Verificando tablas...")
        if await db_manager.verify_tables():
            print("✓ Todas las tablas requeridas están presentes")
        else:
            print("✗ Faltan tablas requeridas")
            return
        
        # Obtener estadísticas generales
        print("\n3. Obteniendo estadísticas de contenido...")
        stats = await db_manager.get_content_stats()
        print(f"Estadísticas: {stats}")
        
        # Obtener películas
        print("\n4. Obteniendo películas...")
        movies = await db_manager.get_movies(limit=5)
        print(f"Total películas obtenidas: {len(movies)}")
        if movies:
            print("Primera película:")
            movie = movies[0]
            print(f"  ID: {movie.get('id')}")
            print(f"  Nombre: {movie.get('stream_display_name')}")
            print(f"  Tipo: {movie.get('type')}")
            print(f"  Categoría: {movie.get('category_name', 'N/A')}")
            print(f"  TMDB ID: {movie.get('tmdb_id')}")
        
        # Obtener series
        print("\n5. Obteniendo series...")
        series = await db_manager.get_tv_shows(limit=5)
        print(f"Total series obtenidas: {len(series)}")
        if series:
            print("Primera serie:")
            serie = series[0]
            print(f"  ID: {serie.get('id')}")
            print(f"  Título: {serie.get('title')}")
            print(f"  Categoría: {serie.get('category_name', 'N/A')}")
            print(f"  TMDB ID: {serie.get('tmdb_id')}")
        
        # Obtener categorías
        print("\n6. Obteniendo categorías...")
        categories = await db_manager.get_categories()
        print(f"Total categorías: {len(categories)}")
        if categories:
            print("Primeras 3 categorías:")
            for cat in categories[:3]:
                print(f"  - {cat.get('id')}: {cat.get('category_name')} (tipo: {cat.get('category_type')})")
        
        # Obtener tipos de streams
        print("\n7. Obteniendo tipos de streams...")
        stream_types = await db_manager.get_stream_types()
        print(f"Total tipos de streams: {len(stream_types)}")
        if stream_types:
            print("Tipos de streams:")
            for st in stream_types:
                print(f"  - {st.get('type_id')}: {st.get('type_name')} (key: {st.get('type_key')})")
        
        # Buscar películas
        print("\n8. Buscando películas (término: 'action')...")
        search_results = await db_manager.search_movies("action", limit=3)
        print(f"Resultados de búsqueda: {len(search_results)}")
        for movie in search_results:
            print(f"  - {movie.get('stream_display_name')} (ID: {movie.get('id')})")
        
        # Obtener streams sin TMDB
        print("\n9. Obteniendo contenido sin TMDB...")
        no_tmdb = await db_manager.get_movies_without_tmdb(limit=3)
        print(f"Películas sin TMDB: {len(no_tmdb)}")
        for movie in no_tmdb:
            print(f"  - {movie.get('stream_display_name')} (ID: {movie.get('id')})")
        
        print("\n✓ Todas las pruebas completadas exitosamente")
        
    except Exception as e:
        print(f"✗ Error durante las pruebas: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Cerrar conexiones
        await db_manager.close()

if __name__ == "__main__":
    asyncio.run(test_database_methods())
