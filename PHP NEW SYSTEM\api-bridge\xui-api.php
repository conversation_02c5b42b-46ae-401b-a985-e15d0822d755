<?php
/**
 * XUI Database API Bridge
 * =======================
 * 
 * API intermedia para acceder a la base de datos XUI
 * Sube este archivo a un hosting que tenga acceso a tu base XUI
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Configuración de tu base de datos XUI
$config = [
    'host' => '**************',
    'port' => 3306,
    'database' => 'xui',
    'username' => 'infest84',
    'password' => 'GZM6hh12zHKgfg34FghtAQ84WxloPRX]SV$fP'
];

// Función para conectar a la base de datos
function connectToXUI($config) {
    try {
        $dsn = sprintf(
            "mysql:host=%s;port=%d;dbname=%s;charset=utf8mb4",
            $config['host'],
            $config['port'],
            $config['database']
        );
        
        $pdo = new PDO($dsn, $config['username'], $config['password'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_TIMEOUT => 10
        ]);
        
        return $pdo;
    } catch (Exception $e) {
        throw new Exception("Error conectando a XUI: " . $e->getMessage());
    }
}

// Función para ejecutar consultas seguras
function executeQuery($pdo, $sql, $params = []) {
    try {
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        throw new Exception("Error en consulta: " . $e->getMessage());
    }
}

// Manejar solicitudes
try {
    $action = $_GET['action'] ?? $_POST['action'] ?? '';
    
    if (empty($action)) {
        throw new Exception("Acción no especificada");
    }
    
    $pdo = connectToXUI($config);
    $response = ['success' => true, 'data' => null];
    
    switch ($action) {
        case 'test':
            $result = executeQuery($pdo, "SELECT VERSION() as version, NOW() as current_time");
            $response['data'] = [
                'status' => 'connected',
                'version' => $result[0]['version'],
                'time' => $result[0]['current_time']
            ];
            break;
            
        case 'stats':
            $sql = "
                SELECT 
                    COUNT(*) as total_streams,
                    SUM(CASE WHEN type = 1 THEN 1 ELSE 0 END) as live_tv,
                    SUM(CASE WHEN type = 2 THEN 1 ELSE 0 END) as movies,
                    SUM(CASE WHEN type = 3 THEN 1 ELSE 0 END) as series,
                    SUM(CASE WHEN type = 2 AND movie_symlink = 1 THEN 1 ELSE 0 END) as symlink_movies,
                    SUM(CASE WHEN type = 2 AND direct_source = 1 THEN 1 ELSE 0 END) as direct_movies
                FROM streams
            ";
            $response['data'] = executeQuery($pdo, $sql)[0];
            break;
            
        case 'quality':
            $sql = "
                SELECT 
                    SUM(CASE WHEN stream_display_name LIKE '%4K%' OR stream_display_name LIKE '%2160p%' THEN 1 ELSE 0 END) as content_4k,
                    SUM(CASE WHEN stream_display_name LIKE '%60fps%' OR stream_display_name LIKE '%60FPS%' THEN 1 ELSE 0 END) as content_60fps,
                    SUM(CASE WHEN stream_display_name LIKE '%HDR%' THEN 1 ELSE 0 END) as content_hdr,
                    SUM(CASE WHEN stream_display_name LIKE '%1080p%' OR stream_display_name LIKE '%FHD%' THEN 1 ELSE 0 END) as content_fhd,
                    SUM(CASE WHEN stream_display_name LIKE '%720p%' OR stream_display_name LIKE '%HD%' THEN 1 ELSE 0 END) as content_hd
                FROM streams 
                WHERE type = 2
            ";
            $response['data'] = executeQuery($pdo, $sql)[0];
            break;
            
        case 'recent':
            $limit = intval($_GET['limit'] ?? 10);
            $sql = "
                SELECT id, stream_display_name, added, year, rating, type
                FROM streams 
                WHERE type = 2 
                ORDER BY added DESC 
                LIMIT ?
            ";
            $response['data'] = executeQuery($pdo, $sql, [$limit]);
            break;
            
        case 'popular':
            $limit = intval($_GET['limit'] ?? 10);
            $sql = "
                SELECT id, stream_display_name, year, rating, type
                FROM streams 
                WHERE type = 2 AND rating > 0
                ORDER BY rating DESC 
                LIMIT ?
            ";
            $response['data'] = executeQuery($pdo, $sql, [$limit]);
            break;
            
        case 'search':
            $term = $_GET['term'] ?? $_POST['term'] ?? '';
            if (empty($term)) {
                throw new Exception("Término de búsqueda requerido");
            }
            
            $limit = intval($_GET['limit'] ?? 50);
            $searchPattern = '%' . $term . '%';
            
            $sql = "
                SELECT id, stream_display_name, year, rating, type, added
                FROM streams 
                WHERE stream_display_name LIKE ? 
                ORDER BY added DESC 
                LIMIT ?
            ";
            $response['data'] = executeQuery($pdo, $sql, [$searchPattern, $limit]);
            break;
            
        case '4k-content':
            $limit = intval($_GET['limit'] ?? 50);
            $sql = "
                SELECT id, stream_display_name, year, rating, added
                FROM streams 
                WHERE type = 2 
                AND (stream_display_name LIKE '%4K%' OR stream_display_name LIKE '%2160p%')
                ORDER BY added DESC 
                LIMIT ?
            ";
            $response['data'] = executeQuery($pdo, $sql, [$limit]);
            break;
            
        case 'categories':
            $sql = "SELECT * FROM streams_categories ORDER BY cat_order ASC, category_name ASC";
            $response['data'] = executeQuery($pdo, $sql);
            break;
            
        case 'duplicates':
            $limit = intval($_GET['limit'] ?? 50);
            $sql = "
                SELECT 
                    stream_display_name,
                    COUNT(*) as duplicate_count,
                    GROUP_CONCAT(id) as stream_ids,
                    SUM(CASE WHEN movie_symlink = 1 THEN 1 ELSE 0 END) as symlink_count,
                    SUM(CASE WHEN direct_source = 1 THEN 1 ELSE 0 END) as direct_count
                FROM streams 
                WHERE type = 2 
                GROUP BY stream_display_name 
                HAVING COUNT(*) > 1
                ORDER BY duplicate_count DESC
                LIMIT ?
            ";
            $response['data'] = executeQuery($pdo, $sql, [$limit]);
            break;
            
        case 'tables':
            $tables = ['streams', 'streams_categories', 'streams_series', 'streams_episodes'];
            $tableInfo = [];
            
            foreach ($tables as $table) {
                $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
                if ($stmt->fetch()) {
                    $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
                    $count = $stmt->fetch()['count'];
                    $tableInfo[$table] = ['exists' => true, 'count' => $count];
                } else {
                    $tableInfo[$table] = ['exists' => false, 'count' => 0];
                }
            }
            
            $response['data'] = $tableInfo;
            break;
            
        default:
            throw new Exception("Acción no válida: $action");
    }
    
    echo json_encode($response);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}
?>
