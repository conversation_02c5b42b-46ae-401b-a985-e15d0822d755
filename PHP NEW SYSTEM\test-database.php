<?php
/**
 * Database Connection Test - IPTV XUI One Content Manager
 * =======================================================
 * 
 * Tests database connection and displays detailed diagnostics
 */

header('Content-Type: text/html; charset=utf-8');

// Load environment configuration
require_once __DIR__ . '/config/env.php';
loadEnvironmentVariables();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Connection Test - IPTV XUI Manager</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #0f172a;
            color: #f1f5f9;
            margin: 0;
            padding: 2rem;
            line-height: 1.6;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: #1e293b;
            border-radius: 1rem;
            padding: 2rem;
            border: 1px solid #334155;
        }
        h1 {
            color: #3b82f6;
            text-align: center;
            margin-bottom: 2rem;
            font-size: 2rem;
        }
        .test-section {
            background: #0f172a;
            border: 1px solid #334155;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }
        .test-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #94a3b8;
        }
        .status {
            padding: 0.5rem 1rem;
            border-radius: 0.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
            display: inline-block;
        }
        .status.success {
            background: rgba(16, 185, 129, 0.2);
            color: #10b981;
            border: 1px solid #10b981;
        }
        .status.error {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
            border: 1px solid #ef4444;
        }
        .status.warning {
            background: rgba(245, 158, 11, 0.2);
            color: #f59e0b;
            border: 1px solid #f59e0b;
        }
        .config-item {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            border-bottom: 1px solid #334155;
        }
        .config-item:last-child {
            border-bottom: none;
        }
        .config-label {
            font-weight: 500;
            color: #94a3b8;
        }
        .config-value {
            color: #f1f5f9;
            font-family: monospace;
        }
        .error-details {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid #ef4444;
            border-radius: 0.5rem;
            padding: 1rem;
            margin-top: 1rem;
            font-family: monospace;
            font-size: 0.875rem;
        }
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #3b82f6;
            color: white;
            text-decoration: none;
            border-radius: 0.5rem;
            margin-top: 1rem;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #1e40af;
        }
        .query-result {
            background: #0f172a;
            border: 1px solid #334155;
            border-radius: 0.5rem;
            padding: 1rem;
            margin-top: 1rem;
            max-height: 300px;
            overflow-y: auto;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        .table th, .table td {
            padding: 0.5rem;
            text-align: left;
            border-bottom: 1px solid #334155;
        }
        .table th {
            background: #334155;
            color: #f1f5f9;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Database Connection Test</h1>

        <?php
        // Test 1: PHP Extensions
        echo '<div class="test-section">';
        echo '<div class="test-title">1. PHP Extensions Check</div>';
        
        $extensions = ['pdo', 'pdo_mysql', 'json', 'curl'];
        $extensionResults = [];
        
        foreach ($extensions as $ext) {
            $loaded = extension_loaded($ext);
            $extensionResults[$ext] = $loaded;
            $status = $loaded ? 'success' : 'error';
            $statusText = $loaded ? '✓ Loaded' : '✗ Missing';
            echo "<div class='config-item'>";
            echo "<span class='config-label'>{$ext}</span>";
            echo "<span class='status {$status}'>{$statusText}</span>";
            echo "</div>";
        }
        echo '</div>';

        // Test 2: Environment Configuration
        echo '<div class="test-section">';
        echo '<div class="test-title">2. Environment Configuration</div>';
        
        $config = [
            'DB_HOST' => env('DB_HOST', 'Not set'),
            'DB_PORT' => env('DB_PORT', 'Not set'),
            'DB_NAME' => env('DB_NAME', 'Not set'),
            'DB_USER' => env('DB_USER', 'Not set'),
            'DB_PASSWORD' => env('DB_PASSWORD') ? '***' . substr(env('DB_PASSWORD'), -4) : 'Not set'
        ];
        
        foreach ($config as $key => $value) {
            echo "<div class='config-item'>";
            echo "<span class='config-label'>{$key}</span>";
            echo "<span class='config-value'>{$value}</span>";
            echo "</div>";
        }
        echo '</div>';

        // Test 3: Database Connection
        echo '<div class="test-section">';
        echo '<div class="test-title">3. Database Connection Test</div>';
        
        $connectionSuccess = false;
        $connectionError = null;
        $pdo = null;
        
        if (!extension_loaded('pdo_mysql')) {
            echo '<div class="status error">✗ Cannot test - PDO MySQL extension not loaded</div>';
            echo '<div class="error-details">Please install php-mysql extension to enable database connectivity.</div>';
        } else {
            try {
                $dsn = sprintf(
                    "mysql:host=%s;port=%d;dbname=%s;charset=utf8mb4",
                    env('DB_HOST', '**************'),
                    (int)env('DB_PORT', 3306),
                    env('DB_NAME', 'xui'),
                    'utf8mb4'
                );
                
                $options = [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false,
                    PDO::ATTR_TIMEOUT => 10
                ];
                
                echo "<div class='config-item'>";
                echo "<span class='config-label'>DSN</span>";
                echo "<span class='config-value'>{$dsn}</span>";
                echo "</div>";
                
                $startTime = microtime(true);
                $pdo = new PDO(
                    $dsn,
                    env('DB_USER', 'infest84'),
                    env('DB_PASSWORD', 'GZM6hh12zHKgfg34FghtAQ84WxloPRX]SV$fP'),
                    $options
                );
                $connectionTime = round((microtime(true) - $startTime) * 1000, 2);
                
                $connectionSuccess = true;
                echo '<div class="status success">✓ Connection Successful</div>';
                echo "<div class='config-item'>";
                echo "<span class='config-label'>Connection Time</span>";
                echo "<span class='config-value'>{$connectionTime}ms</span>";
                echo "</div>";
                
            } catch (Exception $e) {
                $connectionError = $e->getMessage();
                echo '<div class="status error">✗ Connection Failed</div>';
                echo '<div class="error-details">' . htmlspecialchars($connectionError) . '</div>';
            }
        }
        echo '</div>';

        // Test 4: Database Structure (if connected)
        if ($connectionSuccess && $pdo) {
            echo '<div class="test-section">';
            echo '<div class="test-title">4. Database Structure Check</div>';
            
            try {
                // Check tables
                $tables = ['streams', 'streams_categories', 'streams_series', 'streams_episodes'];
                $tableResults = [];
                
                foreach ($tables as $table) {
                    $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
                    $stmt->execute([$table]);
                    $exists = $stmt->rowCount() > 0;
                    $tableResults[$table] = $exists;
                    
                    $status = $exists ? 'success' : 'warning';
                    $statusText = $exists ? '✓ Exists' : '⚠ Missing';
                    echo "<div class='config-item'>";
                    echo "<span class='config-label'>Table: {$table}</span>";
                    echo "<span class='status {$status}'>{$statusText}</span>";
                    echo "</div>";
                }
                
                // Sample data check
                if ($tableResults['streams']) {
                    $stmt = $pdo->query("SELECT COUNT(*) as count FROM streams LIMIT 1");
                    $count = $stmt->fetch()['count'];
                    echo "<div class='config-item'>";
                    echo "<span class='config-label'>Total Streams</span>";
                    echo "<span class='config-value'>{$count}</span>";
                    echo "</div>";
                    
                    if ($count > 0) {
                        echo '<div class="query-result">';
                        echo '<strong>Sample Data (First 5 streams):</strong>';
                        $stmt = $pdo->query("SELECT id, stream_display_name, type, added FROM streams ORDER BY id LIMIT 5");
                        $results = $stmt->fetchAll();
                        
                        if ($results) {
                            echo '<table class="table">';
                            echo '<tr><th>ID</th><th>Name</th><th>Type</th><th>Added</th></tr>';
                            foreach ($results as $row) {
                                echo '<tr>';
                                echo '<td>' . htmlspecialchars($row['id']) . '</td>';
                                echo '<td>' . htmlspecialchars($row['stream_display_name']) . '</td>';
                                echo '<td>' . htmlspecialchars($row['type']) . '</td>';
                                echo '<td>' . htmlspecialchars($row['added']) . '</td>';
                                echo '</tr>';
                            }
                            echo '</table>';
                        }
                        echo '</div>';
                    }
                }
                
            } catch (Exception $e) {
                echo '<div class="status error">✗ Structure Check Failed</div>';
                echo '<div class="error-details">' . htmlspecialchars($e->getMessage()) . '</div>';
            }
            echo '</div>';
        }

        // Summary and Actions
        echo '<div class="test-section">';
        echo '<div class="test-title">5. Summary & Actions</div>';
        
        if ($connectionSuccess) {
            echo '<div class="status success">✓ Database connection is working!</div>';
            echo '<p>Your system can connect to the XUI One database. The application should work with real data.</p>';
            echo '<a href="public/index.php" class="btn">🚀 Launch Application</a>';
        } else {
            echo '<div class="status error">✗ Database connection failed</div>';
            echo '<p>The application will run in demo mode. To use real data, please:</p>';
            echo '<ul>';
            if (!extension_loaded('pdo_mysql')) {
                echo '<li>Install PHP MySQL extension (php-mysql)</li>';
            }
            if ($connectionError) {
                if (strpos($connectionError, 'Access denied') !== false) {
                    echo '<li>Check database credentials in .env file</li>';
                } elseif (strpos($connectionError, 'Connection refused') !== false) {
                    echo '<li>Verify database server is running and accessible</li>';
                    echo '<li>Check firewall settings for port 3306</li>';
                } else {
                    echo '<li>Review connection error details above</li>';
                }
            }
            echo '</ul>';
            echo '<a href="public/index.php" class="btn">📱 Try Demo Mode</a>';
        }
        echo '</div>';
        ?>
    </div>
</body>
</html>
