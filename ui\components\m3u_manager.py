"""
Gestor de archivos M3U
=====================

Panel para importar, gestionar y procesar archivos M3U.
"""

import tkinter as tk
from tkinter import filedialog, messagebox
import customtkinter as ctk
import asyncio
import logging
from typing import Dict, List, Optional, Any
from pathlib import Path
import threading

from core.m3u_parser import M3UParser
from ui.components.loading_dialog import LoadingDialog

class M3UManager:
    """Gestor de archivos M3U"""
    
    def __init__(self, parent, settings, db_manager):
        self.parent = parent
        self.settings = settings
        self.db_manager = db_manager
        self.logger = logging.getLogger("iptv_manager.m3u_manager")
        
        # Parser M3U
        self.parser = M3UParser(db_manager)
        
        # Estado
        self.current_file = None
        self.entries = []
        self.stats = {}
        
        # Crear interfaz
        self._create_layout()
        self._create_toolbar()
        self._create_content_area()
        
        # Cargar datos existentes
        asyncio.create_task(self._load_playlists())
    
    def _create_layout(self):
        """Crear layout principal"""
        # Frame principal
        self.main_frame = ctk.CTkFrame(self.parent, corner_radius=0)
        self.main_frame.pack(fill="both", expand=True, padx=0, pady=0)
        
        # Configurar grid
        self.main_frame.grid_rowconfigure(1, weight=1)
        self.main_frame.grid_columnconfigure(0, weight=1)
        
        # Título
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="Gestor de Archivos M3U",
            font=("Arial", 24, "bold")
        )
        title_label.grid(row=0, column=0, pady=(20, 10), sticky="w", padx=20)
    
    def _create_toolbar(self):
        """Crear barra de herramientas"""
        # Frame de toolbar
        self.toolbar_frame = ctk.CTkFrame(self.main_frame, height=60)
        self.toolbar_frame.grid(row=1, column=0, sticky="ew", padx=20, pady=(0, 10))
        self.toolbar_frame.grid_propagate(False)
        
        # Botones
        self.import_button = ctk.CTkButton(
            self.toolbar_frame,
            text="📁 Importar M3U",
            font=("Arial", 12),
            height=40,
            command=self._import_m3u_file
        )
        self.import_button.pack(side="left", padx=10, pady=10)
        
        self.refresh_button = ctk.CTkButton(
            self.toolbar_frame,
            text="🔄 Actualizar",
            font=("Arial", 12),
            height=40,
            command=self._refresh_data
        )
        self.refresh_button.pack(side="left", padx=10, pady=10)
        
        self.duplicates_button = ctk.CTkButton(
            self.toolbar_frame,
            text="🔍 Buscar Duplicados",
            font=("Arial", 12),
            height=40,
            command=self._find_duplicates
        )
        self.duplicates_button.pack(side="left", padx=10, pady=10)
        
        # Información de archivo actual
        self.file_info_label = ctk.CTkLabel(
            self.toolbar_frame,
            text="No hay archivo cargado",
            font=("Arial", 11),
            text_color=self.settings.colors["text_secondary"]
        )
        self.file_info_label.pack(side="right", padx=10, pady=10)
    
    def _create_content_area(self):
        """Crear área de contenido"""
        # Frame de contenido
        self.content_frame = ctk.CTkFrame(self.main_frame)
        self.content_frame.grid(row=2, column=0, sticky="nsew", padx=20, pady=(0, 20))
        
        # Configurar grid
        self.content_frame.grid_rowconfigure(0, weight=1)
        self.content_frame.grid_columnconfigure(0, weight=1)
        
        # Crear notebook para pestañas
        self.notebook = ctk.CTkTabview(self.content_frame)
        self.notebook.grid(row=0, column=0, sticky="nsew", padx=20, pady=20)
        
        # Pestañas
        self.playlists_tab = self.notebook.add("Playlists")
        self.entries_tab = self.notebook.add("Entradas")
        self.duplicates_tab = self.notebook.add("Duplicados")
        self.stats_tab = self.notebook.add("Estadísticas")
        
        # Configurar pestañas
        self._setup_playlists_tab()
        self._setup_entries_tab()
        self._setup_duplicates_tab()
        self._setup_stats_tab()
    
    def _setup_playlists_tab(self):
        """Configurar pestaña de playlists"""
        # Lista de playlists
        self.playlists_frame = ctk.CTkScrollableFrame(self.playlists_tab)
        self.playlists_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Encabezado
        header_frame = ctk.CTkFrame(self.playlists_frame, height=40)
        header_frame.pack(fill="x", pady=(0, 10))
        header_frame.pack_propagate(False)
        
        # Columnas
        columns = ["Nombre", "Entradas", "Duplicados", "Estado", "Acciones"]
        for i, col in enumerate(columns):
            label = ctk.CTkLabel(
                header_frame,
                text=col,
                font=("Arial", 12, "bold")
            )
            label.grid(row=0, column=i, padx=10, pady=10, sticky="w")
        
        # Configurar grid
        header_frame.grid_columnconfigure(0, weight=2)
        header_frame.grid_columnconfigure(1, weight=1)
        header_frame.grid_columnconfigure(2, weight=1)
        header_frame.grid_columnconfigure(3, weight=1)
        header_frame.grid_columnconfigure(4, weight=2)
        
        # Lista de playlists
        self.playlists_list_frame = ctk.CTkFrame(self.playlists_frame)
        self.playlists_list_frame.pack(fill="both", expand=True)
    
    def _setup_entries_tab(self):
        """Configurar pestaña de entradas"""
        # Lista de entradas
        self.entries_frame = ctk.CTkScrollableFrame(self.entries_tab)
        self.entries_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Barra de búsqueda
        search_frame = ctk.CTkFrame(self.entries_frame, height=50)
        search_frame.pack(fill="x", pady=(0, 10))
        search_frame.pack_propagate(False)
        
        # Campo de búsqueda
        self.search_entry = ctk.CTkEntry(
            search_frame,
            placeholder_text="Buscar entradas...",
            width=300
        )
        self.search_entry.pack(side="left", padx=10, pady=10)
        
        # Botón de búsqueda
        search_button = ctk.CTkButton(
            search_frame,
            text="🔍",
            width=40,
            command=self._search_entries
        )
        search_button.pack(side="left", padx=5, pady=10)
        
        # Filtros
        self.filter_combobox = ctk.CTkComboBox(
            search_frame,
            values=["Todos", "Películas", "Series", "En Vivo", "Otros"],
            width=150
        )
        self.filter_combobox.pack(side="left", padx=10, pady=10)
        
        # Lista de entradas
        self.entries_list_frame = ctk.CTkFrame(self.entries_frame)
        self.entries_list_frame.pack(fill="both", expand=True)
    
    def _setup_duplicates_tab(self):
        """Configurar pestaña de duplicados"""
        # Lista de duplicados
        self.duplicates_frame = ctk.CTkScrollableFrame(self.duplicates_tab)
        self.duplicates_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Botón para buscar duplicados
        search_duplicates_button = ctk.CTkButton(
            self.duplicates_frame,
            text="🔍 Buscar Duplicados",
            font=("Arial", 12),
            height=40,
            command=self._find_duplicates
        )
        search_duplicates_button.pack(pady=10)
        
        # Lista de duplicados
        self.duplicates_list_frame = ctk.CTkFrame(self.duplicates_frame)
        self.duplicates_list_frame.pack(fill="both", expand=True)
    
    def _setup_stats_tab(self):
        """Configurar pestaña de estadísticas"""
        # Frame de estadísticas
        self.stats_frame = ctk.CTkScrollableFrame(self.stats_tab)
        self.stats_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Tarjetas de estadísticas
        self.stats_cards_frame = ctk.CTkFrame(self.stats_frame)
        self.stats_cards_frame.pack(fill="x", pady=10)
        
        # Estadísticas detalladas
        self.detailed_stats_frame = ctk.CTkFrame(self.stats_frame)
        self.detailed_stats_frame.pack(fill="both", expand=True, pady=10)
    
    def _import_m3u_file(self):
        """Importar archivo M3U"""
        file_path = filedialog.askopenfilename(
            title="Seleccionar archivo M3U",
            filetypes=[
                ("Archivos M3U", "*.m3u"),
                ("Archivos M3U8", "*.m3u8"),
                ("Todos los archivos", "*.*")
            ]
        )
        
        if file_path:
            self.load_file(file_path)
    
    def load_file(self, file_path: str):
        """Cargar archivo M3U"""
        self.current_file = file_path
        
        # Actualizar información del archivo
        file_name = Path(file_path).name
        self.file_info_label.configure(text=f"Archivo: {file_name}")
        
        # Procesar archivo en hilo separado
        def process_file():
            try:
                with LoadingDialog(self.parent, "Procesando archivo M3U...") as dialog:
                    # Procesar archivo
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    
                    def update_progress(value, message):
                        dialog.update_progress(value, message)
                    
                    result = loop.run_until_complete(
                        self.parser.process_file_async(
                            file_path,
                            progress_callback=update_progress
                        )
                    )
                    
                    loop.close()
                    
                    if result['success']:
                        self.entries = result.get('entries', [])
                        self.stats = result.get('stats', {})
                        
                        # Actualizar interfaz
                        self._update_interface()
                        
                        messagebox.showinfo(
                            "Éxito",
                            f"Archivo procesado correctamente.\\n"
                            f"Entradas: {result.get('entries_count', 0)}"
                        )
                    else:
                        messagebox.showerror(
                            "Error",
                            f"Error al procesar archivo: {result.get('error', 'Error desconocido')}"
                        )
                        
            except Exception as e:
                messagebox.showerror("Error", f"Error al procesar archivo: {str(e)}")
        
        # Ejecutar en hilo separado
        threading.Thread(target=process_file, daemon=True).start()
    
    def _refresh_data(self):
        """Actualizar datos"""
        asyncio.create_task(self._load_playlists())
    
    def _find_duplicates(self):
        """Buscar duplicados"""
        async def find_duplicates():
            try:
                duplicates = await self.db_manager.get_duplicates()
                self._update_duplicates_list(duplicates)
            except Exception as e:
                self.logger.error(f"Error al buscar duplicados: {str(e)}")
        
        asyncio.create_task(find_duplicates())
    
    def _search_entries(self):
        """Buscar entradas"""
        search_term = self.search_entry.get()
        filter_type = self.filter_combobox.get()
        
        # Implementar búsqueda
        # Por ahora, solo actualiza la lista
        self._update_entries_list(self.entries)
    
    async def _load_playlists(self):
        """Cargar playlists existentes"""
        try:
            # Obtener playlists de la base de datos
            playlists = await self.db_manager.execute_query(
                "SELECT * FROM playlists ORDER BY created_at DESC"
            )
            
            self._update_playlists_list(playlists)
            
        except Exception as e:
            self.logger.error(f"Error al cargar playlists: {str(e)}")
    
    def _update_interface(self):
        """Actualizar interfaz con datos cargados"""
        # Actualizar lista de entradas
        self._update_entries_list(self.entries)
        
        # Actualizar estadísticas
        self._update_stats(self.stats)
    
    def _update_playlists_list(self, playlists: List[Dict]):
        """Actualizar lista de playlists"""
        # Limpiar lista actual
        for widget in self.playlists_list_frame.winfo_children():
            widget.destroy()
        
        # Agregar playlists
        for playlist in playlists:
            self._create_playlist_row(playlist)
    
    def _create_playlist_row(self, playlist: Dict):
        """Crear fila de playlist"""
        row_frame = ctk.CTkFrame(self.playlists_list_frame, height=50)
        row_frame.pack(fill="x", pady=2)
        row_frame.pack_propagate(False)
        
        # Configurar grid
        row_frame.grid_columnconfigure(0, weight=2)
        row_frame.grid_columnconfigure(1, weight=1)
        row_frame.grid_columnconfigure(2, weight=1)
        row_frame.grid_columnconfigure(3, weight=1)
        row_frame.grid_columnconfigure(4, weight=2)
        
        # Nombre
        name_label = ctk.CTkLabel(
            row_frame,
            text=playlist.get('name', 'Sin nombre'),
            font=("Arial", 12)
        )
        name_label.grid(row=0, column=0, padx=10, pady=10, sticky="w")
        
        # Entradas
        entries_label = ctk.CTkLabel(
            row_frame,
            text=str(playlist.get('total_entries', 0)),
            font=("Arial", 12)
        )
        entries_label.grid(row=0, column=1, padx=10, pady=10)
        
        # Duplicados
        duplicates_label = ctk.CTkLabel(
            row_frame,
            text=str(playlist.get('duplicates_found', 0)),
            font=("Arial", 12)
        )
        duplicates_label.grid(row=0, column=2, padx=10, pady=10)
        
        # Estado
        status_label = ctk.CTkLabel(
            row_frame,
            text=playlist.get('status', 'unknown'),
            font=("Arial", 12)
        )
        status_label.grid(row=0, column=3, padx=10, pady=10)
        
        # Acciones
        actions_frame = ctk.CTkFrame(row_frame, fg_color="transparent")
        actions_frame.grid(row=0, column=4, padx=10, pady=10, sticky="e")
        
        view_button = ctk.CTkButton(
            actions_frame,
            text="👁️",
            width=30,
            height=30,
            command=lambda: self._view_playlist(playlist['id'])
        )
        view_button.pack(side="left", padx=2)
        
        delete_button = ctk.CTkButton(
            actions_frame,
            text="🗑️",
            width=30,
            height=30,
            fg_color=self.settings.colors["error"],
            command=lambda: self._delete_playlist(playlist['id'])
        )
        delete_button.pack(side="left", padx=2)
    
    def _update_entries_list(self, entries: List):
        """Actualizar lista de entradas"""
        # Limpiar lista actual
        for widget in self.entries_list_frame.winfo_children():
            widget.destroy()
        
        # Mensaje si no hay entradas
        if not entries:
            no_entries_label = ctk.CTkLabel(
                self.entries_list_frame,
                text="No hay entradas cargadas",
                font=("Arial", 14),
                text_color=self.settings.colors["text_secondary"]
            )
            no_entries_label.pack(pady=50)
            return
        
        # Agregar entradas (mostrar solo las primeras 100)
        for i, entry in enumerate(entries[:100]):
            self._create_entry_row(entry, i)
        
        # Mostrar mensaje si hay más entradas
        if len(entries) > 100:
            more_label = ctk.CTkLabel(
                self.entries_list_frame,
                text=f"... y {len(entries) - 100} entradas más",
                font=("Arial", 12),
                text_color=self.settings.colors["text_secondary"]
            )
            more_label.pack(pady=10)
    
    def _create_entry_row(self, entry, index: int):
        """Crear fila de entrada"""
        row_frame = ctk.CTkFrame(self.entries_list_frame, height=40)
        row_frame.pack(fill="x", pady=1)
        row_frame.pack_propagate(False)
        
        # Título
        title_label = ctk.CTkLabel(
            row_frame,
            text=getattr(entry, 'title', 'Sin título'),
            font=("Arial", 11),
            anchor="w"
        )
        title_label.pack(side="left", padx=10, pady=5, fill="x", expand=True)
        
        # Tipo
        type_label = ctk.CTkLabel(
            row_frame,
            text=getattr(entry, 'content_type', 'unknown'),
            font=("Arial", 10),
            width=80
        )
        type_label.pack(side="right", padx=10, pady=5)
        
        # Calidad
        quality_label = ctk.CTkLabel(
            row_frame,
            text=getattr(entry, 'quality', ''),
            font=("Arial", 10),
            width=60
        )
        quality_label.pack(side="right", padx=5, pady=5)
    
    def _update_duplicates_list(self, duplicates: List[Dict]):
        """Actualizar lista de duplicados"""
        # Limpiar lista actual
        for widget in self.duplicates_list_frame.winfo_children():
            widget.destroy()
        
        # Mensaje si no hay duplicados
        if not duplicates:
            no_duplicates_label = ctk.CTkLabel(
                self.duplicates_list_frame,
                text="No se encontraron duplicados",
                font=("Arial", 14),
                text_color=self.settings.colors["success"]
            )
            no_duplicates_label.pack(pady=50)
            return
        
        # Agregar duplicados
        for duplicate in duplicates:
            self._create_duplicate_row(duplicate)
    
    def _create_duplicate_row(self, duplicate: Dict):
        """Crear fila de duplicado"""
        row_frame = ctk.CTkFrame(self.duplicates_list_frame, height=60)
        row_frame.pack(fill="x", pady=5)
        row_frame.pack_propagate(False)
        
        # Título
        title_label = ctk.CTkLabel(
            row_frame,
            text=duplicate.get('title', 'Sin título'),
            font=("Arial", 12, "bold"),
            anchor="w"
        )
        title_label.pack(side="left", padx=10, pady=5, fill="x", expand=True)
        
        # Contador
        count_label = ctk.CTkLabel(
            row_frame,
            text=f"{duplicate.get('count', 0)} duplicados",
            font=("Arial", 11),
            text_color=self.settings.colors["warning"]
        )
        count_label.pack(side="right", padx=10, pady=5)
        
        # Botón eliminar
        delete_button = ctk.CTkButton(
            row_frame,
            text="🗑️ Eliminar",
            width=100,
            height=30,
            fg_color=self.settings.colors["error"],
            command=lambda: self._delete_duplicates(duplicate)
        )
        delete_button.pack(side="right", padx=10, pady=5)
    
    def _update_stats(self, stats: Dict):
        """Actualizar estadísticas"""
        # Limpiar stats actuales
        for widget in self.stats_cards_frame.winfo_children():
            widget.destroy()
        
        # Crear tarjetas de estadísticas
        stats_data = [
            ("Total Entradas", stats.get('total_entries', 0)),
            ("Duplicados", stats.get('duplicates_count', 0)),
            ("Películas", stats.get('content_types', {}).get('movie', 0)),
            ("Series", stats.get('content_types', {}).get('series', 0)),
            ("En Vivo", stats.get('content_types', {}).get('live', 0)),
            ("Otros", stats.get('content_types', {}).get('other', 0))
        ]
        
        for i, (title, value) in enumerate(stats_data):
            card = self._create_stat_card(self.stats_cards_frame, title, value)
            card.grid(row=i//3, column=i%3, padx=10, pady=10, sticky="ew")
        
        # Configurar grid
        self.stats_cards_frame.grid_columnconfigure((0, 1, 2), weight=1)
    
    def _create_stat_card(self, parent, title: str, value: int):
        """Crear tarjeta de estadística"""
        card = ctk.CTkFrame(parent, corner_radius=10)
        
        # Valor
        value_label = ctk.CTkLabel(
            card,
            text=str(value),
            font=("Arial", 20, "bold"),
            text_color=self.settings.colors["accent"]
        )
        value_label.pack(pady=(15, 5))
        
        # Título
        title_label = ctk.CTkLabel(
            card,
            text=title,
            font=("Arial", 12),
            text_color=self.settings.colors["text_secondary"]
        )
        title_label.pack(pady=(0, 15))
        
        return card
    
    def _view_playlist(self, playlist_id: int):
        """Ver playlist"""
        # Implementar vista de playlist
        pass
    
    def _delete_playlist(self, playlist_id: int):
        """Eliminar playlist"""
        # Implementar eliminación de playlist
        pass
    
    def _delete_duplicates(self, duplicate: Dict):
        """Eliminar duplicados"""
        # Implementar eliminación de duplicados
        pass
    
    def destroy(self):
        """Destruir componente"""
        if hasattr(self, 'main_frame'):
            self.main_frame.destroy()
