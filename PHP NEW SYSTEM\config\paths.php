<?php
/**
 * Path Configuration for IPTV XUI One Content Manager
 * ==================================================
 * 
 * Centralized path management for different hosting environments
 */

/**
 * Get the base path of the project
 */
function getProjectBasePath() {
    // Try to determine the base path from the current file location
    $currentDir = __DIR__;
    $projectRoot = dirname($currentDir);
    
    // If we're being called from a file in the public directory
    if (basename(dirname($_SERVER['SCRIPT_FILENAME'] ?? '')) === 'public') {
        return dirname(dirname($_SERVER['SCRIPT_FILENAME']));
    }
    
    // If we're being called from the root index.php
    if (basename($_SERVER['SCRIPT_FILENAME'] ?? '') === 'index.php' && 
        strpos($_SERVER['SCRIPT_FILENAME'] ?? '', 'public') === false) {
        return dirname($_SERVER['SCRIPT_FILENAME']);
    }
    
    // Default to the parent directory of config
    return $projectRoot;
}

/**
 * Get path relative to project root
 */
function getProjectPath($relativePath = '') {
    $basePath = getProjectBasePath();
    return $basePath . '/' . ltrim($relativePath, '/');
}

/**
 * Check if a path exists relative to project root
 */
function projectPathExists($relativePath) {
    return file_exists(getProjectPath($relativePath));
}

/**
 * Get the correct include path for configuration files
 */
function getConfigPath($configFile) {
    $basePath = getProjectBasePath();
    return $basePath . '/config/' . $configFile;
}

/**
 * Get the correct include path for core files
 */
function getCorePath($coreFile) {
    $basePath = getProjectBasePath();
    return $basePath . '/core/' . $coreFile;
}

/**
 * Get the correct include path for public files
 */
function getPublicPath($publicFile = '') {
    $basePath = getProjectBasePath();
    return $basePath . '/public/' . ltrim($publicFile, '/');
}

/**
 * Get the correct include path for API files
 */
function getApiPath($apiFile) {
    $basePath = getProjectBasePath();
    return $basePath . '/api/' . $apiFile;
}

/**
 * Debug function to show all paths
 */
function debugPaths() {
    return [
        'current_dir' => __DIR__,
        'script_filename' => $_SERVER['SCRIPT_FILENAME'] ?? 'not set',
        'script_name' => $_SERVER['SCRIPT_NAME'] ?? 'not set',
        'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? 'not set',
        'project_base_path' => getProjectBasePath(),
        'config_path' => getConfigPath(''),
        'core_path' => getCorePath(''),
        'public_path' => getPublicPath(''),
        'api_path' => getApiPath('')
    ];
}

// Define constants for easy access
if (!defined('PROJECT_ROOT')) {
    define('PROJECT_ROOT', getProjectBasePath());
}

if (!defined('CONFIG_PATH')) {
    define('CONFIG_PATH', getConfigPath(''));
}

if (!defined('CORE_PATH')) {
    define('CORE_PATH', getCorePath(''));
}

if (!defined('PUBLIC_PATH')) {
    define('PUBLIC_PATH', getPublicPath(''));
}

if (!defined('API_PATH')) {
    define('API_PATH', getApiPath(''));
}
