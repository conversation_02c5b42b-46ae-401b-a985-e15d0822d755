"""
Utilidades para trabajar con datos XUI One
==========================================

Funciones auxiliares para parsear y formatear datos de la base de datos XUI One.
"""

import json
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

logger = logging.getLogger("iptv_manager.xui_utils")

def parse_movie_properties(movie_properties: str) -> Dict[str, Any]:
    """
    Parsear el JSON de movie_properties desde la base de datos XUI One
    
    Args:
        movie_properties: String JSON con propiedades de la película
        
    Returns:
        Dict con las propiedades parseadas
    """
    if not movie_properties:
        return {}
    
    try:
        # Intentar parsear el JSON
        data = json.loads(movie_properties)
        
        # Normalizar campos comunes
        normalized = {
            'tmdb_id': data.get('tmdb_id', 0),
            'title': data.get('name', ''),
            'original_title': data.get('o_name', ''),
            'plot': data.get('plot', data.get('description', '')),
            'genre': data.get('genre', ''),
            'director': data.get('director', ''),
            'cast': data.get('cast', data.get('actors', '')),
            'year': data.get('release_date', '').split('-')[0] if data.get('release_date') else '',
            'rating': float(data.get('rating', 0)) if data.get('rating') else 0.0,
            'runtime': data.get('episode_run_time', 0),
            'poster_url': data.get('movie_image', data.get('cover_big', '')),
            'backdrop_url': data.get('backdrop_path', [''])[0] if data.get('backdrop_path') else '',
            'trailer_url': data.get('youtube_trailer', ''),
            'country': data.get('country', ''),
            'release_date': data.get('release_date', ''),
            'duration': data.get('duration', ''),
            'age_rating': data.get('mpaa_rating', ''),
            'kinopoisk_url': data.get('kinopoisk_url', ''),
            'similar': data.get('similar', {})
        }
        
        return normalized
        
    except (json.JSONDecodeError, Exception) as e:
        logger.error(f"Error al parsear movie_properties: {str(e)}")
        return {}

def format_movie_properties(movie_data: Dict[str, Any]) -> str:
    """
    Formatear datos de película a JSON para almacenar en movie_properties
    
    Args:
        movie_data: Dict con datos de la película
        
    Returns:
        String JSON formateado
    """
    try:
        # Estructura para XUI One
        xui_data = {
            'tmdb_id': movie_data.get('tmdb_id', 0),
            'name': movie_data.get('title', ''),
            'o_name': movie_data.get('original_title', ''),
            'plot': movie_data.get('plot', movie_data.get('overview', '')),
            'description': movie_data.get('plot', movie_data.get('overview', '')),
            'genre': movie_data.get('genre', ''),
            'director': movie_data.get('director', ''),
            'cast': movie_data.get('cast', ''),
            'actors': movie_data.get('cast', ''),
            'release_date': movie_data.get('release_date', ''),
            'rating': movie_data.get('rating', 0),
            'episode_run_time': movie_data.get('runtime', 0),
            'movie_image': movie_data.get('poster_url', ''),
            'cover_big': movie_data.get('poster_url', ''),
            'backdrop_path': [movie_data.get('backdrop_url', '')] if movie_data.get('backdrop_url') else [],
            'youtube_trailer': movie_data.get('trailer_url', ''),
            'country': movie_data.get('country', ''),
            'duration': movie_data.get('duration', ''),
            'mpaa_rating': movie_data.get('age_rating', ''),
            'kinopoisk_url': movie_data.get('kinopoisk_url', ''),
            'age': movie_data.get('age_rating', ''),
            'rating_count_kinopoisk': 0,
            'duration_secs': movie_data.get('runtime', 0) * 60 if movie_data.get('runtime') else 0,
            'video': [],
            'audio': [],
            'bitrate': 0
        }
        
        return json.dumps(xui_data, ensure_ascii=False)
        
    except Exception as e:
        logger.error(f"Error al formatear movie_properties: {str(e)}")
        return '{}'

def parse_category_ids(category_id: str) -> List[int]:
    """
    Parsear los IDs de categorías desde el formato XUI One
    
    Args:
        category_id: String con IDs de categorías en formato [1,2,3] o JSON
        
    Returns:
        Lista de IDs de categorías
    """
    if not category_id:
        return []
    
    try:
        # Intentar parsear como JSON
        if category_id.startswith('[') and category_id.endswith(']'):
            ids = json.loads(category_id)
            return [int(id) for id in ids if str(id).isdigit()]
        
        # Intentar parsear como string separado por comas
        if ',' in category_id:
            return [int(id.strip()) for id in category_id.split(',') if id.strip().isdigit()]
        
        # Un solo ID
        if category_id.isdigit():
            return [int(category_id)]
        
        return []
        
    except (json.JSONDecodeError, ValueError, Exception) as e:
        logger.error(f"Error al parsear category_ids: {str(e)}")
        return []

def format_category_ids(category_ids: List[int]) -> str:
    """
    Formatear lista de IDs de categorías al formato XUI One
    
    Args:
        category_ids: Lista de IDs de categorías
        
    Returns:
        String formateado para XUI One
    """
    if not category_ids:
        return '[]'
    
    try:
        return json.dumps(category_ids)
    except Exception as e:
        logger.error(f"Error al formatear category_ids: {str(e)}")
        return '[]'

def parse_stream_source(stream_source: str) -> List[str]:
    """
    Parsear URLs de stream desde el formato XUI One
    
    Args:
        stream_source: String con URLs en formato JSON
        
    Returns:
        Lista de URLs de stream
    """
    if not stream_source:
        return []
    
    try:
        # Intentar parsear como JSON
        if stream_source.startswith('[') and stream_source.endswith(']'):
            sources = json.loads(stream_source)
            return [url for url in sources if url]
        
        # URL simple
        if stream_source.startswith('http'):
            return [stream_source]
        
        return []
        
    except (json.JSONDecodeError, Exception) as e:
        logger.error(f"Error al parsear stream_source: {str(e)}")
        return []

def format_stream_source(urls: List[str]) -> str:
    """
    Formatear URLs de stream al formato XUI One
    
    Args:
        urls: Lista de URLs de stream
        
    Returns:
        String JSON formateado
    """
    if not urls:
        return '[]'
    
    try:
        return json.dumps(urls)
    except Exception as e:
        logger.error(f"Error al formatear stream_source: {str(e)}")
        return '[]'

def get_stream_type_name(type_id: int) -> str:
    """
    Obtener nombre del tipo de stream
    
    Args:
        type_id: ID del tipo de stream
        
    Returns:
        Nombre del tipo de stream
    """
    type_names = {
        1: 'Live TV',
        2: 'Movie',
        3: 'Series',
        4: 'Radio'
    }
    return type_names.get(type_id, 'Unknown')

def is_movie_stream(stream_data: Dict[str, Any]) -> bool:
    """
    Verificar si un stream es una película
    
    Args:
        stream_data: Datos del stream
        
    Returns:
        True si es una película
    """
    return stream_data.get('type') == 2

def is_series_stream(stream_data: Dict[str, Any]) -> bool:
    """
    Verificar si un stream es una serie
    
    Args:
        stream_data: Datos del stream
        
    Returns:
        True si es una serie
    """
    return stream_data.get('type') == 3

def is_live_stream(stream_data: Dict[str, Any]) -> bool:
    """
    Verificar si un stream es TV en vivo
    
    Args:
        stream_data: Datos del stream
        
    Returns:
        True si es TV en vivo
    """
    return stream_data.get('type') == 1

def format_timestamp(timestamp: int) -> str:
    """
    Formatear timestamp de XUI One a fecha legible
    
    Args:
        timestamp: Timestamp en formato Unix
        
    Returns:
        Fecha formateada
    """
    if not timestamp:
        return ''
    
    try:
        dt = datetime.fromtimestamp(timestamp)
        return dt.strftime('%Y-%m-%d %H:%M:%S')
    except (ValueError, OSError) as e:
        logger.error(f"Error al formatear timestamp: {str(e)}")
        return ''

def get_content_quality_score(stream_data: Dict[str, Any]) -> int:
    """
    Calcular puntuación de calidad del contenido
    
    Args:
        stream_data: Datos del stream
        
    Returns:
        Puntuación de 0-100
    """
    score = 0
    
    # Verificar si tiene TMDB ID
    if stream_data.get('tmdb_id', 0) > 0:
        score += 30
    
    # Verificar si tiene movie_properties
    if stream_data.get('movie_properties'):
        score += 20
        
        # Parsear propiedades
        props = parse_movie_properties(stream_data['movie_properties'])
        
        # Verificar campos específicos
        if props.get('plot'):
            score += 10
        if props.get('genre'):
            score += 10
        if props.get('cast'):
            score += 10
        if props.get('poster_url'):
            score += 10
        if props.get('rating', 0) > 0:
            score += 10
    
    return min(score, 100)
