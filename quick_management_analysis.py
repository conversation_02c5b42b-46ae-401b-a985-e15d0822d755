"""
An<PERSON><PERSON>is Rápido de Gestión
==========================

Consultas SQL directas y rápidas para gestión de contenido.
"""

import asyncio
import sys
import os

# Agregar el directorio raíz al path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.settings import Settings
from core.database_manager import DatabaseManager

async def quick_management_analysis():
    """Análisis rápido de gestión"""
    print("⚡ ANÁLISIS RÁPIDO DE GESTIÓN")
    print("=" * 50)
    
    try:
        # Inicializar componentes
        settings = Settings()
        db_manager = DatabaseManager(settings)
        
        # Probar conexión
        print("1. Probando conexión...")
        if not await db_manager.test_connection():
            print("❌ Error de conexión")
            return
        print("✅ Conexión exitosa\n")
        
        # ========================================
        # CONSULTA 1: RESUMEN GENERAL
        # ========================================
        print("📊 CONSULTA 1: RESUMEN GENERAL")
        print("-" * 40)
        
        query1 = """
        SELECT 
            'Symlink Total' as tipo,
            COUNT(*) as cantidad
        FROM streams WHERE type = 2 AND movie_symlink = 1
        UNION ALL
        SELECT 
            'Direct_source Total' as tipo,
            COUNT(*) as cantidad
        FROM streams WHERE type = 2 AND direct_source = 1
        UNION ALL
        SELECT 
            'Symlink CAM/TS' as tipo,
            COUNT(*) as cantidad
        FROM streams 
        WHERE type = 2 AND movie_symlink = 1
        AND (stream_display_name LIKE '%CAM%' OR stream_display_name LIKE '%TS%')
        UNION ALL
        SELECT 
            'Symlink 4K' as tipo,
            COUNT(*) as cantidad
        FROM streams 
        WHERE type = 2 AND movie_symlink = 1
        AND (stream_display_name LIKE '%4K%' OR stream_display_name LIKE '%2160p%')
        """
        
        result1 = await db_manager.execute_query(query1)
        for row in result1:
            print(f"   {row['tipo']}: {row['cantidad']:,}")
        
        # ========================================
        # CONSULTA 2: DIRECT_SOURCE DUPLICADOS SIMPLES
        # ========================================
        print("\n📁 CONSULTA 2: DIRECT_SOURCE DUPLICADOS (TOP 10)")
        print("-" * 40)
        
        query2 = """
        SELECT 
            stream_display_name,
            COUNT(*) as copias
        FROM streams 
        WHERE type = 2 AND direct_source = 1
        GROUP BY stream_display_name
        HAVING COUNT(*) > 1
        ORDER BY COUNT(*) DESC
        LIMIT 10
        """
        
        result2 = await db_manager.execute_query(query2)
        for i, row in enumerate(result2, 1):
            print(f"   {i:2d}. '{row['stream_display_name'][:45]}' - {row['copias']} copias")
        
        # ========================================
        # CONSULTA 3: SYMLINK BAJA CALIDAD
        # ========================================
        print("\n🔗 CONSULTA 3: SYMLINK BAJA CALIDAD (TOP 10)")
        print("-" * 40)
        
        query3 = """
        SELECT 
            id,
            stream_display_name,
            tmdb_id,
            year
        FROM streams 
        WHERE type = 2 AND movie_symlink = 1
        AND (
            stream_display_name LIKE '%CAM%' OR 
            stream_display_name LIKE '%TS%' OR
            stream_display_name LIKE '%480p%' OR
            stream_display_name LIKE '%SCREENER%'
        )
        ORDER BY added DESC
        LIMIT 10
        """
        
        result3 = await db_manager.execute_query(query3)
        for i, row in enumerate(result3, 1):
            print(f"   {i:2d}. ID {row['id']} - {row['stream_display_name'][:40]}")
            print(f"       TMDB: {row['tmdb_id']} | Año: {row['year']}")
        
        # ========================================
        # CONSULTA 4: TÍTULOS CON MÚLTIPLES VERSIONES
        # ========================================
        print("\n🔄 CONSULTA 4: TÍTULOS CON MÚLTIPLES VERSIONES (TOP 10)")
        print("-" * 40)
        
        query4 = """
        SELECT 
            stream_display_name,
            COUNT(*) as total_versiones,
            SUM(CASE WHEN movie_symlink = 1 THEN 1 ELSE 0 END) as symlink_count,
            SUM(CASE WHEN direct_source = 1 THEN 1 ELSE 0 END) as direct_count
        FROM streams 
        WHERE type = 2
        GROUP BY stream_display_name
        HAVING COUNT(*) > 1
        ORDER BY COUNT(*) DESC
        LIMIT 10
        """
        
        result4 = await db_manager.execute_query(query4)
        for i, row in enumerate(result4, 1):
            print(f"   {i:2d}. '{row['stream_display_name'][:40]}'")
            print(f"       Total: {row['total_versiones']} | Symlink: {row['symlink_count']} | Direct: {row['direct_count']}")
        
        # ========================================
        # CONSULTA 5: CALIDADES EN SYMLINK
        # ========================================
        print("\n🎬 CONSULTA 5: DISTRIBUCIÓN DE CALIDADES EN SYMLINK")
        print("-" * 40)
        
        query5 = """
        SELECT 
            CASE 
                WHEN stream_display_name LIKE '%4K%' OR stream_display_name LIKE '%2160p%' THEN '4K'
                WHEN stream_display_name LIKE '%HDR%' THEN 'HDR'
                WHEN stream_display_name LIKE '%60fps%' OR stream_display_name LIKE '%60FPS%' THEN '60fps'
                WHEN stream_display_name LIKE '%1080p%' OR stream_display_name LIKE '%FHD%' THEN '1080p'
                WHEN stream_display_name LIKE '%720p%' OR stream_display_name LIKE '%HD%' THEN '720p'
                WHEN stream_display_name LIKE '%480p%' THEN '480p'
                WHEN stream_display_name LIKE '%CAM%' OR stream_display_name LIKE '%TS%' THEN 'CAM/TS'
                ELSE 'Sin especificar'
            END as calidad,
            COUNT(*) as cantidad
        FROM streams 
        WHERE type = 2 AND movie_symlink = 1
        GROUP BY 
            CASE 
                WHEN stream_display_name LIKE '%4K%' OR stream_display_name LIKE '%2160p%' THEN '4K'
                WHEN stream_display_name LIKE '%HDR%' THEN 'HDR'
                WHEN stream_display_name LIKE '%60fps%' OR stream_display_name LIKE '%60FPS%' THEN '60fps'
                WHEN stream_display_name LIKE '%1080p%' OR stream_display_name LIKE '%FHD%' THEN '1080p'
                WHEN stream_display_name LIKE '%720p%' OR stream_display_name LIKE '%HD%' THEN '720p'
                WHEN stream_display_name LIKE '%480p%' THEN '480p'
                WHEN stream_display_name LIKE '%CAM%' OR stream_display_name LIKE '%TS%' THEN 'CAM/TS'
                ELSE 'Sin especificar'
            END
        ORDER BY cantidad DESC
        """
        
        result5 = await db_manager.execute_query(query5)
        total_symlink = sum(row['cantidad'] for row in result5)
        
        for row in result5:
            percentage = (row['cantidad'] / total_symlink * 100) if total_symlink > 0 else 0
            print(f"   {row['calidad']}: {row['cantidad']:,} ({percentage:.1f}%)")
        
        # ========================================
        # RECOMENDACIONES ESPECÍFICAS
        # ========================================
        print("\n💡 RECOMENDACIONES ESPECÍFICAS")
        print("-" * 40)
        
        print("   🔗 SYMLINK (VIVE):")
        print("      - Revisar CAM/TS para posible borrado manual")
        print("      - Mantener 4K, HDR, 60fps como máxima prioridad")
        print("      - Evaluar duplicados symlink caso por caso")
        
        print("   📁 DIRECT_SOURCE (PURGAR):")
        print("      - Eliminar duplicados manteniendo mejor calidad")
        print("      - Verificar que existe versión symlink antes de borrar")
        print("      - Mantener al menos una copia por título")
        
        print("   ⚠️ SEGURIDAD:")
        print("      - NUNCA borrar automáticamente")
        print("      - Generar listas de candidatos para revisión manual")
        print("      - Confirmar cada borrado individualmente")
        
        print("   🎯 PRIORIDADES DE BORRADO:")
        print("      1. Direct_source duplicados (menor calidad)")
        print("      2. Symlink CAM/TS (solo si hay versión HD+)")
        print("      3. Symlink 480p (solo si hay versión HD+)")
        print("      4. NUNCA borrar 4K, HDR, 60fps")
        
        print("\n✅ Análisis rápido completado")
        
    except Exception as e:
        print(f"\n❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        if 'db_manager' in locals():
            await db_manager.close()
    
    return True

if __name__ == "__main__":
    success = asyncio.run(quick_management_analysis())
    if success:
        print("\n🚀 Análisis rápido exitoso")
        print("   - Identificados candidatos de gestión")
        print("   - Establecidas prioridades claras")
        print("   - Listo para borrado manual seleccionable")
    else:
        print("\n💥 Error en el análisis")
        sys.exit(1)
