<?php
/**
 * Servidor de Desarrollo Local
 * ===========================
 * 
 * Script para iniciar servidor PHP local fácilmente
 */

echo "🎬 IPTV XUI One Content Manager - Servidor Local\n";
echo "===============================================\n\n";

$port = 8000;
$host = 'localhost';
$docroot = __DIR__;

echo "🚀 Iniciando servidor PHP en:\n";
echo "   URL: http://{$host}:{$port}/start.php\n";
echo "   Directorio: {$docroot}\n\n";

echo "📋 URLs disponibles:\n";
echo "   🏠 Inicio:        http://{$host}:{$port}/start.php\n";
echo "   🔬 Test:          http://{$host}:{$port}/system-test.php\n";
echo "   📊 Dashboard:     http://{$host}:{$port}/public/\n";
echo "   🎬 Contenido:     http://{$host}:{$port}/public/?page=content-browser\n";
echo "   📁 M3U Manager:   http://{$host}:{$port}/public/?page=m3u-manager\n\n";

echo "💡 Para detener el servidor, presiona Ctrl+C\n\n";
echo "🌐 Abriendo navegador...\n";

// Intentar abrir el navegador
$url = "http://{$host}:{$port}/start.php";
if (PHP_OS_FAMILY === 'Windows') {
    exec("start {$url}");
} elseif (PHP_OS_FAMILY === 'Darwin') {
    exec("open {$url}");
} else {
    exec("xdg-open {$url}");
}

// Dar tiempo para que se abra el navegador
sleep(2);

echo "🔄 Iniciando servidor...\n\n";

// Iniciar servidor PHP
$command = "php -S {$host}:{$port} -t {$docroot}";
passthru($command);
?>
