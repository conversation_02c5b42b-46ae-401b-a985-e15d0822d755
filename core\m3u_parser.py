"""
Parser M3U
==========

Parsea archivos M3U y M3U8 para extraer información de canales y contenido IPTV.
Incluye detección de duplicados y categorización automática.
"""

import re
import asyncio
import logging
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
from urllib.parse import urlparse, parse_qs
from dataclasses import dataclass, field
import aiofiles

@dataclass
class M3UEntry:
    """Entrada de M3U con toda la información extraída"""
    title: str
    url: str
    duration: int = -1
    logo: str = ""
    group_title: str = ""
    tvg_id: str = ""
    tvg_name: str = ""
    tvg_logo: str = ""
    tvg_chno: str = ""
    tvg_shift: str = ""
    radio: bool = False
    content_type: str = "other"
    quality: str = ""
    language: str = ""
    raw_line: str = ""
    attributes: Dict[str, str] = field(default_factory=dict)

class M3UParser:
    """Parser para archivos M3U/M3U8"""
    
    def __init__(self, db_manager=None):
        self.db_manager = db_manager
        self.logger = logging.getLogger("iptv_manager.m3u_parser")
        
        # Patrones para identificar contenido
        self.movie_patterns = [
            r'(19|20)\d{2}',  # Años
            r'\b(HD|4K|1080p|720p|480p)\b',  # Calidad
            r'\b(BluRay|DVDRip|WEBRip|BRRip)\b',  # Fuente
            r'\b(SPANISH|LATINO|ENGLISH|SUBTITULADO)\b',  # Idioma
        ]
        
        self.series_patterns = [
            r'S\d{2}E\d{2}',  # Formato SxxExx
            r'Season\s+\d+',  # Season X
            r'Temporada\s+\d+',  # Temporada X
            r'Cap\.\s*\d+',  # Cap. X
            r'Episode\s+\d+',  # Episode X
        ]
        
        self.live_patterns = [
            r'\b(LIVE|EN VIVO|DIRECTO)\b',
            r'\b(NEWS|NOTICIAS|SPORTS|DEPORTES)\b',
            r'\b(24/7|24H)\b',
        ]
    
    async def parse_file(self, file_path: str, playlist_name: str | None = None) -> Tuple[List[M3UEntry], Dict[str, Any]]:
        """
        Parsear archivo M3U
        
        Args:
            file_path: Ruta del archivo M3U
            playlist_name: Nombre de la playlist (opcional)
            
        Returns:
            Tuple con lista de entradas y estadísticas
        """
        try:
            path = Path(file_path)
            if not path.exists():
                raise FileNotFoundError(f"Archivo no encontrado: {file_path}")
            
            self.logger.info(f"Parseando archivo M3U: {file_path}")
            
            # Leer archivo
            async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
                content = await f.read()
            
            # Parsear contenido
            entries = await self._parse_content(content)
            
            # Categorizar contenido
            for entry in entries:
                entry.content_type = self._detect_content_type(entry.title, entry.group_title)
                entry.quality = self._extract_quality(entry.title)
                entry.language = self._extract_language(entry.title)
            
            # Detectar duplicados
            duplicates = self._detect_duplicates(entries)
            
            # Generar estadísticas
            stats = self._generate_stats(entries, duplicates)
            stats['file_path'] = file_path
            stats['playlist_name'] = playlist_name or path.stem
            
            self.logger.info(f"Parseado completado: {len(entries)} entradas, {len(duplicates)} duplicados")
            
            return entries, stats
            
        except Exception as e:
            self.logger.error(f"Error al parsear archivo M3U: {str(e)}")
            raise
    
    async def _parse_content(self, content: str) -> List[M3UEntry]:
        """Parsear contenido del archivo M3U"""
        entries = []
        lines = content.strip().split('\n')
        
        i = 0
        while i < len(lines):
            line = lines[i].strip()
            
            # Buscar línea EXTINF
            if line.startswith('#EXTINF:'):
                try:
                    # Obtener URL (siguiente línea no vacía que no sea comentario)
                    url = ""
                    j = i + 1
                    while j < len(lines):
                        next_line = lines[j].strip()
                        if next_line and not next_line.startswith('#'):
                            url = next_line
                            break
                        j += 1
                    
                    if url:
                        entry = self._parse_extinf_line(line, url)
                        if entry:
                            entries.append(entry)
                    
                    i = j
                except Exception as e:
                    self.logger.warning(f"Error al parsear entrada en línea {i}: {str(e)}")
                    i += 1
            else:
                i += 1
        
        return entries
    
    def _parse_extinf_line(self, extinf_line: str, url: str) -> Optional[M3UEntry]:
        """Parsear línea EXTINF"""
        try:
            # Extraer duración
            duration_match = re.search(r'#EXTINF:\s*(-?\d+)', extinf_line)
            duration = int(duration_match.group(1)) if duration_match else -1
            
            # Extraer atributos
            attributes = self._extract_attributes(extinf_line)
            
            # Extraer título (después de la coma)
            title_match = re.search(r',(.+)$', extinf_line)
            title = title_match.group(1).strip() if title_match else ""
            
            # Crear entrada
            entry = M3UEntry(
                title=title,
                url=url,
                duration=duration,
                raw_line=extinf_line,
                attributes=attributes
            )
            
            # Asignar atributos conocidos
            entry.logo = attributes.get('tvg-logo', '')
            entry.group_title = attributes.get('group-title', '')
            entry.tvg_id = attributes.get('tvg-id', '')
            entry.tvg_name = attributes.get('tvg-name', '')
            entry.tvg_logo = attributes.get('tvg-logo', '')
            entry.tvg_chno = attributes.get('tvg-chno', '')
            entry.tvg_shift = attributes.get('tvg-shift', '')
            entry.radio = attributes.get('radio', '').lower() == 'true'
            
            return entry
            
        except Exception as e:
            self.logger.warning(f"Error al parsear línea EXTINF: {str(e)}")
            return None
    
    def _extract_attributes(self, extinf_line: str) -> Dict[str, str]:
        """Extraer atributos de la línea EXTINF"""
        attributes = {}
        
        # Buscar atributos en formato key="value" o key=value
        pattern = r'(\w+(?:-\w+)*)=(["\']?)([^"\']*?)\2(?=\s|$|,)'
        matches = re.findall(pattern, extinf_line)
        
        for match in matches:
            key, _, value = match
            attributes[key.lower()] = value
        
        return attributes
    
    def _detect_content_type(self, title: str, group_title: str) -> str:
        """Detectar tipo de contenido basado en título y grupo"""
        title_lower = title.lower()
        group_lower = group_title.lower()
        combined = f"{title_lower} {group_lower}"
        
        # Verificar si es contenido en vivo
        for pattern in self.live_patterns:
            if re.search(pattern, combined, re.IGNORECASE):
                return "live"
        
        # Verificar si es serie
        for pattern in self.series_patterns:
            if re.search(pattern, combined, re.IGNORECASE):
                return "series"
        
        # Verificar si es película
        for pattern in self.movie_patterns:
            if re.search(pattern, combined, re.IGNORECASE):
                return "movie"
        
        # Categorías comunes por grupo
        if any(keyword in group_lower for keyword in ['movie', 'pelicula', 'film', 'cine']):
            return "movie"
        elif any(keyword in group_lower for keyword in ['serie', 'tv show', 'series']):
            return "series"
        elif any(keyword in group_lower for keyword in ['live', 'vivo', 'directo', 'canal']):
            return "live"
        
        return "other"
    
    def _extract_quality(self, title: str) -> str:
        """Extraer calidad del título"""
        quality_patterns = {
            '4K': r'\b4K\b',
            '1080p': r'\b1080p?\b',
            '720p': r'\b720p?\b',
            '480p': r'\b480p?\b',
            'HD': r'\bHD\b',
            'SD': r'\bSD\b'
        }
        
        for quality, pattern in quality_patterns.items():
            if re.search(pattern, title, re.IGNORECASE):
                return quality
        
        return ""
    
    def _extract_language(self, title: str) -> str:
        """Extraer idioma del título"""
        language_patterns = {
            'es': r'\b(SPANISH|ESPAÑOL|CASTELLANO|LATINO)\b',
            'en': r'\b(ENGLISH|INGLÉS|INGLES)\b',
            'fr': r'\b(FRENCH|FRANCÉS|FRANCES)\b',
            'pt': r'\b(PORTUGUESE|PORTUGUÉS|PORTUGUES)\b',
            'it': r'\b(ITALIAN|ITALIANO)\b',
            'de': r'\b(GERMAN|ALEMÁN|ALEMAN)\b'
        }
        
        for lang_code, pattern in language_patterns.items():
            if re.search(pattern, title, re.IGNORECASE):
                return lang_code
        
        return ""
    
    def _detect_duplicates(self, entries: List[M3UEntry]) -> List[Dict[str, Any]]:
        """Detectar entradas duplicadas"""
        title_groups = {}
        
        # Agrupar por título normalizado
        for i, entry in enumerate(entries):
            normalized_title = self._normalize_title(entry.title)
            if normalized_title not in title_groups:
                title_groups[normalized_title] = []
            title_groups[normalized_title].append({
                'index': i,
                'entry': entry
            })
        
        # Encontrar duplicados
        duplicates = []
        for title, group in title_groups.items():
            if len(group) > 1:
                duplicates.append({
                    'title': title,
                    'count': len(group),
                    'entries': group
                })
        
        return duplicates
    
    def _normalize_title(self, title: str) -> str:
        """Normalizar título para detección de duplicados"""
        # Convertir a minúsculas
        normalized = title.lower()
        
        # Remover año
        normalized = re.sub(r'\(\d{4}\)', '', normalized)
        
        # Remover calidad y formato
        normalized = re.sub(r'\b(hd|4k|1080p|720p|480p|bluray|dvdrip|webrip|brrip)\b', '', normalized)
        
        # Remover idioma
        normalized = re.sub(r'\b(spanish|latino|english|subtitulado|dubbed)\b', '', normalized)
        
        # Remover caracteres especiales
        normalized = re.sub(r'[^\w\s]', ' ', normalized)
        
        # Normalizar espacios
        normalized = ' '.join(normalized.split())
        
        return normalized.strip()
    
    def _generate_stats(self, entries: List[M3UEntry], duplicates: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generar estadísticas del parsing"""
        stats = {
            'total_entries': len(entries),
            'duplicates_count': len(duplicates),
            'content_types': {},
            'quality_distribution': {},
            'language_distribution': {},
            'group_distribution': {}
        }
        
        # Estadísticas por tipo de contenido
        for entry in entries:
            content_type = entry.content_type
            stats['content_types'][content_type] = stats['content_types'].get(content_type, 0) + 1
            
            # Calidad
            if entry.quality:
                stats['quality_distribution'][entry.quality] = stats['quality_distribution'].get(entry.quality, 0) + 1
            
            # Idioma
            if entry.language:
                stats['language_distribution'][entry.language] = stats['language_distribution'].get(entry.language, 0) + 1
            
            # Grupo
            if entry.group_title:
                stats['group_distribution'][entry.group_title] = stats['group_distribution'].get(entry.group_title, 0) + 1
        
        return stats
    
    async def save_to_database(self, entries: List[M3UEntry], playlist_name: str) -> int:
        """Guardar entradas en base de datos"""
        if not self.db_manager:
            raise RuntimeError("Database manager no configurado")
        
        try:
            # Crear playlist
            playlist_id = await self.db_manager.create_playlist(
                name=playlist_name,
                description=f"Playlist importada con {len(entries)} entradas"
            )
            
            # Insertar entradas
            saved_count = 0
            for entry in entries:
                entry_data = {
                    'playlist_id': playlist_id,
                    'title': entry.title,
                    'url': entry.url,
                    'logo': entry.logo,
                    'group_title': entry.group_title,
                    'tvg_id': entry.tvg_id,
                    'tvg_name': entry.tvg_name,
                    'tvg_logo': entry.tvg_logo,
                    'content_type': entry.content_type,
                    'quality': entry.quality,
                    'language': entry.language
                }
                
                await self.db_manager.insert_m3u_entry(entry_data)
                saved_count += 1
            
            self.logger.info(f"Guardadas {saved_count} entradas en base de datos")
            return playlist_id
            
        except Exception as e:
            self.logger.error(f"Error al guardar en base de datos: {str(e)}")
            raise
    
    async def process_file_async(self, file_path: str, playlist_name: str | None = None, 
                                progress_callback=None) -> Dict[str, Any]:
        """
        Procesar archivo M3U de forma asíncrona con callback de progreso
        
        Args:
            file_path: Ruta del archivo
            playlist_name: Nombre de la playlist
            progress_callback: Función para actualizar progreso
            
        Returns:
            Diccionario con resultados del procesamiento
        """
        try:
            # Parsear archivo
            if progress_callback:
                progress_callback(0.1, "Leyendo archivo...")
            
            entries, stats = await self.parse_file(file_path, playlist_name)
            
            if progress_callback:
                progress_callback(0.5, "Guardando en base de datos...")
            
            # Guardar en base de datos
            playlist_id = await self.save_to_database(entries, stats['playlist_name'])
            
            if progress_callback:
                progress_callback(1.0, "Procesamiento completado")
            
            return {
                'success': True,
                'playlist_id': playlist_id,
                'stats': stats,
                'entries_count': len(entries)
            }
            
        except Exception as e:
            if progress_callback:
                progress_callback(1.0, f"Error: {str(e)}")
            
            return {
                'success': False,
                'error': str(e)
            }
        
    async def remove_duplicates(self, playlist_id: int, strategy: str = "url") -> Dict[str, Any]:
        """
        Eliminar duplicados de una playlist
        
        Args:
            playlist_id: ID de la playlist
            strategy: Estrategia de detección ('url', 'title', 'hash')
        
        Returns:
            Diccionario con estadísticas de eliminación
        """
        try:
            # Obtener entradas de la playlist
            entries = await self.db_manager.execute_query(
                "SELECT * FROM m3u_entries WHERE playlist_id = %s",
                (playlist_id,)
            )
            
            if not entries:
                return {
                    'success': True,
                    'removed_count': 0,
                    'total_count': 0,
                    'strategy': strategy
                }
            
            # Detectar duplicados según estrategia
            duplicates = []
            seen = set()
            
            for entry in entries:
                if strategy == "url":
                    key = entry['url']
                elif strategy == "title":
                    key = entry['title'].lower().strip()
                elif strategy == "hash":
                    key = self._generate_entry_hash(entry)
                else:
                    key = entry['url']  # fallback
                
                if key in seen:
                    duplicates.append(entry['id'])
                else:
                    seen.add(key)
            
            # Eliminar duplicados
            if duplicates:
                placeholders = ','.join(['%s'] * len(duplicates))
                await self.db_manager.execute_update(
                    f"DELETE FROM m3u_entries WHERE id IN ({placeholders})",
                    duplicates
                )
            
            # Actualizar contador de entradas en playlist
            remaining_count = len(entries) - len(duplicates)
            await self.db_manager.execute_update(
                "UPDATE playlists SET entry_count = %s WHERE id = %s",
                (remaining_count, playlist_id)
            )
            
            return {
                'success': True,
                'removed_count': len(duplicates),
                'total_count': len(entries),
                'remaining_count': remaining_count,
                'strategy': strategy
            }
            
        except Exception as e:
            self.logger.error(f"Error al eliminar duplicados: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def delete_playlist(self, playlist_id: int) -> Dict[str, Any]:
        """
        Eliminar playlist completa
        
        Args:
            playlist_id: ID de la playlist a eliminar
        
        Returns:
            Diccionario con resultado de la operación
        """
        try:
            # Obtener información de la playlist
            playlist = await self.db_manager.execute_query(
                "SELECT * FROM playlists WHERE id = %s",
                (playlist_id,)
            )
            
            if not playlist:
                return {
                    'success': False,
                    'error': 'Playlist no encontrada'
                }
            
            # Eliminar entradas de la playlist
            await self.db_manager.execute_update(
                "DELETE FROM m3u_entries WHERE playlist_id = %s",
                (playlist_id,)
            )
            
            # Eliminar la playlist
            await self.db_manager.execute_update(
                "DELETE FROM playlists WHERE id = %s",
                (playlist_id,)
            )
            
            return {
                'success': True,
                'playlist_name': playlist[0]['name'],
                'message': f"Playlist '{playlist[0]['name']}' eliminada correctamente"
            }
            
        except Exception as e:
            self.logger.error(f"Error al eliminar playlist: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def get_playlist_stats(self, playlist_id: int) -> Dict[str, Any]:
        """
        Obtener estadísticas de una playlist
        
        Args:
            playlist_id: ID de la playlist
        
        Returns:
            Diccionario con estadísticas
        """
        try:
            # Estadísticas básicas
            stats_query = """
                SELECT 
                    COUNT(*) as total_entries,
                    COUNT(DISTINCT group_title) as unique_groups,
                    COUNT(DISTINCT url) as unique_urls,
                    COUNT(CASE WHEN radio = 1 THEN 1 END) as radio_count,
                    COUNT(CASE WHEN radio = 0 THEN 1 END) as tv_count
                FROM m3u_entries 
                WHERE playlist_id = %s
            """
            
            stats = await self.db_manager.execute_query(stats_query, (playlist_id,))
            
            # Distribución por grupos
            groups_query = """
                SELECT group_title, COUNT(*) as count
                FROM m3u_entries 
                WHERE playlist_id = %s AND group_title != ''
                GROUP BY group_title
                ORDER BY count DESC
                LIMIT 10
            """
            
            groups = await self.db_manager.execute_query(groups_query, (playlist_id,))
            
            # Distribución por calidad
            quality_query = """
                SELECT quality, COUNT(*) as count
                FROM m3u_entries 
                WHERE playlist_id = %s AND quality != ''
                GROUP BY quality
                ORDER BY count DESC
            """
            
            quality_dist = await self.db_manager.execute_query(quality_query, (playlist_id,))
            
            return {
                'success': True,
                'stats': stats[0] if stats else {},
                'top_groups': groups,
                'quality_distribution': quality_dist
            }
            
        except Exception as e:
            self.logger.error(f"Error al obtener estadísticas: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _generate_entry_hash(self, entry: Dict[str, Any]) -> str:
        """Generar hash único para una entrada"""
        import hashlib
        hash_string = f"{entry.get('title', '')}|{entry.get('url', '')}|{entry.get('group_title', '')}"
        return hashlib.md5(hash_string.encode()).hexdigest()
    
    async def export_playlist(self, playlist_id: int, output_path: str, format: str = "m3u") -> Dict[str, Any]:
        """
        Exportar playlist a archivo
        
        Args:
            playlist_id: ID de la playlist
            output_path: Ruta del archivo de salida
            format: Formato de exportación ('m3u', 'json')
        
        Returns:
            Diccionario con resultado de la operación
        """
        try:
            # Obtener entradas de la playlist
            entries = await self.db_manager.execute_query(
                "SELECT * FROM m3u_entries WHERE playlist_id = %s ORDER BY id",
                (playlist_id,)
            )
            
            if not entries:
                return {
                    'success': False,
                    'error': 'No hay entradas para exportar'
                }
            
            if format.lower() == "m3u":
                await self._export_m3u(entries, output_path)
            elif format.lower() == "json":
                await self._export_json(entries, output_path)
            else:
                return {
                    'success': False,
                    'error': f'Formato no soportado: {format}'
                }
            
            return {
                'success': True,
                'output_path': output_path,
                'entries_count': len(entries),
                'format': format
            }
            
        except Exception as e:
            self.logger.error(f"Error al exportar playlist: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def _export_m3u(self, entries: List[Dict], output_path: str):
        """Exportar entradas a formato M3U"""
        import aiofiles
        
        async with aiofiles.open(output_path, 'w', encoding='utf-8') as f:
            await f.write('#EXTM3U\n')
            
            for entry in entries:
                # Reconstruir línea EXTINF
                extinf_line = f"#EXTINF:{entry.get('duration', -1)}"
                
                # Agregar atributos
                attributes = []
                if entry.get('tvg_id'):
                    attributes.append(f'tvg-id="{entry["tvg_id"]}"')
                if entry.get('tvg_name'):
                    attributes.append(f'tvg-name="{entry["tvg_name"]}"')
                if entry.get('tvg_logo'):
                    attributes.append(f'tvg-logo="{entry["tvg_logo"]}"')
                if entry.get('group_title'):
                    attributes.append(f'group-title="{entry["group_title"]}"')
                if entry.get('radio'):
                    attributes.append('radio="true"')
                
                if attributes:
                    extinf_line += f" {' '.join(attributes)}"
                
                extinf_line += f",{entry.get('title', '')}\n"
                
                await f.write(extinf_line)
                await f.write(f"{entry.get('url', '')}\n")
    
    async def _export_json(self, entries: List[Dict], output_path: str):
        """Exportar entradas a formato JSON"""
        import aiofiles
        import json
        
        async with aiofiles.open(output_path, 'w', encoding='utf-8') as f:
            await f.write(json.dumps(entries, indent=2, ensure_ascii=False, default=str))
