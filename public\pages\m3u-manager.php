<?php
/**
 * M3U Manager Page - IPTV XUI One Content Manager
 * ==============================================
 * 
 * Upload, process, and manage M3U files with drag & drop interface
 */
?>

<style>
.upload-section {
    background: var(--dark-surface);
    border: 1px solid var(--dark-border);
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
}

.upload-area {
    border: 2px dashed var(--dark-border);
    border-radius: 1rem;
    padding: 3rem 2rem;
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.upload-area:hover {
    border-color: var(--primary-color);
    background: rgba(59, 130, 246, 0.05);
}

.upload-area.dragover {
    border-color: var(--primary-color);
    background: rgba(59, 130, 246, 0.1);
    transform: scale(1.02);
}

.upload-icon {
    font-size: 4rem;
    color: var(--text-secondary);
    margin-bottom: 1rem;
    transition: var(--transition);
}

.upload-area:hover .upload-icon {
    color: var(--primary-color);
    transform: scale(1.1);
}

.upload-text {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.upload-subtext {
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

.file-input {
    display: none;
}

.upload-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-top: 2rem;
}

.option-card {
    background: var(--dark-bg);
    border: 1px solid var(--dark-border);
    border-radius: 0.75rem;
    padding: 1.5rem;
    transition: var(--transition);
}

.option-card:hover {
    border-color: var(--primary-color);
}

.option-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.option-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.option-title {
    font-weight: 600;
    color: var(--text-primary);
}

.option-description {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin-bottom: 1rem;
}

.checkbox-wrapper {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.checkbox {
    width: 18px;
    height: 18px;
    border: 2px solid var(--dark-border);
    border-radius: 4px;
    background: var(--dark-bg);
    cursor: pointer;
    transition: var(--transition);
}

.checkbox:checked {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.progress-section {
    background: var(--dark-surface);
    border: 1px solid var(--dark-border);
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
    display: none;
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.progress-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.progress-bar-container {
    background: var(--dark-bg);
    border-radius: 0.5rem;
    height: 12px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 0.5rem;
    transition: width 0.3s ease;
    width: 0%;
}

.progress-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.progress-stat {
    text-align: center;
    padding: 1rem;
    background: var(--dark-bg);
    border-radius: 0.5rem;
}

.progress-stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
}

.progress-stat-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-top: 0.25rem;
}

.results-section {
    background: var(--dark-surface);
    border: 1px solid var(--dark-border);
    border-radius: 1rem;
    padding: 2rem;
    display: none;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.results-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.results-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.summary-card {
    background: var(--dark-bg);
    border: 1px solid var(--dark-border);
    border-radius: 0.75rem;
    padding: 1.5rem;
    text-align: center;
}

.summary-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.summary-label {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.export-options {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    margin-top: 2rem;
}

.log-container {
    background: var(--dark-bg);
    border: 1px solid var(--dark-border);
    border-radius: 0.5rem;
    padding: 1rem;
    max-height: 300px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    margin-top: 1rem;
}

.log-entry {
    padding: 0.25rem 0;
    border-bottom: 1px solid var(--dark-border);
}

.log-entry:last-child {
    border-bottom: none;
}

.log-timestamp {
    color: var(--text-secondary);
}

.log-level {
    font-weight: 600;
    margin: 0 0.5rem;
}

.log-level.info { color: var(--primary-color); }
.log-level.success { color: var(--success-color); }
.log-level.warning { color: var(--warning-color); }
.log-level.error { color: var(--error-color); }

.log-message {
    color: var(--text-primary);
}
</style>

<!-- Upload Section -->
<div class="upload-section">
    <h2 style="margin-bottom: 2rem; color: var(--text-primary);">
        <i class="fas fa-upload"></i>
        Upload M3U Files
    </h2>
    
    <div class="upload-area" id="uploadArea">
        <div class="upload-icon">
            <i class="fas fa-cloud-upload-alt"></i>
        </div>
        <div class="upload-text">Drag & Drop M3U Files Here</div>
        <div class="upload-subtext">or click to browse files</div>
        <button class="btn btn-primary">
            <i class="fas fa-folder-open"></i>
            Browse Files
        </button>
        <input type="file" id="fileInput" class="file-input" accept=".m3u,.m3u8" multiple>
    </div>
    
    <!-- Upload Options -->
    <div class="upload-options">
        <div class="option-card">
            <div class="option-header">
                <div class="option-icon">
                    <i class="fas fa-magic"></i>
                </div>
                <div class="option-title">TMDB Enrichment</div>
            </div>
            <div class="option-description">
                Automatically enrich content with TMDB metadata including posters, descriptions, and ratings.
            </div>
            <div class="checkbox-wrapper">
                <input type="checkbox" id="enrichTmdb" class="checkbox" checked>
                <label for="enrichTmdb">Enable TMDB enrichment</label>
            </div>
        </div>
        
        <div class="option-card">
            <div class="option-header">
                <div class="option-icon">
                    <i class="fas fa-copy"></i>
                </div>
                <div class="option-title">Duplicate Handling</div>
            </div>
            <div class="option-description">
                Skip content that already exists in the database to avoid duplicates.
            </div>
            <div class="checkbox-wrapper">
                <input type="checkbox" id="skipDuplicates" class="checkbox" checked>
                <label for="skipDuplicates">Skip existing content</label>
            </div>
        </div>
        
        <div class="option-card">
            <div class="option-header">
                <div class="option-icon">
                    <i class="fas fa-tags"></i>
                </div>
                <div class="option-title">Auto Categorization</div>
            </div>
            <div class="option-description">
                Automatically categorize content based on title and metadata patterns.
            </div>
            <div class="checkbox-wrapper">
                <input type="checkbox" id="autoCategorize" class="checkbox" checked>
                <label for="autoCategorize">Enable auto categorization</label>
            </div>
        </div>
        
        <div class="option-card">
            <div class="option-header">
                <div class="option-icon">
                    <i class="fas fa-batch"></i>
                </div>
                <div class="option-title">Batch Processing</div>
            </div>
            <div class="option-description">
                Process content in batches for better performance and progress tracking.
            </div>
            <div class="checkbox-wrapper">
                <input type="checkbox" id="batchProcessing" class="checkbox" checked>
                <label for="batchProcessing">Enable batch processing</label>
            </div>
        </div>
    </div>
</div>

<!-- Progress Section -->
<div class="progress-section" id="progressSection">
    <div class="progress-header">
        <div class="progress-title">Processing M3U Files</div>
        <button class="btn btn-secondary" onclick="cancelProcessing()">
            <i class="fas fa-times"></i>
            Cancel
        </button>
    </div>
    
    <div class="progress-bar-container">
        <div class="progress-bar" id="progressBar"></div>
    </div>
    
    <div style="display: flex; justify-content: space-between; margin-bottom: 1rem;">
        <span id="progressText">Initializing...</span>
        <span id="progressPercent">0%</span>
    </div>
    
    <div class="progress-stats">
        <div class="progress-stat">
            <div class="progress-stat-value" id="processedCount">0</div>
            <div class="progress-stat-label">Processed</div>
        </div>
        <div class="progress-stat">
            <div class="progress-stat-value" id="enrichedCount">0</div>
            <div class="progress-stat-label">Enriched</div>
        </div>
        <div class="progress-stat">
            <div class="progress-stat-value" id="errorCount">0</div>
            <div class="progress-stat-label">Errors</div>
        </div>
        <div class="progress-stat">
            <div class="progress-stat-value" id="timeElapsed">0s</div>
            <div class="progress-stat-label">Time Elapsed</div>
        </div>
    </div>
    
    <!-- Processing Log -->
    <div class="log-container" id="logContainer">
        <div class="log-entry">
            <span class="log-timestamp">[00:00:00]</span>
            <span class="log-level info">[INFO]</span>
            <span class="log-message">Ready to process M3U files...</span>
        </div>
    </div>
</div>

<!-- Results Section -->
<div class="results-section" id="resultsSection">
    <div class="results-header">
        <div class="results-title">Processing Complete</div>
        <button class="btn btn-primary" onclick="resetUploader()">
            <i class="fas fa-plus"></i>
            Upload More Files
        </button>
    </div>
    
    <div class="results-summary">
        <div class="summary-card">
            <div class="summary-value" id="totalProcessed">0</div>
            <div class="summary-label">Total Processed</div>
        </div>
        <div class="summary-card">
            <div class="summary-value" id="successfulImports">0</div>
            <div class="summary-label">Successful Imports</div>
        </div>
        <div class="summary-card">
            <div class="summary-value" id="tmdbEnriched">0</div>
            <div class="summary-label">TMDB Enriched</div>
        </div>
        <div class="summary-card">
            <div class="summary-value" id="totalErrors">0</div>
            <div class="summary-label">Errors</div>
        </div>
    </div>
    
    <!-- Export Options -->
    <div class="export-options">
        <button class="btn btn-primary" onclick="exportResults('m3u')">
            <i class="fas fa-download"></i>
            Export M3U
        </button>
        <button class="btn btn-secondary" onclick="exportResults('txt')">
            <i class="fas fa-file-text"></i>
            Export TXT
        </button>
        <button class="btn btn-secondary" onclick="exportResults('json')">
            <i class="fas fa-code"></i>
            Export JSON
        </button>
        <button class="btn btn-secondary" onclick="viewReport()">
            <i class="fas fa-chart-bar"></i>
            View Report
        </button>
    </div>
</div>

<script>
let processingData = {
    files: [],
    currentFile: 0,
    startTime: null,
    isProcessing: false
};

// Initialize drag & drop
document.addEventListener('DOMContentLoaded', function() {
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('fileInput');
    
    // Click to upload
    uploadArea.addEventListener('click', () => fileInput.click());
    
    // Drag & drop events
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleDrop);
    
    // File input change
    fileInput.addEventListener('change', handleFileSelect);
});

function handleDragOver(e) {
    e.preventDefault();
    e.stopPropagation();
    document.getElementById('uploadArea').classList.add('dragover');
}

function handleDragLeave(e) {
    e.preventDefault();
    e.stopPropagation();
    document.getElementById('uploadArea').classList.remove('dragover');
}

function handleDrop(e) {
    e.preventDefault();
    e.stopPropagation();
    document.getElementById('uploadArea').classList.remove('dragover');
    
    const files = Array.from(e.dataTransfer.files);
    processFiles(files);
}

function handleFileSelect(e) {
    const files = Array.from(e.target.files);
    processFiles(files);
}

function processFiles(files) {
    // Filter M3U files
    const m3uFiles = files.filter(file => 
        file.name.toLowerCase().endsWith('.m3u') || 
        file.name.toLowerCase().endsWith('.m3u8')
    );
    
    if (m3uFiles.length === 0) {
        showNotification('Please select valid M3U files', 'error');
        return;
    }
    
    processingData.files = m3uFiles;
    processingData.currentFile = 0;
    processingData.startTime = Date.now();
    processingData.isProcessing = true;
    
    // Show progress section
    document.querySelector('.upload-section').style.display = 'none';
    document.getElementById('progressSection').style.display = 'block';
    
    // Start processing
    startProcessing();
}

function startProcessing() {
    addLogEntry('info', `Starting processing of ${processingData.files.length} files`);
    
    // Simulate processing with progress updates
    let progress = 0;
    const totalSteps = processingData.files.length * 100;
    
    const interval = setInterval(() => {
        progress += Math.random() * 5;
        
        if (progress >= 100) {
            progress = 100;
            clearInterval(interval);
            completeProcessing();
        }
        
        updateProgress(progress);
        updateStats();
        
        // Add random log entries
        if (Math.random() < 0.3) {
            const messages = [
                'Parsing M3U entries...',
                'Enriching with TMDB data...',
                'Detecting content type...',
                'Saving to database...',
                'Processing complete for entry'
            ];
            addLogEntry('info', messages[Math.floor(Math.random() * messages.length)]);
        }
    }, 200);
}

function updateProgress(percent) {
    document.getElementById('progressBar').style.width = percent + '%';
    document.getElementById('progressPercent').textContent = Math.round(percent) + '%';
    
    if (percent < 30) {
        document.getElementById('progressText').textContent = 'Parsing M3U files...';
    } else if (percent < 70) {
        document.getElementById('progressText').textContent = 'Enriching with TMDB data...';
    } else if (percent < 90) {
        document.getElementById('progressText').textContent = 'Saving to database...';
    } else {
        document.getElementById('progressText').textContent = 'Finalizing...';
    }
}

function updateStats() {
    const elapsed = Math.floor((Date.now() - processingData.startTime) / 1000);
    document.getElementById('timeElapsed').textContent = elapsed + 's';
    
    // Simulate increasing counters
    const processed = Math.floor(Math.random() * 100);
    const enriched = Math.floor(processed * 0.8);
    const errors = Math.floor(Math.random() * 5);
    
    document.getElementById('processedCount').textContent = processed;
    document.getElementById('enrichedCount').textContent = enriched;
    document.getElementById('errorCount').textContent = errors;
}

function addLogEntry(level, message) {
    const logContainer = document.getElementById('logContainer');
    const timestamp = new Date().toLocaleTimeString();
    
    const entry = document.createElement('div');
    entry.className = 'log-entry';
    entry.innerHTML = `
        <span class="log-timestamp">[${timestamp}]</span>
        <span class="log-level ${level}">[${level.toUpperCase()}]</span>
        <span class="log-message">${message}</span>
    `;
    
    logContainer.appendChild(entry);
    logContainer.scrollTop = logContainer.scrollHeight;
}

function completeProcessing() {
    addLogEntry('success', 'Processing completed successfully!');
    
    // Hide progress, show results
    setTimeout(() => {
        document.getElementById('progressSection').style.display = 'none';
        document.getElementById('resultsSection').style.display = 'block';
        
        // Update result summary
        document.getElementById('totalProcessed').textContent = '156';
        document.getElementById('successfulImports').textContent = '142';
        document.getElementById('tmdbEnriched').textContent = '128';
        document.getElementById('totalErrors').textContent = '3';
        
        showNotification('M3U processing completed successfully!', 'success');
    }, 1000);
}

function cancelProcessing() {
    if (confirm('Are you sure you want to cancel processing?')) {
        processingData.isProcessing = false;
        addLogEntry('warning', 'Processing cancelled by user');
        resetUploader();
    }
}

function resetUploader() {
    document.querySelector('.upload-section').style.display = 'block';
    document.getElementById('progressSection').style.display = 'none';
    document.getElementById('resultsSection').style.display = 'none';
    
    // Reset file input
    document.getElementById('fileInput').value = '';
    
    // Clear log
    document.getElementById('logContainer').innerHTML = `
        <div class="log-entry">
            <span class="log-timestamp">[00:00:00]</span>
            <span class="log-level info">[INFO]</span>
            <span class="log-message">Ready to process M3U files...</span>
        </div>
    `;
}

function exportResults(format) {
    showNotification(`Exporting results as ${format.toUpperCase()}...`, 'info');
    // TODO: Implement actual export functionality
}

function viewReport() {
    showNotification('Opening detailed report...', 'info');
    // TODO: Implement report viewer
}
</script>
