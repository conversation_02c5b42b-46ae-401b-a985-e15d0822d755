# 🚀 Installation Guide - IPTV XUI One Content Manager (PHP Edition)

## 📋 Prerequisites

### System Requirements
- **PHP**: 8.0 or higher
- **MySQL/MariaDB**: 5.7+ or 10.3+
- **Web Server**: Apache 2.4+ or Nginx 1.18+
- **Memory**: Minimum 512MB RAM (1GB+ recommended)
- **Storage**: 1GB+ free space

### PHP Extensions Required
- `pdo_mysql` - Database connectivity
- `json` - JSON processing
- `mbstring` - Multi-byte string handling
- `curl` - HTTP requests for TMDB API
- `fileinfo` - File type detection
- `gd` or `imagick` - Image processing (optional)

### Database Access
- Existing XUI One MySQL database
- Database user with SELECT, INSERT, UPDATE, DELETE permissions
- Network access to the database server

## 🛠️ Installation Steps

### 1. Download and Extract

```bash
# Clone or download the project
git clone https://github.com/iptv-manager/xui-one-content-manager.git
cd xui-one-content-manager

# Or download and extract ZIP
wget https://github.com/iptv-manager/xui-one-content-manager/archive/main.zip
unzip main.zip
cd xui-one-content-manager-main
```

### 2. Install Dependencies (Optional)

```bash
# If you have Composer installed
composer install --no-dev --optimize-autoloader

# If you don't have Composer, the system will work without it
# All core functionality is self-contained
```

### 3. Configure Web Server

#### Apache Configuration

Create a virtual host or configure your document root:

```apache
<VirtualHost *:80>
    ServerName iptv-manager.local
    DocumentRoot /path/to/xui-one-content-manager/public
    
    <Directory /path/to/xui-one-content-manager/public>
        AllowOverride All
        Require all granted
        DirectoryIndex index.php
    </Directory>
    
    # Optional: Enable compression
    <IfModule mod_deflate.c>
        AddOutputFilterByType DEFLATE text/html text/css text/javascript application/javascript application/json
    </IfModule>
</VirtualHost>
```

#### Nginx Configuration

```nginx
server {
    listen 80;
    server_name iptv-manager.local;
    root /path/to/xui-one-content-manager/public;
    index index.php;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.0-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;

    # Gzip compression
    gzip on;
    gzip_types text/css application/javascript application/json;
}
```

### 4. Set Permissions

```bash
# Make directories writable
chmod 755 public/
chmod 755 uploads/
chmod 755 logs/

# Create directories if they don't exist
mkdir -p uploads logs
chmod 755 uploads logs

# Set ownership (adjust user/group as needed)
chown -R www-data:www-data /path/to/xui-one-content-manager
```

### 5. Database Configuration

#### Option A: Environment Variables (Recommended)

Create a `.env` file in the project root:

```bash
# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=iptv_xui
DB_USER=your_username
DB_PASSWORD=your_password

# TMDB API Configuration
TMDB_API_KEY=your_tmdb_api_key

# Application Settings
APP_DEBUG=false
LOG_LEVEL=INFO
DEFAULT_THEME=dark
```

#### Option B: Direct Configuration

Edit `config/database.php` and update the database credentials:

```php
define('DB_CONFIG', [
    'host' => 'your_database_host',
    'port' => 3306,
    'database' => 'your_xui_database_name',
    'username' => 'your_username',
    'password' => 'your_password',
    // ... other settings
]);
```

### 6. TMDB API Setup (Optional but Recommended)

1. Visit [The Movie Database](https://www.themoviedb.org/)
2. Create a free account
3. Go to Settings → API
4. Request an API key
5. Add your API key to the configuration

### 7. Test Installation

1. Open your web browser
2. Navigate to your configured domain (e.g., `http://iptv-manager.local`)
3. You should see the dashboard
4. Check the database connection status

## 🔧 Configuration Options

### Application Settings

Edit `config/settings.php` to customize:

- **Theme**: Default theme (dark/light/auto)
- **Language**: Default language
- **Page Size**: Items per page
- **Upload Limits**: Maximum file sizes
- **Cache Settings**: Enable/disable caching
- **Security**: Session timeout, CSRF protection

### Performance Optimization

#### Enable PHP OPcache

Add to your `php.ini`:

```ini
opcache.enable=1
opcache.memory_consumption=128
opcache.interned_strings_buffer=8
opcache.max_accelerated_files=4000
opcache.revalidate_freq=2
opcache.fast_shutdown=1
```

#### Database Optimization

```sql
-- Add indexes for better performance
ALTER TABLE streams ADD INDEX idx_type_symlink (type, movie_symlink);
ALTER TABLE streams ADD INDEX idx_tmdb_id (tmdb_id);
ALTER TABLE streams ADD INDEX idx_added (added);
```

## 🚨 Troubleshooting

### Common Issues

#### 1. Database Connection Failed
- Verify database credentials
- Check if MySQL service is running
- Ensure database user has proper permissions
- Test connection from command line: `mysql -h host -u user -p database`

#### 2. File Upload Issues
- Check PHP upload limits: `upload_max_filesize`, `post_max_size`
- Verify directory permissions: `uploads/` should be writable
- Check disk space availability

#### 3. TMDB API Errors
- Verify API key is correct
- Check internet connectivity
- Ensure API rate limits aren't exceeded

#### 4. Performance Issues
- Enable PHP OPcache
- Increase PHP memory limit
- Optimize database with proper indexes
- Enable gzip compression

### Debug Mode

Enable debug mode for detailed error information:

```php
// In config/settings.php
define('APP_CONFIG', [
    'debug' => true,
    // ... other settings
]);
```

### Log Files

Check log files for errors:

```bash
# Application logs
tail -f logs/app.log

# Web server logs
tail -f /var/log/apache2/error.log  # Apache
tail -f /var/log/nginx/error.log    # Nginx

# PHP logs
tail -f /var/log/php8.0-fpm.log
```

## 🔐 Security Considerations

### Production Deployment

1. **Disable Debug Mode**: Set `APP_DEBUG=false`
2. **Use HTTPS**: Configure SSL/TLS certificates
3. **Secure Database**: Use strong passwords, limit access
4. **File Permissions**: Restrict write access to necessary directories only
5. **Regular Updates**: Keep PHP, web server, and database updated
6. **Backup Strategy**: Implement regular backups

### Recommended Security Headers

```apache
# Apache .htaccess
Header always set X-Frame-Options "SAMEORIGIN"
Header always set X-Content-Type-Options "nosniff"
Header always set X-XSS-Protection "1; mode=block"
Header always set Referrer-Policy "strict-origin-when-cross-origin"
```

## 📞 Support

### Getting Help

1. **Documentation**: Check the README.md and inline code comments
2. **Issues**: Report bugs on GitHub Issues
3. **Community**: Join our Discord/Telegram community
4. **Professional Support**: Contact <EMAIL>

### System Information

To help with support, gather this information:

```bash
# PHP version and extensions
php -v
php -m

# Web server version
apache2 -v  # or nginx -v

# Database version
mysql --version

# System information
uname -a
df -h
free -h
```

## 🎉 Next Steps

After successful installation:

1. **Upload M3U Files**: Use the M3U Manager to import content
2. **Configure TMDB**: Set up automatic metadata enrichment
3. **Explore Analytics**: View content statistics and insights
4. **Customize Settings**: Adjust preferences to your needs
5. **Set Up Backups**: Implement regular backup procedures

---

**🎬 Welcome to the modern IPTV content management experience!**
