"""
User models for IPTV XUI One Content Manager
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, List, Optional, Any
import json

@dataclass
class User:
    """Model for user data"""
    id: Optional[int] = None
    username: str = ""
    email: str = ""
    password_hash: str = ""
    first_name: str = ""
    last_name: str = ""
    is_active: bool = True
    is_admin: bool = False
    last_login: Optional[datetime] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'password_hash': self.password_hash,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'is_active': self.is_active,
            'is_admin': self.is_admin,
            'last_login': self.last_login.isoformat() if self.last_login else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'User':
        """Create from dictionary"""
        user = cls()
        for key, value in data.items():
            if key in ['last_login', 'created_at', 'updated_at'] and isinstance(value, str):
                try:
                    value = datetime.fromisoformat(value.replace('Z', '+00:00'))
                except:
                    value = None
            if hasattr(user, key):
                setattr(user, key, value)
        return user

@dataclass
class UserPreferences:
    """Model for user preferences"""
    id: Optional[int] = None
    user_id: Optional[int] = None
    theme: str = "dark"
    language: str = "es"
    auto_import: bool = True
    notifications_enabled: bool = True
    default_quality: str = "HD"
    preferred_genres: List[str] = field(default_factory=list)
    dashboard_layout: Dict[str, Any] = field(default_factory=dict)
    tmdb_settings: Dict[str, Any] = field(default_factory=dict)
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'theme': self.theme,
            'language': self.language,
            'auto_import': self.auto_import,
            'notifications_enabled': self.notifications_enabled,
            'default_quality': self.default_quality,
            'preferred_genres': json.dumps(self.preferred_genres),
            'dashboard_layout': json.dumps(self.dashboard_layout),
            'tmdb_settings': json.dumps(self.tmdb_settings)
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'UserPreferences':
        """Create from dictionary"""
        prefs = cls()
        for key, value in data.items():
            if key in ['preferred_genres', 'dashboard_layout', 'tmdb_settings'] and isinstance(value, str):
                try:
                    value = json.loads(value)
                except:
                    value = [] if key == 'preferred_genres' else {}
            elif key in ['created_at', 'updated_at'] and isinstance(value, str):
                try:
                    value = datetime.fromisoformat(value.replace('Z', '+00:00'))
                except:
                    value = None
            if hasattr(prefs, key):
                setattr(prefs, key, value)
        return prefs

@dataclass
class Session:
    """Model for user session"""
    id: Optional[int] = None
    session_id: str = ""
    user_id: Optional[int] = None
    user_data: Dict[str, Any] = field(default_factory=dict)
    preferences: Dict[str, Any] = field(default_factory=dict)
    last_activity: Optional[datetime] = None
    expires_at: Optional[datetime] = None
    created_at: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'id': self.id,
            'session_id': self.session_id,
            'user_id': self.user_id,
            'user_data': json.dumps(self.user_data),
            'preferences': json.dumps(self.preferences),
            'last_activity': self.last_activity.isoformat() if self.last_activity else None,
            'expires_at': self.expires_at.isoformat() if self.expires_at else None,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Session':
        """Create from dictionary"""
        session = cls()
        for key, value in data.items():
            if key in ['user_data', 'preferences'] and isinstance(value, str):
                try:
                    value = json.loads(value)
                except:
                    value = {}
            elif key in ['last_activity', 'expires_at', 'created_at'] and isinstance(value, str):
                try:
                    value = datetime.fromisoformat(value.replace('Z', '+00:00'))
                except:
                    value = None
            if hasattr(session, key):
                setattr(session, key, value)
        return session
    
    def is_expired(self) -> bool:
        """Check if session is expired"""
        if not self.expires_at:
            return True
        return datetime.now() > self.expires_at
