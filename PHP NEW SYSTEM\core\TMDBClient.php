<?php
/**
 * TMDB API Client for IPTV XUI One Content Manager
 * ===============================================
 * 
 * Handles all interactions with The Movie Database API
 * including search, metadata enrichment, and caching.
 */

require_once __DIR__ . '/../config/tmdb_config.php';
require_once __DIR__ . '/../config/settings.php';

class TMDBClient {
    private $apiKey;
    private $baseUrl;
    private $cache = [];
    private $rateLimiter = [];
    private $logger;
    private $stats = [
        'api_calls' => 0,
        'cache_hits' => 0,
        'cache_misses' => 0,
        'rate_limit_hits' => 0
    ];

    public function __construct() {
        $this->apiKey = TMDB_CONFIG['api_key'];
        $this->baseUrl = TMDB_CONFIG['base_url'];
        $this->logger = $this->initializeLogger();
        
        if (empty($this->apiKey)) {
            throw new Exception("TMDB API key is required");
        }
    }

    /**
     * Search for movies
     */
    public function searchMovies($query, $year = null, $page = 1) {
        $params = [
            'query' => $query,
            'page' => $page,
            'include_adult' => TMDB_CONFIG['include_adult'] ? 'true' : 'false',
            'language' => TMDB_CONFIG['language']
        ];

        if ($year) {
            $params['year'] = $year;
        }

        $cacheKey = 'search_movies_' . md5(serialize($params));
        return $this->makeRequest('search/movie', $params, $cacheKey, TMDB_CACHE_CONFIG['ttl']['search_results']);
    }

    /**
     * Search for TV shows
     */
    public function searchTVShows($query, $year = null, $page = 1) {
        $params = [
            'query' => $query,
            'page' => $page,
            'include_adult' => TMDB_CONFIG['include_adult'] ? 'true' : 'false',
            'language' => TMDB_CONFIG['language']
        ];

        if ($year) {
            $params['first_air_date_year'] = $year;
        }

        $cacheKey = 'search_tv_' . md5(serialize($params));
        return $this->makeRequest('search/tv', $params, $cacheKey, TMDB_CACHE_CONFIG['ttl']['search_results']);
    }

    /**
     * Get movie details
     */
    public function getMovieDetails($movieId) {
        $params = [
            'language' => TMDB_CONFIG['language'],
            'append_to_response' => 'credits,images,videos,keywords'
        ];

        $cacheKey = 'movie_details_' . $movieId;
        return $this->makeRequest("movie/{$movieId}", $params, $cacheKey, TMDB_CACHE_CONFIG['ttl']['movie_details']);
    }

    /**
     * Get TV show details
     */
    public function getTVShowDetails($tvId) {
        $params = [
            'language' => TMDB_CONFIG['language'],
            'append_to_response' => 'credits,images,videos,keywords'
        ];

        $cacheKey = 'tv_details_' . $tvId;
        return $this->makeRequest("tv/{$tvId}", $params, $cacheKey, TMDB_CACHE_CONFIG['ttl']['tv_details']);
    }

    /**
     * Find best match for a title
     */
    public function findBestMatch($title, $year = null, $type = 'movie') {
        $cleanTitle = clean_title_for_tmdb($title);
        
        try {
            // Search based on type
            if ($type === 'movie') {
                $results = $this->searchMovies($cleanTitle, $year);
            } else {
                $results = $this->searchTVShows($cleanTitle, $year);
            }

            if (empty($results['results'])) {
                return null;
            }

            // Find best match using similarity
            $bestMatch = $this->calculateBestMatch($cleanTitle, $results['results'], $year);
            
            if ($bestMatch) {
                $this->log('info', "Found TMDB match", [
                    'original_title' => $title,
                    'clean_title' => $cleanTitle,
                    'match_title' => $bestMatch['title'] ?? $bestMatch['name'],
                    'tmdb_id' => $bestMatch['id'],
                    'similarity' => $bestMatch['_similarity'] ?? 'unknown'
                ]);
            }

            return $bestMatch;

        } catch (Exception $e) {
            $this->log('error', "TMDB search failed", [
                'title' => $title,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Calculate best match from search results
     */
    private function calculateBestMatch($searchTitle, $results, $year = null) {
        $bestMatch = null;
        $bestScore = 0;

        foreach ($results as $result) {
            $resultTitle = $result['title'] ?? $result['name'] ?? '';
            $originalTitle = $result['original_title'] ?? $result['original_name'] ?? '';
            
            // Calculate similarity scores
            $titleScore = $this->calculateSimilarity($searchTitle, $resultTitle);
            $originalScore = $this->calculateSimilarity($searchTitle, $originalTitle);
            $maxScore = max($titleScore, $originalScore);

            // Year bonus
            if ($year && isset($result['release_date'])) {
                $resultYear = (int)substr($result['release_date'], 0, 4);
                if (abs($resultYear - $year) <= TMDB_MATCHING['year_tolerance']) {
                    $maxScore += 0.1; // Small bonus for year match
                }
            }

            if ($maxScore > $bestScore && $maxScore >= TMDB_MATCHING['similarity_threshold']) {
                $bestScore = $maxScore;
                $bestMatch = $result;
                $bestMatch['_similarity'] = $maxScore;
            }
        }

        return $bestMatch;
    }

    /**
     * Calculate string similarity
     */
    private function calculateSimilarity($str1, $str2) {
        $str1 = strtolower(trim($str1));
        $str2 = strtolower(trim($str2));

        if ($str1 === $str2) {
            return 1.0;
        }

        // Use Levenshtein distance for similarity
        $maxLen = max(strlen($str1), strlen($str2));
        if ($maxLen === 0) {
            return 0;
        }

        $distance = levenshtein($str1, $str2);
        return 1 - ($distance / $maxLen);
    }

    /**
     * Enrich content with TMDB data
     */
    public function enrichContent($content) {
        $enriched = $content;
        
        try {
            $match = $this->findBestMatch(
                $content['clean_title'] ?? $content['title'],
                $content['year'] ?? null,
                $content['content_type'] === 'tv_show' ? 'tv' : 'movie'
            );

            if ($match) {
                $enriched['tmdb_id'] = $match['id'];
                $enriched['tmdb_data'] = $match;
                
                // Get detailed information
                if ($content['content_type'] === 'tv_show') {
                    $details = $this->getTVShowDetails($match['id']);
                } else {
                    $details = $this->getMovieDetails($match['id']);
                }

                if ($details) {
                    $enriched['tmdb_details'] = $details;
                    $enriched = $this->mapTMDBData($enriched, $details);
                }
            }

        } catch (Exception $e) {
            $this->log('error', "Content enrichment failed", [
                'title' => $content['title'],
                'error' => $e->getMessage()
            ]);
        }

        return $enriched;
    }

    /**
     * Map TMDB data to content structure
     */
    private function mapTMDBData($content, $tmdbData) {
        $content['overview'] = $tmdbData['overview'] ?? '';
        $content['rating'] = $tmdbData['vote_average'] ?? 0;
        $content['vote_count'] = $tmdbData['vote_count'] ?? 0;
        $content['popularity'] = $tmdbData['popularity'] ?? 0;
        $content['poster_path'] = $tmdbData['poster_path'] ?? '';
        $content['backdrop_path'] = $tmdbData['backdrop_path'] ?? '';
        $content['genres'] = $tmdbData['genres'] ?? [];
        
        // Movie specific
        if (isset($tmdbData['release_date'])) {
            $content['release_date'] = $tmdbData['release_date'];
            $content['year'] = (int)substr($tmdbData['release_date'], 0, 4);
        }
        
        // TV Show specific
        if (isset($tmdbData['first_air_date'])) {
            $content['first_air_date'] = $tmdbData['first_air_date'];
            $content['year'] = (int)substr($tmdbData['first_air_date'], 0, 4);
        }

        return $content;
    }

    /**
     * Make API request with rate limiting and caching
     */
    private function makeRequest($endpoint, $params = [], $cacheKey = null, $cacheTtl = null) {
        // Check cache first
        if ($cacheKey && $this->isCacheEnabled()) {
            $cached = $this->getFromCache($cacheKey);
            if ($cached !== null) {
                $this->stats['cache_hits']++;
                return $cached;
            }
            $this->stats['cache_misses']++;
        }

        // Rate limiting
        $this->enforceRateLimit();

        // Build URL
        $params['api_key'] = $this->apiKey;
        $url = $this->baseUrl . '/' . ltrim($endpoint, '/') . '?' . http_build_query($params);

        // Make request
        $context = stream_context_create([
            'http' => [
                'timeout' => TMDB_CONFIG['timeout'],
                'user_agent' => 'IPTV XUI One Content Manager/2.0'
            ]
        ]);

        $response = file_get_contents($url, false, $context);
        $this->stats['api_calls']++;

        if ($response === false) {
            throw new Exception("TMDB API request failed: {$endpoint}");
        }

        $data = json_decode($response, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception("Invalid JSON response from TMDB API");
        }

        // Check for API errors
        if (isset($data['status_code']) && $data['status_code'] !== 1) {
            throw new Exception("TMDB API error: " . ($data['status_message'] ?? 'Unknown error'));
        }

        // Cache the result
        if ($cacheKey && $this->isCacheEnabled()) {
            $this->setCache($cacheKey, $data, $cacheTtl);
        }

        $this->log('debug', "TMDB API request", [
            'endpoint' => $endpoint,
            'params' => $params,
            'response_size' => strlen($response)
        ]);

        return $data;
    }

    /**
     * Enforce rate limiting
     */
    private function enforceRateLimit() {
        $now = time();
        $this->rateLimiter = array_filter($this->rateLimiter, function($timestamp) use ($now) {
            return $now - $timestamp < 60; // Keep last minute
        });

        if (count($this->rateLimiter) >= TMDB_RATE_LIMIT['requests_per_minute']) {
            $this->stats['rate_limit_hits']++;
            sleep(1); // Wait 1 second
        }

        $this->rateLimiter[] = $now;
    }

    /**
     * Cache management
     */
    private function isCacheEnabled() {
        return TMDB_CACHE_CONFIG['enabled'];
    }

    private function getFromCache($key) {
        if (!isset($this->cache[$key])) {
            return null;
        }

        $item = $this->cache[$key];
        if ($item['expires'] < time()) {
            unset($this->cache[$key]);
            return null;
        }

        return $item['data'];
    }

    private function setCache($key, $data, $ttl = null) {
        $ttl = $ttl ?? TMDB_CACHE_CONFIG['ttl']['search_results'];
        $this->cache[$key] = [
            'data' => $data,
            'expires' => time() + $ttl
        ];
    }

    /**
     * Get client statistics
     */
    public function getStats() {
        return $this->stats;
    }

    /**
     * Initialize logger
     */
    private function initializeLogger() {
        return new class {
            public function log($level, $message, $context = []) {
                if (LOGGING_CONFIG['enabled']) {
                    $logEntry = sprintf(
                        "[%s] TMDBClient %s: %s %s\n",
                        date('Y-m-d H:i:s'),
                        strtoupper($level),
                        $message,
                        !empty($context) ? json_encode($context) : ''
                    );
                    file_put_contents(LOGGING_CONFIG['file_path'], $logEntry, FILE_APPEND | LOCK_EX);
                }
            }
        };
    }

    private function log($level, $message, $context = []) {
        $this->logger->log($level, $message, $context);
    }
}
