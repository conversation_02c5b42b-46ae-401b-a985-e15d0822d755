#!/usr/bin/env python3
"""
Configurador de Base de Datos para XUI One
==========================================

Este script te ayuda a configurar las credenciales de tu base de datos XUI One.
"""

import os
import sys
from pathlib import Path

def get_user_input(prompt, default=None, secure=False):
    """Obtener entrada del usuario con valor por defecto"""
    if default:
        display_prompt = f"{prompt} [{default}]: "
    else:
        display_prompt = f"{prompt}: "
    
    if secure:
        import getpass
        value = getpass.getpass(display_prompt)
    else:
        value = input(display_prompt)
    
    return value.strip() if value.strip() else default

def test_connection(host, port, user, password, database):
    """Probar conexión a la base de datos"""
    try:
        import mysql.connector
        
        conn = mysql.connector.connect(
            host=host,
            port=int(port),
            user=user,
            password=password,
            database=database,
            connect_timeout=10
        )
        
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM streams")
        result = cursor.fetchone()
        
        cursor.close()
        conn.close()
        
        return True, f"Conexión exitosa. Streams encontrados: {result[0] if result else 0}"
        
    except Exception as e:
        return False, f"Error de conexión: {str(e)}"

def main():
    """Función principal del configurador"""
    print("🔧 Configurador de Base de Datos XUI One")
    print("=" * 50)
    print()
    
    # Obtener datos del usuario
    print("📋 Ingresa los datos de tu base de datos XUI One:")
    print()
    
    host = get_user_input("Host de la base de datos", "localhost")
    port = get_user_input("Puerto", "3306")
    database = get_user_input("Nombre de la base de datos", "xtream_codes")
    user = get_user_input("Usuario")
    password = get_user_input("Contraseña", secure=True)
    
    print()
    print("🔍 Probando conexión...")
    
    # Probar conexión
    success, message = test_connection(host, port, user, password, database)
    
    if success:
        print(f"✅ {message}")
        print()
        
        # Actualizar archivo .env
        env_path = Path(".env")
        
        if env_path.exists():
            # Leer archivo existente
            with open(env_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Actualizar credenciales
            import re
            
            content = re.sub(r'DB_HOST=.*', f'DB_HOST={host}', content)
            content = re.sub(r'DB_PORT=.*', f'DB_PORT={port}', content)
            content = re.sub(r'DB_NAME=.*', f'DB_NAME={database}', content)
            content = re.sub(r'DB_USER=.*', f'DB_USER={user}', content)
            content = re.sub(r'DB_PASSWORD=.*', f'DB_PASSWORD={password}', content)
            
            # Guardar archivo
            with open(env_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ Archivo .env actualizado correctamente")
            print()
            print("🚀 Ya puedes ejecutar la aplicación con:")
            print("   python main.py")
            
        else:
            print("❌ Archivo .env no encontrado")
            return False
    else:
        print(f"❌ {message}")
        print()
        print("💡 Verifica que:")
        print("   - El servidor MySQL/MariaDB esté ejecutándose")
        print("   - Las credenciales sean correctas")
        print("   - La base de datos XUI One esté instalada")
        print("   - Los permisos de usuario sean suficientes")
        return False
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️  Configuración cancelada por el usuario")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ Error inesperado: {str(e)}")
        sys.exit(1)
