"""
Test del Content Browser
========================

Script para probar específicamente el nuevo panel de exploración de contenido.
"""

import asyncio
import sys
import os

# Agregar el directorio raíz al path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.settings import Settings
from core.database_manager import DatabaseManager

async def test_content_browser():
    """Probar funcionalidades del content browser"""
    print("🧪 Test del Content Browser")
    print("=" * 50)
    
    try:
        # Inicializar componentes
        settings = Settings()
        db_manager = DatabaseManager(settings)
        
        # Probar conexión
        print("1. Probando conexión...")
        if await db_manager.test_connection():
            print("✅ Conexión exitosa")
        else:
            print("❌ Error de conexión")
            return
        
        # Probar obtener streams generales
        print("\n2. Obteniendo streams generales...")
        streams = await db_manager.get_streams(limit=10)
        print(f"📺 Streams encontrados: {len(streams)}")
        
        for i, stream in enumerate(streams[:3]):
            print(f"   {i+1}. {stream.get('stream_display_name', 'Sin nombre')} (Tipo: {stream.get('type')}, ID: {stream.get('id')})")
        
        # Probar obtener películas
        print("\n3. Obteniendo películas...")
        movies = await db_manager.get_movies(limit=10)
        print(f"🎬 Películas encontradas: {len(movies)}")
        
        for i, movie in enumerate(movies[:3]):
            print(f"   {i+1}. {movie.get('stream_display_name', 'Sin nombre')} (Año: {movie.get('year')}, Rating: {movie.get('rating')})")
        
        # Probar obtener series
        print("\n4. Obteniendo series...")
        series = await db_manager.get_tv_shows(limit=10)
        print(f"📺 Series encontradas: {len(series)}")
        
        for i, serie in enumerate(series[:3]):
            print(f"   {i+1}. {serie.get('title', 'Sin nombre')} (Año: {serie.get('year')}, Rating: {serie.get('rating')})")
        
        # Probar obtener TV en vivo
        print("\n5. Obteniendo canales de TV en vivo...")
        live_streams = await db_manager.get_live_streams(limit=10)
        print(f"📡 Canales en vivo encontrados: {len(live_streams)}")
        
        for i, channel in enumerate(live_streams[:3]):
            print(f"   {i+1}. {channel.get('stream_display_name', 'Sin nombre')} (Categoría: {channel.get('category_name', 'Sin categoría')})")
        
        # Probar búsqueda
        print("\n6. Probando búsqueda...")
        search_results = await db_manager.search_streams("Batman", limit=5)
        print(f"🔍 Resultados para 'Batman': {len(search_results)}")
        
        for i, result in enumerate(search_results):
            print(f"   {i+1}. {result.get('stream_display_name', 'Sin nombre')} (Tipo: {result.get('type')})")
        
        # Probar estadísticas
        print("\n7. Obteniendo estadísticas...")
        stats = await db_manager.get_content_stats()
        print("📊 Estadísticas:")
        print(f"   - Total streams: {stats.get('total_streams', 0)}")
        print(f"   - Películas: {stats.get('total_movies', 0)}")
        print(f"   - Series: {stats.get('total_series', 0)}")
        print(f"   - TV en vivo: {stats.get('total_live', 0)}")
        print(f"   - Con TMDB: {stats.get('with_tmdb', 0)}")
        print(f"   - Sin TMDB: {stats.get('without_tmdb', 0)}")
        
        # Probar contenido sin TMDB
        print("\n8. Contenido sin información TMDB...")
        no_tmdb = await db_manager.get_streams_without_tmdb(limit=5)
        print(f"❓ Contenido sin TMDB: {len(no_tmdb)}")
        
        for i, item in enumerate(no_tmdb):
            print(f"   {i+1}. {item.get('stream_display_name', 'Sin nombre')} (ID: {item.get('id')})")
        
        # Probar duplicados
        print("\n9. Detectando duplicados...")
        duplicates = await db_manager.get_duplicate_streams()
        print(f"🔄 Duplicados encontrados: {len(duplicates)}")
        
        for i, dup in enumerate(duplicates[:3]):
            print(f"   {i+1}. '{dup.get('stream_display_name', 'Sin nombre')}' aparece {dup.get('count', 0)} veces")
        
        print("\n✅ Test del Content Browser completado exitosamente")
        print("\n🎉 El Content Browser está listo para mostrar datos reales de XUI One")
        
    except Exception as e:
        print(f"\n❌ Error durante el test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        if 'db_manager' in locals():
            await db_manager.close()
    
    return True

if __name__ == "__main__":
    success = asyncio.run(test_content_browser())
    if success:
        print("\n🚀 El Content Browser puede mostrar datos reales de XUI One")
        print("   - Películas con información TMDB")
        print("   - Series con temporadas y episodios")
        print("   - Canales de TV en vivo")
        print("   - Búsqueda en tiempo real")
        print("   - Estadísticas detalladas")
        print("   - Detección de duplicados")
    else:
        print("\n💥 Test falló")
        print("Revisa la configuración de la base de datos")
        sys.exit(1)
