<?php
/**
 * Simple Database Manager for IPTV XUI One Content Manager
 * =======================================================
 * 
 * Simplified version to avoid SQL syntax errors and long threads
 */

class SimpleDatabaseManager {
    private $pdo;
    private $config;
    
    public function __construct() {
        $this->config = DB_CONFIG;
        $this->connect();
    }
    
    /**
     * Establish database connection with optimized settings
     */
    private function connect() {
        try {
            $dsn = "mysql:host={$this->config['host']};port={$this->config['port']};dbname={$this->config['database']};charset={$this->config['charset']}";
            
            // Optimized PDO options to prevent long threads
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::ATTR_TIMEOUT => 10,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
            ];
            
            $this->pdo = new PDO($dsn, $this->config['username'], $this->config['password'], $options);
            
            // Set session timeouts to prevent long threads
            $this->pdo->exec("SET SESSION wait_timeout = 30");
            $this->pdo->exec("SET SESSION interactive_timeout = 30");
            $this->pdo->exec("SET SESSION net_read_timeout = 10");
            $this->pdo->exec("SET SESSION net_write_timeout = 10");
            
        } catch (PDOException $e) {
            throw new Exception("Database connection failed: " . $e->getMessage());
        }
    }
    
    /**
     * Test database connection
     */
    public function testConnection() {
        try {
            $stmt = $this->pdo->query("SELECT 1");
            return $stmt !== false;
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Get basic content statistics
     */
    public function getContentStats() {
        try {
            $sql = "SELECT 
                        COUNT(*) as total_streams,
                        SUM(CASE WHEN type = 1 THEN 1 ELSE 0 END) as live_tv,
                        SUM(CASE WHEN type = 2 THEN 1 ELSE 0 END) as movies,
                        SUM(CASE WHEN type = 3 THEN 1 ELSE 0 END) as series,
                        SUM(CASE WHEN type = 2 AND movie_symlink = 1 THEN 1 ELSE 0 END) as symlink_movies,
                        SUM(CASE WHEN type = 2 AND direct_source = 1 THEN 1 ELSE 0 END) as direct_movies
                    FROM streams";
            
            $stmt = $this->pdo->query($sql);
            return $stmt->fetch();
            
        } catch (Exception $e) {
            return [
                'total_streams' => 0,
                'live_tv' => 0,
                'movies' => 0,
                'series' => 0,
                'symlink_movies' => 0,
                'direct_movies' => 0
            ];
        }
    }
    
    /**
     * Get quality statistics
     */
    public function getQualityStats() {
        try {
            $sql = "SELECT 
                        SUM(CASE WHEN stream_display_name LIKE '%4K%' OR stream_display_name LIKE '%2160p%' THEN 1 ELSE 0 END) as content_4k,
                        SUM(CASE WHEN stream_display_name LIKE '%60fps%' OR stream_display_name LIKE '%60FPS%' THEN 1 ELSE 0 END) as content_60fps,
                        SUM(CASE WHEN stream_display_name LIKE '%HDR%' THEN 1 ELSE 0 END) as content_hdr,
                        SUM(CASE WHEN stream_display_name LIKE '%1080p%' OR stream_display_name LIKE '%FHD%' THEN 1 ELSE 0 END) as content_fhd,
                        SUM(CASE WHEN stream_display_name LIKE '%720p%' OR stream_display_name LIKE '%HD%' THEN 1 ELSE 0 END) as content_hd
                    FROM streams 
                    WHERE type = 2";
            
            $stmt = $this->pdo->query($sql);
            return $stmt->fetch();
            
        } catch (Exception $e) {
            return [
                'content_4k' => 0,
                'content_60fps' => 0,
                'content_hdr' => 0,
                'content_fhd' => 0,
                'content_hd' => 0
            ];
        }
    }
    
    /**
     * Get movies with simple pagination
     */
    public function getMovies($limit = 50, $offset = 0, $search = '') {
        try {
            $sql = "SELECT s.id, s.stream_display_name, s.type, s.added, s.movie_symlink, s.direct_source, s.tmdb_id
                    FROM streams s 
                    WHERE s.type = 2";
            
            $params = [];
            
            if (!empty($search)) {
                $sql .= " AND s.stream_display_name LIKE ?";
                $params[] = '%' . $search . '%';
            }
            
            $sql .= " ORDER BY s.added DESC LIMIT ? OFFSET ?";
            $params[] = $limit;
            $params[] = $offset;
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * Get content missing TMDB data
     */
    public function getMissingTMDBContent($limit = 100, $offset = 0) {
        try {
            $sql = "SELECT id, stream_display_name, type, added
                    FROM streams 
                    WHERE type = 2 AND (tmdb_id IS NULL OR tmdb_id = 0)
                    ORDER BY added DESC 
                    LIMIT ? OFFSET ?";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$limit, $offset]);
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * Get categories
     */
    public function getCategories() {
        try {
            $sql = "SELECT id, category_name, category_type 
                    FROM streams_categories 
                    ORDER BY category_name";
            
            $stmt = $this->pdo->query($sql);
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * Check if required tables exist
     */
    public function validateTables() {
        $requiredTables = ['streams', 'streams_categories'];
        $missingTables = [];
        
        foreach ($requiredTables as $table) {
            try {
                $stmt = $this->pdo->query("SELECT 1 FROM `$table` LIMIT 1");
                if (!$stmt) {
                    $missingTables[] = $table;
                }
            } catch (Exception $e) {
                $missingTables[] = $table;
            }
        }
        
        return empty($missingTables);
    }
    
    /**
     * Get recent content
     */
    public function getRecentContent($limit = 10) {
        try {
            $sql = "SELECT id, stream_display_name, type, added, movie_symlink
                    FROM streams
                    ORDER BY added DESC
                    LIMIT ?";

            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$limit]);
            return $stmt->fetchAll();

        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * Get recent additions (alias for getRecentContent)
     */
    public function getRecentAdditions($limit = 10) {
        return $this->getRecentContent($limit);
    }

    /**
     * Get popular content
     */
    public function getPopularContent($limit = 10) {
        try {
            $sql = "SELECT id, stream_display_name, type, added, movie_symlink
                    FROM streams
                    WHERE type = 2
                    ORDER BY RAND()
                    LIMIT ?";

            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$limit]);
            return $stmt->fetchAll();

        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * Get duplicate analysis
     */
    public function getDuplicateAnalysis($limit = 50, $offset = 0) {
        try {
            $sql = "SELECT 
                        stream_display_name,
                        COUNT(*) as duplicate_count,
                        GROUP_CONCAT(id) as stream_ids,
                        SUM(CASE WHEN movie_symlink = 1 THEN 1 ELSE 0 END) as symlink_count,
                        SUM(CASE WHEN direct_source = 1 THEN 1 ELSE 0 END) as direct_count
                    FROM streams 
                    WHERE type = 2 
                    GROUP BY stream_display_name 
                    HAVING COUNT(*) > 1
                    ORDER BY duplicate_count DESC
                    LIMIT ? OFFSET ?";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$limit, $offset]);
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * Close connection
     */
    public function __destruct() {
        $this->pdo = null;
    }
}
