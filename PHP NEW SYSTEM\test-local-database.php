<?php
/**
 * Test Local Database Connection
 * ==============================
 * 
 * Prueba la conexión y funcionalidad de la base de datos SQLite local
 */

echo "<h1>Prueba de Base de Datos Local SQLite</h1>\n";

try {
    require_once __DIR__ . '/core/LocalDatabaseManager.php';
    
    $db = new LocalDatabaseManager();
    
    echo "<h2>✅ Conexión Establecida</h2>\n";
    
    // Información de la base de datos
    $db_info = $db->getDatabaseInfo();
    echo "<h3>Información de Base de Datos:</h3>\n";
    echo "<ul>\n";
    echo "<li><strong>Tipo:</strong> " . $db_info['type'] . "</li>\n";
    echo "<li><strong>Ubicación:</strong> " . $db_info['path'] . "</li>\n";
    echo "<li><strong>Tamaño:</strong> " . round($db_info['size'] / 1024, 2) . " KB</li>\n";
    echo "<li><strong>Escribible:</strong> " . ($db_info['writable'] ? 'Sí' : 'No') . "</li>\n";
    echo "</ul>\n";
    
    // Estadísticas de contenido
    echo "<h2>📊 Estadísticas de Contenido</h2>\n";
    $stats = $db->getContentStats();
    echo "<ul>\n";
    echo "<li><strong>Total Streams:</strong> " . number_format($stats['total_streams']) . "</li>\n";
    echo "<li><strong>Películas:</strong> " . number_format($stats['movies']) . "</li>\n";
    echo "<li><strong>Series:</strong> " . number_format($stats['series']) . "</li>\n";
    echo "<li><strong>TV en Vivo:</strong> " . number_format($stats['live_tv']) . "</li>\n";
    echo "<li><strong>Contenido Symlink:</strong> " . number_format($stats['symlink_movies']) . "</li>\n";
    echo "<li><strong>Contenido Direct Source:</strong> " . number_format($stats['direct_movies']) . "</li>\n";
    echo "</ul>\n";
    
    // Estadísticas de calidad
    echo "<h2>🎬 Estadísticas de Calidad</h2>\n";
    $quality = $db->getQualityStats();
    echo "<ul>\n";
    echo "<li><strong>Contenido 4K:</strong> " . number_format($quality['content_4k']) . "</li>\n";
    echo "<li><strong>Contenido 60fps:</strong> " . number_format($quality['content_60fps']) . "</li>\n";
    echo "<li><strong>Contenido HDR:</strong> " . number_format($quality['content_hdr']) . "</li>\n";
    echo "<li><strong>Contenido FHD:</strong> " . number_format($quality['content_fhd']) . "</li>\n";
    echo "<li><strong>Contenido HD:</strong> " . number_format($quality['content_hd']) . "</li>\n";
    echo "</ul>\n";
    
    // Contenido reciente
    echo "<h2>🕒 Contenido Reciente</h2>\n";
    $recent = $db->getRecentAdditions(5);
    echo "<ul>\n";
    foreach ($recent as $item) {
        echo "<li><strong>" . htmlspecialchars($item['stream_display_name']) . "</strong> - Agregado: " . date('d/m/Y H:i', $item['added']) . "</li>\n";
    }
    echo "</ul>\n";
    
    // Contenido popular
    echo "<h2>⭐ Contenido Popular</h2>\n";
    $popular = $db->getPopularContent(5);
    echo "<ul>\n";
    foreach ($popular as $item) {
        echo "<li><strong>" . htmlspecialchars($item['stream_display_name']) . "</strong> - Rating: " . $item['rating'] . "/10</li>\n";
    }
    echo "</ul>\n";
    
    // Contenido 4K
    echo "<h2>💎 Contenido 4K</h2>\n";
    $content_4k = $db->get4KContent(5);
    echo "<ul>\n";
    foreach ($content_4k as $item) {
        echo "<li><strong>" . htmlspecialchars($item['stream_display_name']) . "</strong></li>\n";
    }
    echo "</ul>\n";
    
    // Categorías
    echo "<h2>📂 Categorías</h2>\n";
    $categories = $db->getCategories();
    echo "<ul>\n";
    foreach ($categories as $cat) {
        echo "<li><strong>" . htmlspecialchars($cat['category_name']) . "</strong> (" . $cat['category_type'] . ")</li>\n";
    }
    echo "</ul>\n";
    
    // Prueba de búsqueda
    echo "<h2>🔍 Prueba de Búsqueda</h2>\n";
    $search_results = $db->searchContent('Avatar', 3);
    echo "<p><strong>Búsqueda: 'Avatar'</strong></p>\n";
    echo "<ul>\n";
    foreach ($search_results as $item) {
        echo "<li>" . htmlspecialchars($item['stream_display_name']) . "</li>\n";
    }
    echo "</ul>\n";
    
    // Estadísticas del gestor
    echo "<h2>📈 Estadísticas del Gestor</h2>\n";
    $manager_stats = $db->getStats();
    echo "<ul>\n";
    echo "<li><strong>Consultas Ejecutadas:</strong> " . $manager_stats['queries_executed'] . "</li>\n";
    echo "<li><strong>Cache Hits:</strong> " . $manager_stats['cache_hits'] . "</li>\n";
    echo "<li><strong>Cache Misses:</strong> " . $manager_stats['cache_misses'] . "</li>\n";
    echo "</ul>\n";
    
    echo "<h2>🎉 ¡Todas las Pruebas Exitosas!</h2>\n";
    echo "<p>La base de datos local está funcionando perfectamente.</p>\n";
    
    echo "<h3>Acciones Disponibles:</h3>\n";
    echo "<ul>\n";
    echo "<li><a href='sistema-local.php' style='color: #3b82f6; text-decoration: none;'>🚀 Lanzar Sistema Completo</a></li>\n";
    echo "<li><a href='create-local-database.php' style='color: #10b981; text-decoration: none;'>🔄 Recrear Base de Datos</a></li>\n";
    echo "<li><a href='demo-interface.php' style='color: #f59e0b; text-decoration: none;'>👁️ Vista Previa</a></li>\n";
    echo "</ul>\n";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</h2>\n";
    echo "<p>Posibles soluciones:</p>\n";
    echo "<ul>\n";
    echo "<li><a href='create-local-database.php'>Crear base de datos local</a></li>\n";
    echo "<li>Verificar permisos de escritura en el directorio</li>\n";
    echo "<li>Asegurarse de que SQLite esté disponible</li>\n";
    echo "</ul>\n";
}
?>
