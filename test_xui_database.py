#!/usr/bin/env python3
"""
Test de conexión a base de datos XUI One
=======================================

Script para probar la conexión a la base de datos XUI One real
y verificar que podemos leer datos de las tablas principales.
"""

import asyncio
import sys
import os

# Añadir el directorio actual al path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.settings import Settings
from core.database_manager import DatabaseManager
from utils.xui_helpers import parse_movie_properties, parse_stream_source, parse_category_ids

async def test_xui_database():
    """Probar conexión y lectura de datos XUI One"""
    
    print("🔍 Probando conexión a base de datos XUI One")
    print("=" * 50)
    
    # Inicializar configuración
    settings = Settings()
    db_manager = DatabaseManager(settings)
    
    try:
        # Probar conexión básica
        print("1. Probando conexión básica...")
        connection_ok = await db_manager.test_connection()
        if connection_ok:
            print("✅ Conexión exitosa")
        else:
            print("❌ Error en conexión")
            return False
        
        # Verificar tablas
        print("\n2. Verificando tablas requeridas...")
        tables_ok = await db_manager.verify_tables()
        if tables_ok:
            print("✅ Todas las tablas existen")
        else:
            print("❌ Algunas tablas faltan")
            return False
        
        # Probar lectura de estadísticas
        print("\n3. Obteniendo estadísticas de contenido...")
        stats = await db_manager.get_content_stats()
        print(f"📊 Estadísticas:")
        print(f"   - Total streams: {stats.get('total_streams', 0)}")
        print(f"   - Películas: {stats.get('total_movies', 0)}")
        print(f"   - Series: {stats.get('total_series', 0)}")
        print(f"   - Live TV: {stats.get('total_live', 0)}")
        print(f"   - Con TMDB: {stats.get('with_tmdb', 0)}")
        print(f"   - Sin TMDB: {stats.get('without_tmdb', 0)}")
        
        # Probar lectura de categorías
        print("\n4. Obteniendo categorías...")
        categories = await db_manager.get_categories()
        print(f"📂 Categorías encontradas: {len(categories)}")
        
        # Mostrar algunas categorías
        for i, cat in enumerate(categories[:5]):
            print(f"   - {cat.get('category_name', 'Sin nombre')} (ID: {cat.get('id', 'N/A')}, Tipo: {cat.get('category_type', 'N/A')})")
        
        if len(categories) > 5:
            print(f"   ... y {len(categories) - 5} más")
        
        # Probar lectura de películas
        print("\n5. Obteniendo películas...")
        movies = await db_manager.get_movies(limit=3)
        print(f"🎬 Películas encontradas: {len(movies)}")
        
        for i, movie in enumerate(movies):
            print(f"\n   Película {i+1}:")
            print(f"   - ID: {movie.get('id')}")
            print(f"   - Nombre: {movie.get('stream_display_name', 'Sin nombre')}")
            print(f"   - Tipo: {movie.get('type')}")
            print(f"   - TMDB ID: {movie.get('tmdb_id', 'N/A')}")
            print(f"   - Año: {movie.get('year', 'N/A')}")
            print(f"   - Rating: {movie.get('rating', 'N/A')}")
            
            # Parsear movie_properties si existe
            if movie.get('movie_properties'):
                properties = parse_movie_properties(movie.get('movie_properties'))
                if properties:
                    print(f"   - Título TMDB: {properties.get('title', 'N/A')}")
                    print(f"   - Descripción: {properties.get('description', 'N/A')[:100]}...")
                    print(f"   - Director: {properties.get('director', 'N/A')}")
                    print(f"   - Género: {properties.get('genre', 'N/A')}")
        
        # Probar lectura de series
        print("\n6. Obteniendo series...")
        series = await db_manager.get_series(limit=3)
        print(f"📺 Series encontradas: {len(series)}")
        
        for i, serie in enumerate(series):
            print(f"\n   Serie {i+1}:")
            print(f"   - ID: {serie.get('id')}")
            print(f"   - Título: {serie.get('title', 'Sin título')}")
            print(f"   - TMDB ID: {serie.get('tmdb_id', 'N/A')}")
            print(f"   - Año: {serie.get('year', 'N/A')}")
            print(f"   - Rating: {serie.get('rating', 'N/A')}")
            print(f"   - Temporadas: {serie.get('seasons', 'N/A')}")
        
        # Probar búsqueda
        print("\n7. Probando búsqueda...")
        search_results = await db_manager.search_streams("Batman", limit=5)
        print(f"🔍 Resultados de búsqueda para 'Batman': {len(search_results)}")
        
        for i, result in enumerate(search_results):
            print(f"   {i+1}. {result.get('stream_display_name', 'Sin nombre')} (ID: {result.get('id')})")
        
        # Probar contenido sin TMDB
        print("\n8. Contenido sin información TMDB...")
        unmatched = await db_manager.get_streams_without_tmdb(limit=5)
        print(f"❓ Contenido sin TMDB: {len(unmatched)}")
        
        for i, item in enumerate(unmatched):
            print(f"   {i+1}. {item.get('stream_display_name', 'Sin nombre')} (ID: {item.get('id')})")
        
        # Probar duplicados
        print("\n9. Detectando duplicados...")
        duplicates = await db_manager.get_duplicate_streams()
        print(f"🔄 Duplicados encontrados: {len(duplicates)}")

        for i, dup in enumerate(duplicates[:3]):
            print(f"   {i+1}. '{dup.get('stream_display_name', 'Sin nombre')}' aparece {dup.get('count', 0)} veces")
        
        print("\n✅ Todas las pruebas completadas exitosamente")
        return True
        
    except Exception as e:
        print(f"❌ Error durante las pruebas: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        await db_manager.close()

def main():
    """Función principal"""
    print("IPTV XUI One - Test de Base de Datos")
    print("====================================")
    
    # Ejecutar pruebas
    result = asyncio.run(test_xui_database())
    
    if result:
        print("\n🎉 Test completado exitosamente")
        print("La base de datos XUI One está lista para usar")
    else:
        print("\n💥 Test falló")
        print("Revisa la configuración de la base de datos")
        sys.exit(1)

if __name__ == "__main__":
    main()
