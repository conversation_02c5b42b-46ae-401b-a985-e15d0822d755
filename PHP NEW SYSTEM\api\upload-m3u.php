<?php
/**
 * M3U Upload API Endpoint
 * ======================
 *
 * Handles M3U file uploads and processing
 */

// Include path management first
require_once __DIR__ . '/../config/paths.php';

// Initialize session management
require_once getConfigPath('session.php');

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

require_once getCorePath('ContentManager.php');
require_once getConfigPath('settings.php');

try {
    // Initialize content manager
    $contentManager = new ContentManager();
    
    // Check if files were uploaded
    if (!isset($_FILES['m3u_files']) || empty($_FILES['m3u_files']['name'][0])) {
        throw new Exception('No files uploaded');
    }
    
    // Get upload options
    $options = [
        'enrich_tmdb' => isset($_POST['enrich_tmdb']) && $_POST['enrich_tmdb'] === 'true',
        'skip_duplicates' => isset($_POST['skip_duplicates']) && $_POST['skip_duplicates'] === 'true',
        'auto_categorize' => isset($_POST['auto_categorize']) && $_POST['auto_categorize'] === 'true',
        'batch_processing' => isset($_POST['batch_processing']) && $_POST['batch_processing'] === 'true',
        'batch_size' => (int)($_POST['batch_size'] ?? 100)
    ];
    
    // Process uploaded files
    $results = [];
    $uploadDir = __DIR__ . '/../uploads/';
    
    // Ensure upload directory exists
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    // Process each uploaded file
    $fileCount = count($_FILES['m3u_files']['name']);
    
    for ($i = 0; $i < $fileCount; $i++) {
        $fileName = $_FILES['m3u_files']['name'][$i];
        $fileTmpName = $_FILES['m3u_files']['tmp_name'][$i];
        $fileSize = $_FILES['m3u_files']['size'][$i];
        $fileError = $_FILES['m3u_files']['error'][$i];
        
        // Skip if file has errors
        if ($fileError !== UPLOAD_ERR_OK) {
            $results[] = [
                'file' => $fileName,
                'success' => false,
                'error' => 'Upload error: ' . $fileError
            ];
            continue;
        }
        
        // Validate file size
        if ($fileSize > UPLOAD_CONFIG['max_file_size']) {
            $results[] = [
                'file' => $fileName,
                'success' => false,
                'error' => 'File too large'
            ];
            continue;
        }
        
        // Validate file extension
        $fileExt = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
        if (!in_array($fileExt, UPLOAD_CONFIG['allowed_extensions'])) {
            $results[] = [
                'file' => $fileName,
                'success' => false,
                'error' => 'Invalid file type'
            ];
            continue;
        }
        
        // Move uploaded file
        $targetPath = $uploadDir . uniqid() . '_' . $fileName;
        if (!move_uploaded_file($fileTmpName, $targetPath)) {
            $results[] = [
                'file' => $fileName,
                'success' => false,
                'error' => 'Failed to save file'
            ];
            continue;
        }
        
        // Process M3U file
        try {
            $result = $contentManager->processM3UFile($targetPath, $options);
            
            $results[] = [
                'file' => $fileName,
                'success' => $result['success'],
                'stats' => $result['stats'] ?? [],
                'message' => $result['message'] ?? '',
                'error' => $result['error'] ?? null
            ];
            
            // Clean up uploaded file
            unlink($targetPath);
            
        } catch (Exception $e) {
            $results[] = [
                'file' => $fileName,
                'success' => false,
                'error' => $e->getMessage()
            ];
            
            // Clean up uploaded file
            if (file_exists($targetPath)) {
                unlink($targetPath);
            }
        }
    }
    
    // Calculate overall statistics
    $totalFiles = count($results);
    $successfulFiles = count(array_filter($results, function($r) { return $r['success']; }));
    $totalProcessed = array_sum(array_column(array_column($results, 'stats'), 'processed_items'));
    $totalEnriched = array_sum(array_column(array_column($results, 'stats'), 'enriched_items'));
    $totalErrors = array_sum(array_column(array_column($results, 'stats'), 'errors'));
    
    // Return response
    echo json_encode([
        'success' => true,
        'summary' => [
            'total_files' => $totalFiles,
            'successful_files' => $successfulFiles,
            'failed_files' => $totalFiles - $successfulFiles,
            'total_processed' => $totalProcessed,
            'total_enriched' => $totalEnriched,
            'total_errors' => $totalErrors
        ],
        'results' => $results,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}
?>
