/**
 * Pages CSS - IPTV XUI One Content Manager
 * =======================================
 * 
 * Unified styles for all pages with proper responsive design
 */

/* ======================
   GLOBAL PAGE STYLES
   ====================== */

.page-container {
    width: 100%;
    max-width: 100%;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Grid Systems */
.grid-1 { grid-template-columns: 1fr; }
.grid-2 { grid-template-columns: repeat(2, 1fr); }
.grid-3 { grid-template-columns: repeat(3, 1fr); }
.grid-4 { grid-template-columns: repeat(4, 1fr); }

.grid-auto-fit-sm { grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); }
.grid-auto-fit-md { grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); }
.grid-auto-fit-lg { grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); }

.grid {
    display: grid;
    gap: 1.5rem;
    width: 100%;
    box-sizing: border-box;
}

/* ======================
   DASHBOARD STYLES
   ====================== */

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
    width: 100%;
}

.stat-card {
    background: linear-gradient(135deg, var(--dark-surface), var(--dark-bg));
    border: 1px solid var(--dark-border);
    border-radius: 1rem;
    padding: 1.5rem;
    position: relative;
    overflow: hidden;
    transition: var(--transition);
    min-height: 160px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 100%;
    box-sizing: border-box;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
    border-color: var(--primary-color);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.stat-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
    width: 100%;
}

.stat-title {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    line-height: 1;
}

.stat-change {
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.stat-change.positive { color: var(--success-color); }
.stat-change.negative { color: var(--error-color); }

/* Quick Actions */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
    width: 100%;
}

.action-card {
    background: var(--dark-surface);
    border: 1px solid var(--dark-border);
    border-radius: 1rem;
    padding: 1.5rem;
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
    text-decoration: none;
    color: inherit;
    min-height: 140px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    box-sizing: border-box;
}

.action-card:hover {
    transform: translateY(-2px);
    border-color: var(--primary-color);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
}

.action-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: white;
    margin: 0 auto 0.75rem;
    flex-shrink: 0;
}

.action-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
    font-size: 0.95rem;
}

.action-description {
    font-size: 0.8rem;
    color: var(--text-secondary);
    line-height: 1.3;
}

/* Content Grid */
.content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    width: 100%;
}

.content-list {
    background: var(--dark-surface);
    border: 1px solid var(--dark-border);
    border-radius: 1rem;
    padding: 2rem;
    width: 100%;
    box-sizing: border-box;
}

.content-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--dark-border);
}

.content-list-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
}

.content-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid var(--dark-border);
    transition: var(--transition);
}

.content-item:last-child {
    border-bottom: none;
}

.content-item:hover {
    background: rgba(59, 130, 246, 0.05);
    border-radius: 0.5rem;
    margin: 0 -0.5rem;
    padding: 1rem 0.5rem;
}

.content-poster {
    width: 48px;
    height: 72px;
    background: var(--dark-border);
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    font-size: 1.5rem;
    flex-shrink: 0;
}

.content-info {
    flex: 1;
}

.content-title {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.content-meta {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

/* Charts */
.chart-container {
    background: var(--dark-surface);
    border: 1px solid var(--dark-border);
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
    width: 100%;
    box-sizing: border-box;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.chart-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.chart-canvas {
    max-height: 400px;
    width: 100%;
}

/* ======================
   M3U MANAGER STYLES
   ====================== */

.upload-section {
    background: var(--dark-surface);
    border: 1px solid var(--dark-border);
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
    width: 100%;
    box-sizing: border-box;
}

.upload-area {
    border: 2px dashed var(--dark-border);
    border-radius: 1rem;
    padding: 3rem 2rem;
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    width: 100%;
    box-sizing: border-box;
}

.upload-area:hover {
    border-color: var(--primary-color);
    background: rgba(59, 130, 246, 0.05);
}

.upload-area.dragover {
    border-color: var(--primary-color);
    background: rgba(59, 130, 246, 0.1);
    transform: scale(1.02);
}

.upload-icon {
    font-size: 4rem;
    color: var(--text-secondary);
    margin-bottom: 1rem;
    transition: var(--transition);
}

.upload-area:hover .upload-icon {
    color: var(--primary-color);
    transform: scale(1.1);
}

/* ======================
   ANALYTICS STYLES
   ====================== */

.analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
    width: 100%;
}

.chart-card {
    background: var(--dark-surface);
    border: 1px solid var(--dark-border);
    border-radius: 1rem;
    padding: 2rem;
    position: relative;
    overflow: hidden;
    width: 100%;
    box-sizing: border-box;
}

.chart-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

/* ======================
   SETTINGS STYLES
   ====================== */

.settings-grid {
    display: grid;
    grid-template-columns: 250px 1fr;
    gap: 2rem;
    width: 100%;
}

.settings-nav {
    background: var(--dark-surface);
    border: 1px solid var(--dark-border);
    border-radius: 1rem;
    padding: 1.5rem;
    height: fit-content;
    width: 100%;
    box-sizing: border-box;
}

.settings-nav-item {
    display: block;
    padding: 1rem;
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: 0.5rem;
    margin-bottom: 0.5rem;
    transition: var(--transition);
    width: 100%;
    box-sizing: border-box;
}

.settings-nav-item:hover {
    background: rgba(59, 130, 246, 0.1);
    color: var(--primary-color);
}

.settings-nav-item.active {
    background: var(--primary-color);
    color: white;
}

.settings-content {
    background: var(--dark-surface);
    border: 1px solid var(--dark-border);
    border-radius: 1rem;
    padding: 2rem;
    width: 100%;
    box-sizing: border-box;
}

/* ======================
   ADDITIONAL STYLES
   ====================== */

/* Settings specific styles */
.settings-section {
    display: none;
}

.settings-section.active {
    display: block;
}

.section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--dark-border);
}

.form-group {
    margin-bottom: 2rem;
}

.form-label {
    display: block;
    color: var(--text-primary);
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.form-input {
    width: 100%;
    background: var(--dark-bg);
    border: 1px solid var(--dark-border);
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
    color: var(--text-primary);
    transition: var(--transition);
    box-sizing: border-box;
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-description {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--dark-border);
    transition: var(--transition);
    border-radius: 34px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: var(--transition);
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: var(--primary-color);
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

.settings-actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--dark-border);
    flex-wrap: wrap;
}

.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 1rem;
}

.status-indicator.success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.status-indicator.error {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
}

.status-indicator.warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.config-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.config-card {
    background: var(--dark-bg);
    border: 1px solid var(--dark-border);
    border-radius: 0.75rem;
    padding: 1.5rem;
    width: 100%;
    box-sizing: border-box;
}

.config-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.config-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: white;
    flex-shrink: 0;
}

.config-title {
    font-weight: 600;
    color: var(--text-primary);
}

.config-description {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin-bottom: 1rem;
}

/* Upload area styles */
.upload-text {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.upload-subtext {
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

.file-input {
    display: none;
}

.upload-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-top: 2rem;
}

.option-card {
    background: var(--dark-bg);
    border: 1px solid var(--dark-border);
    border-radius: 0.75rem;
    padding: 1.5rem;
    transition: var(--transition);
    width: 100%;
    box-sizing: border-box;
}

.option-card:hover {
    border-color: var(--primary-color);
}

.option-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.option-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
}

.option-title {
    font-weight: 600;
    color: var(--text-primary);
}

.option-description {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin-bottom: 1rem;
}

.checkbox-wrapper {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.checkbox {
    width: 18px;
    height: 18px;
    border: 2px solid var(--dark-border);
    border-radius: 4px;
    background: var(--dark-bg);
    cursor: pointer;
    transition: var(--transition);
}

.checkbox:checked {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

/* Progress section styles */
.progress-section {
    background: var(--dark-surface);
    border: 1px solid var(--dark-border);
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
    display: none;
    width: 100%;
    box-sizing: border-box;
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.progress-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.progress-bar-container {
    background: var(--dark-bg);
    border-radius: 0.5rem;
    height: 12px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 0.5rem;
    transition: width 0.3s ease;
    width: 0%;
}

.progress-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.progress-stat {
    text-align: center;
    padding: 1rem;
    background: var(--dark-bg);
    border-radius: 0.5rem;
}

.progress-stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
}

.progress-stat-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-top: 0.25rem;
}

/* Results section styles */
.results-section {
    background: var(--dark-surface);
    border: 1px solid var(--dark-border);
    border-radius: 1rem;
    padding: 2rem;
    display: none;
    width: 100%;
    box-sizing: border-box;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.results-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.results-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.summary-card {
    background: var(--dark-bg);
    border: 1px solid var(--dark-border);
    border-radius: 0.75rem;
    padding: 1.5rem;
    text-align: center;
}

.summary-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.summary-label {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.export-options {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    margin-top: 2rem;
}

/* Log container styles */
.log-container {
    background: var(--dark-bg);
    border: 1px solid var(--dark-border);
    border-radius: 0.5rem;
    padding: 1rem;
    max-height: 300px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    margin-top: 1rem;
}

.log-entry {
    padding: 0.25rem 0;
    border-bottom: 1px solid var(--dark-border);
}

.log-entry:last-child {
    border-bottom: none;
}

.log-timestamp {
    color: var(--text-secondary);
}

.log-level {
    font-weight: 600;
    margin: 0 0.5rem;
}

.log-level.info { color: var(--primary-color); }
.log-level.success { color: var(--success-color); }
.log-level.warning { color: var(--warning-color); }
.log-level.error { color: var(--error-color); }

.log-message {
    color: var(--text-primary);
}

/* ======================
   ANALYTICS SPECIFIC
   ====================== */

.filters-section {
    background: var(--dark-surface);
    border: 1px solid var(--dark-border);
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
    width: 100%;
    box-sizing: border-box;
}

.filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-label {
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
}

.filter-select {
    background: var(--dark-bg);
    border: 1px solid var(--dark-border);
    border-radius: 0.5rem;
    padding: 0.75rem;
    color: var(--text-primary);
    width: 100%;
    box-sizing: border-box;
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.metric-card {
    background: var(--dark-surface);
    border: 1px solid var(--dark-border);
    border-radius: 1rem;
    padding: 1.5rem;
    text-align: center;
    position: relative;
    overflow: hidden;
    transition: var(--transition);
    width: 100%;
    box-sizing: border-box;
}

.metric-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.metric-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin: 0 auto 1rem;
    color: white;
}

.metric-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.metric-label {
    color: var(--text-secondary);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.metric-change {
    font-size: 0.75rem;
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
}

.metric-change.positive {
    color: var(--success-color);
}

.metric-change.negative {
    color: var(--error-color);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.chart-controls {
    display: flex;
    gap: 0.5rem;
}

.chart-btn {
    padding: 0.5rem;
    background: var(--dark-bg);
    border: 1px solid var(--dark-border);
    border-radius: 0.375rem;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
}

.chart-btn:hover {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.chart-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.chart-container {
    height: 300px;
    position: relative;
}

.insights-section {
    background: var(--dark-surface);
    border: 1px solid var(--dark-border);
    border-radius: 1rem;
    padding: 2rem;
    width: 100%;
    box-sizing: border-box;
}

.insights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.insight-card {
    background: var(--dark-bg);
    border: 1px solid var(--dark-border);
    border-radius: 0.75rem;
    padding: 1.5rem;
    width: 100%;
    box-sizing: border-box;
}

.insight-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.insight-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: white;
    flex-shrink: 0;
}

.insight-title {
    font-weight: 600;
    color: var(--text-primary);
}

.insight-description {
    color: var(--text-secondary);
    line-height: 1.6;
}

.insight-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 1rem 0;
}

/* ======================
   RESPONSIVE DESIGN
   ====================== */

@media (max-width: 1200px) {
    .dashboard-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
    }

    .quick-actions {
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
        gap: 1rem;
    }

    .analytics-grid {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 1.5rem;
    }

    .content-grid {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 1.5rem;
    }
}

@media (max-width: 900px) {
    .content-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .settings-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .analytics-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}

@media (max-width: 768px) {
    .dashboard-grid,
    .quick-actions,
    .analytics-grid,
    .content-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .stat-card,
    .action-card,
    .chart-card,
    .content-list,
    .upload-section {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .chart-container {
        padding: 1rem;
    }

    .upload-area {
        padding: 2rem 1rem;
    }

    .upload-icon {
        font-size: 3rem;
    }

    .stat-value {
        font-size: 1.5rem;
    }

    .action-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .action-title {
        font-size: 0.875rem;
    }

    .action-description {
        font-size: 0.75rem;
    }
}

/* ======================
   UTILITY CLASSES
   ====================== */

.error-message {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid var(--error-color);
    border-radius: 0.5rem;
    padding: 1rem;
    color: var(--error-color);
    text-align: center;
    margin-bottom: 2rem;
    width: 100%;
    box-sizing: border-box;
}

.success-message {
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid var(--success-color);
    border-radius: 0.5rem;
    padding: 1rem;
    color: var(--success-color);
    text-align: center;
    margin-bottom: 2rem;
    width: 100%;
    box-sizing: border-box;
}

.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    width: 100%;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--dark-border);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Fix for overflow issues */
.page-wrapper {
    width: 100%;
    max-width: 100%;
    overflow-x: hidden;
    box-sizing: border-box;
}

.container {
    width: 100%;
    max-width: 100%;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}
