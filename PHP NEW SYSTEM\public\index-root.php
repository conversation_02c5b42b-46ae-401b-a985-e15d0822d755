<?php
/**
 * Root Index File - IPTV XUI One Content Manager
 * ==============================================
 * 
 * This file should be renamed to index.php and placed in the root directory
 * when uploading to hosting. It redirects to the public folder.
 */

// Redirect to public folder
if (file_exists(__DIR__ . '/public/index.php')) {
    header('Location: /public/');
    exit;
} elseif (file_exists(__DIR__ . '/PHP NEW SYSTEM/public/index.php')) {
    header('Location: /PHP NEW SYSTEM/public/');
    exit;
} else {
    // If public folder not found, show simple message
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>IPTV XUI Manager</title>
        <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 2rem; background: #0f172a; color: white; }
            .container { max-width: 600px; margin: 0 auto; background: #1e293b; padding: 2rem; border-radius: 1rem; }
            .btn { display: inline-block; padding: 1rem 2rem; background: #3b82f6; color: white; text-decoration: none; border-radius: 0.5rem; margin: 0.5rem; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🎬 IPTV XUI Manager</h1>
            <p>Welcome! Please navigate to the correct directory:</p>
            <a href="/public/" class="btn">📱 Launch Application</a>
            <a href="/PHP NEW SYSTEM/public/" class="btn">🔧 Alternative Path</a>
        </div>
    </body>
    </html>
    <?php
}
?>
