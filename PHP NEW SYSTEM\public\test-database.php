<?php
/**
 * Database Connection Test - IPTV XUI One Content Manager
 * =======================================================
 * 
 * Tests database connection and displays detailed diagnostics
 */

header('Content-Type: text/html; charset=utf-8');

// Load environment configuration
require_once dirname(__DIR__) . '/config/env.php';
loadEnvironmentVariables();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Connection Test - IPTV XUI Manager</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #0f172a;
            color: #f1f5f9;
            margin: 0;
            padding: 2rem;
            line-height: 1.6;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: #1e293b;
            border-radius: 1rem;
            padding: 2rem;
            border: 1px solid #334155;
        }
        h1 {
            color: #3b82f6;
            text-align: center;
            margin-bottom: 2rem;
            font-size: 2rem;
        }
        .test-section {
            background: #0f172a;
            border: 1px solid #334155;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }
        .test-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #94a3b8;
        }
        .status {
            padding: 0.5rem 1rem;
            border-radius: 0.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
            display: inline-block;
        }
        .status.success {
            background: rgba(16, 185, 129, 0.2);
            color: #10b981;
            border: 1px solid #10b981;
        }
        .status.error {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
            border: 1px solid #ef4444;
        }
        .status.warning {
            background: rgba(245, 158, 11, 0.2);
            color: #f59e0b;
            border: 1px solid #f59e0b;
        }
        .config-item {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            border-bottom: 1px solid #334155;
        }
        .config-item:last-child {
            border-bottom: none;
        }
        .config-label {
            font-weight: 500;
            color: #94a3b8;
        }
        .config-value {
            color: #f1f5f9;
            font-family: monospace;
        }
        .error-details {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid #ef4444;
            border-radius: 0.5rem;
            padding: 1rem;
            margin-top: 1rem;
            font-family: monospace;
            font-size: 0.875rem;
        }
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #3b82f6;
            color: white;
            text-decoration: none;
            border-radius: 0.5rem;
            margin-top: 1rem;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #1e40af;
        }
        .btn.success {
            background: #10b981;
        }
        .btn.success:hover {
            background: #059669;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        .table th, .table td {
            padding: 0.5rem;
            text-align: left;
            border-bottom: 1px solid #334155;
        }
        .table th {
            background: #334155;
            color: #f1f5f9;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Database Connection Test</h1>
        
        <div class="test-section">
            <div class="test-title">📊 Quick Status</div>
            <?php
            $canConnect = false;
            $statusMessage = '';
            $statusClass = 'error';
            
            if (!extension_loaded('pdo_mysql')) {
                $statusMessage = '❌ PDO MySQL extension not installed';
                $statusClass = 'error';
            } else {
                try {
                    $dsn = sprintf(
                        "mysql:host=%s;port=%d;dbname=%s;charset=utf8mb4",
                        env('DB_HOST', '**************'),
                        (int)env('DB_PORT', 3306),
                        env('DB_NAME', 'xui')
                    );
                    
                    $pdo = new PDO(
                        $dsn,
                        env('DB_USER', 'infest84'),
                        env('DB_PASSWORD', 'GZM6hh12zHKgfg34FghtAQ84WxloPRX]SV$fP'),
                        [PDO::ATTR_TIMEOUT => 5]
                    );
                    
                    $canConnect = true;
                    $statusMessage = '✅ Database connection successful!';
                    $statusClass = 'success';
                } catch (Exception $e) {
                    $statusMessage = '❌ Connection failed: ' . $e->getMessage();
                    $statusClass = 'error';
                }
            }
            ?>
            <div class="status <?= $statusClass ?>"><?= $statusMessage ?></div>
        </div>

        <?php if ($canConnect): ?>
            <div class="test-section">
                <div class="test-title">🎉 Success! Database Connected</div>
                <p>Your system can connect to the XUI One database. The application will work with real data.</p>
                <a href="index.php" class="btn success">🚀 Launch Application with Real Data</a>
            </div>
        <?php else: ?>
            <div class="test-section">
                <div class="test-title">🔧 Troubleshooting</div>
                
                <?php if (!extension_loaded('pdo_mysql')): ?>
                    <p><strong>Issue:</strong> PDO MySQL extension is not installed.</p>
                    <p><strong>Solution:</strong></p>
                    <ul>
                        <li><strong>Windows (XAMPP/WAMP):</strong> Uncomment <code>extension=pdo_mysql</code> in php.ini</li>
                        <li><strong>Linux:</strong> <code>sudo apt install php-mysql</code> (Ubuntu/Debian)</li>
                        <li><strong>Linux:</strong> <code>sudo yum install php-mysql</code> (CentOS/RHEL)</li>
                    </ul>
                    <a href="install-extensions.php" class="btn">📖 Detailed Installation Guide</a>
                <?php else: ?>
                    <p><strong>PDO MySQL is installed</strong> but connection failed.</p>
                    <p><strong>Possible issues:</strong></p>
                    <ul>
                        <li>Database server is not running</li>
                        <li>Incorrect credentials in .env file</li>
                        <li>Firewall blocking connection</li>
                        <li>Network connectivity issues</li>
                    </ul>
                    
                    <div class="config-item">
                        <span class="config-label">Host</span>
                        <span class="config-value"><?= env('DB_HOST', 'Not set') ?></span>
                    </div>
                    <div class="config-item">
                        <span class="config-label">Port</span>
                        <span class="config-value"><?= env('DB_PORT', 'Not set') ?></span>
                    </div>
                    <div class="config-item">
                        <span class="config-label">Database</span>
                        <span class="config-value"><?= env('DB_NAME', 'Not set') ?></span>
                    </div>
                    <div class="config-item">
                        <span class="config-label">Username</span>
                        <span class="config-value"><?= env('DB_USER', 'Not set') ?></span>
                    </div>
                <?php endif; ?>
            </div>
            
            <div class="test-section">
                <div class="test-title">📱 Continue with Demo Mode</div>
                <p>While you resolve the database connection, you can still use the application with demo data.</p>
                <a href="index.php" class="btn">📱 Launch Demo Mode</a>
            </div>
        <?php endif; ?>

        <div class="test-section">
            <div class="test-title">🔗 Useful Links</div>
            <a href="check-requirements.php" class="btn">🔍 Check System Requirements</a>
            <a href="install-extensions.php" class="btn">🔧 Installation Guide</a>
            <a href="index.php" class="btn">🏠 Back to Application</a>
        </div>
    </div>
</body>
</html>
