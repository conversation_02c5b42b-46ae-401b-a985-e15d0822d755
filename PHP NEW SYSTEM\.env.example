# IPTV XUI One Content Manager - Environment Configuration
# ========================================================
# 
# Copy this file to .env and configure your settings
# This file contains example values - replace with your actual configuration

# Database Configuration
# ----------------------
DB_HOST=localhost
DB_PORT=3306
DB_NAME=iptv_xui
DB_USER=your_username
DB_PASSWORD=your_password

# TMDB API Configuration
# ----------------------
TMDB_API_KEY=your_tmdb_api_key_here

# Application Settings
# -------------------
APP_DEBUG=false
LOG_LEVEL=INFO
DEFAULT_THEME=dark

# Security Settings
# ----------------
SESSION_LIFETIME=3600
MAX_UPLOAD_SIZE=50M
CSRF_PROTECTION=true

# Performance Settings
# -------------------
ENABLE_CACHING=true
CACHE_TTL=3600
BATCH_SIZE=100

# Hosting Configuration
# --------------------
# Set to true if you're using shared hosting
SHARED_HOSTING=true

# Set your domain/subdomain
APP_URL=https://yourdomain.com

# Timezone
APP_TIMEZONE=UTC
