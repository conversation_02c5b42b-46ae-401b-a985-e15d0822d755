@echo off
cls
echo.
echo ==========================================
echo   IPTV XUI One Content Manager - PHP
echo ==========================================
echo.
echo 🚀 Iniciando servidor local...
echo.

cd /d "%~dp0"

echo 📋 Verificando PHP...
php -v >nul 2>&1
if errorlevel 1 (
    echo ❌ PHP no está instalado o no está en el PATH
    echo.
    echo 💡 Para instalar PHP en Windows:
    echo    1. Descarga PHP desde https://windows.php.net/download/
    echo    2. Extrae los archivos
    echo    3. Añade la carpeta al PATH del sistema
    echo.
    pause
    exit /b 1
)

echo ✅ PHP encontrado
echo.

echo 🔧 Iniciando servidor en puerto 8000...
echo.
echo 📋 URLs disponibles:
echo    🏠 Inicio:      http://localhost:8000/start.php
echo    🔬 Test:        http://localhost:8000/system-test.php
echo    📊 Dashboard:   http://localhost:8000/public/
echo.
echo 💡 Para detener el servidor, presiona Ctrl+C
echo.

REM Esperar un momento y abrir el navegador
timeout /t 2 /nobreak >nul
start http://localhost:8000/start.php

REM Iniciar el servidor PHP
php -S localhost:8000

pause
