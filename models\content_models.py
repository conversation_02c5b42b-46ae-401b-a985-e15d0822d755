"""
Content models for IPTV XUI One Content Manager
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, List, Optional, Any
import json

@dataclass
class Movie:
    """Model for movie data"""
    id: Optional[int] = None
    tmdb_id: Optional[int] = None
    title: str = ""
    original_title: str = ""
    overview: str = ""
    release_date: Optional[datetime] = None
    runtime: Optional[int] = None
    vote_average: float = 0.0
    vote_count: int = 0
    popularity: float = 0.0
    poster_path: str = ""
    backdrop_path: str = ""
    genre_ids: List[int] = field(default_factory=list)
    adult: bool = False
    original_language: str = ""
    status: str = ""
    budget: int = 0
    revenue: int = 0
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'id': self.id,
            'tmdb_id': self.tmdb_id,
            'title': self.title,
            'original_title': self.original_title,
            'overview': self.overview,
            'release_date': self.release_date.isoformat() if self.release_date else None,
            'runtime': self.runtime,
            'vote_average': self.vote_average,
            'vote_count': self.vote_count,
            'popularity': self.popularity,
            'poster_path': self.poster_path,
            'backdrop_path': self.backdrop_path,
            'genre_ids': json.dumps(self.genre_ids),
            'adult': self.adult,
            'original_language': self.original_language,
            'status': self.status,
            'budget': self.budget,
            'revenue': self.revenue
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Movie':
        """Create from dictionary"""
        movie = cls()
        for key, value in data.items():
            if key == 'genre_ids' and isinstance(value, str):
                try:
                    value = json.loads(value)
                except:
                    value = []
            elif key in ['release_date', 'created_at', 'updated_at'] and isinstance(value, str):
                try:
                    value = datetime.fromisoformat(value.replace('Z', '+00:00'))
                except:
                    value = None
            if hasattr(movie, key):
                setattr(movie, key, value)
        return movie

@dataclass
class TVShow:
    """Model for TV show data"""
    id: Optional[int] = None
    tmdb_id: Optional[int] = None
    name: str = ""
    original_name: str = ""
    overview: str = ""
    first_air_date: Optional[datetime] = None
    last_air_date: Optional[datetime] = None
    number_of_episodes: int = 0
    number_of_seasons: int = 0
    episode_run_time: List[int] = field(default_factory=list)
    vote_average: float = 0.0
    vote_count: int = 0
    popularity: float = 0.0
    poster_path: str = ""
    backdrop_path: str = ""
    genre_ids: List[int] = field(default_factory=list)
    origin_country: List[str] = field(default_factory=list)
    original_language: str = ""
    status: str = ""
    type: str = ""
    adult: bool = False
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'id': self.id,
            'tmdb_id': self.tmdb_id,
            'name': self.name,
            'original_name': self.original_name,
            'overview': self.overview,
            'first_air_date': self.first_air_date.isoformat() if self.first_air_date else None,
            'last_air_date': self.last_air_date.isoformat() if self.last_air_date else None,
            'number_of_episodes': self.number_of_episodes,
            'number_of_seasons': self.number_of_seasons,
            'episode_run_time': json.dumps(self.episode_run_time),
            'vote_average': self.vote_average,
            'vote_count': self.vote_count,
            'popularity': self.popularity,
            'poster_path': self.poster_path,
            'backdrop_path': self.backdrop_path,
            'genre_ids': json.dumps(self.genre_ids),
            'origin_country': json.dumps(self.origin_country),
            'original_language': self.original_language,
            'status': self.status,
            'type': self.type,
            'adult': self.adult
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TVShow':
        """Create from dictionary"""
        show = cls()
        for key, value in data.items():
            if key in ['genre_ids', 'origin_country', 'episode_run_time'] and isinstance(value, str):
                try:
                    value = json.loads(value)
                except:
                    value = []
            elif key in ['first_air_date', 'last_air_date', 'created_at', 'updated_at'] and isinstance(value, str):
                try:
                    value = datetime.fromisoformat(value.replace('Z', '+00:00'))
                except:
                    value = None
            if hasattr(show, key):
                setattr(show, key, value)
        return show

@dataclass
class M3UEntry:
    """Model for M3U playlist entry"""
    id: Optional[int] = None
    playlist_id: Optional[int] = None
    title: str = ""
    url: str = ""
    duration: int = -1
    logo: str = ""
    group_title: str = ""
    tvg_id: str = ""
    tvg_name: str = ""
    tvg_logo: str = ""
    tvg_chno: str = ""
    tvg_shift: str = ""
    radio: bool = False
    content_type: str = ""
    quality: str = ""
    language: str = ""
    raw_line: str = ""
    attributes: Dict[str, str] = field(default_factory=dict)
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'id': self.id,
            'playlist_id': self.playlist_id,
            'title': self.title,
            'url': self.url,
            'duration': self.duration,
            'logo': self.logo,
            'group_title': self.group_title,
            'tvg_id': self.tvg_id,
            'tvg_name': self.tvg_name,
            'tvg_logo': self.tvg_logo,
            'tvg_chno': self.tvg_chno,
            'tvg_shift': self.tvg_shift,
            'radio': self.radio,
            'content_type': self.content_type,
            'quality': self.quality,
            'language': self.language,
            'raw_line': self.raw_line,
            'attributes': json.dumps(self.attributes)
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'M3UEntry':
        """Create from dictionary"""
        entry = cls()
        for key, value in data.items():
            if key == 'attributes' and isinstance(value, str):
                try:
                    value = json.loads(value)
                except:
                    value = {}
            elif key in ['created_at', 'updated_at'] and isinstance(value, str):
                try:
                    value = datetime.fromisoformat(value.replace('Z', '+00:00'))
                except:
                    value = None
            if hasattr(entry, key):
                setattr(entry, key, value)
        return entry
    
    def get_hash(self) -> str:
        """Generate hash for duplicate detection"""
        import hashlib
        hash_string = f"{self.title}|{self.url}|{self.group_title}"
        return hashlib.md5(hash_string.encode()).hexdigest()

@dataclass
class Playlist:
    """Model for M3U playlist"""
    id: Optional[int] = None
    name: str = ""
    description: str = ""
    file_path: str = ""
    entry_count: int = 0
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'file_path': self.file_path,
            'entry_count': self.entry_count,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Playlist':
        """Create from dictionary"""
        playlist = cls()
        for key, value in data.items():
            if key in ['created_at', 'updated_at'] and isinstance(value, str):
                try:
                    value = datetime.fromisoformat(value.replace('Z', '+00:00'))
                except:
                    value = None
            if hasattr(playlist, key):
                setattr(playlist, key, value)
        return playlist
