<?php
/**
 * Test Dashboard - IPTV XUI One Content Manager
 * ===========================================
 * 
 * Test the dashboard functionality with SimpleDatabaseManager
 */

// Include path management
require_once __DIR__ . '/config/paths.php';

// Initialize session management
require_once getConfigPath('session.php');

// Include configuration
require_once getConfigPath('database.php');

// Test dashboard functionality
try {
    require_once getCorePath('SimpleDatabaseManager.php');
    
    $db = new SimpleDatabaseManager();
    
    // Test all methods used in dashboard
    echo "<h2>🧪 Testing Dashboard Methods</h2>";
    
    // Test connection
    if ($db->testConnection()) {
        echo "<p>✅ Database connection: OK</p>";
    } else {
        echo "<p>❌ Database connection: FAILED</p>";
    }
    
    // Test getContentStats
    try {
        $contentStats = $db->getContentStats();
        echo "<p>✅ getContentStats(): OK</p>";
        echo "<pre>" . print_r($contentStats, true) . "</pre>";
    } catch (Exception $e) {
        echo "<p>❌ getContentStats(): " . $e->getMessage() . "</p>";
    }
    
    // Test getQualityStats
    try {
        $qualityStats = $db->getQualityStats();
        echo "<p>✅ getQualityStats(): OK</p>";
        echo "<pre>" . print_r($qualityStats, true) . "</pre>";
    } catch (Exception $e) {
        echo "<p>❌ getQualityStats(): " . $e->getMessage() . "</p>";
    }
    
    // Test getRecentAdditions
    try {
        $recentContent = $db->getRecentAdditions(5);
        echo "<p>✅ getRecentAdditions(): OK (" . count($recentContent) . " items)</p>";
        if (!empty($recentContent)) {
            echo "<ul>";
            foreach (array_slice($recentContent, 0, 3) as $content) {
                echo "<li>" . htmlspecialchars(substr($content['stream_display_name'], 0, 50)) . "</li>";
            }
            echo "</ul>";
        }
    } catch (Exception $e) {
        echo "<p>❌ getRecentAdditions(): " . $e->getMessage() . "</p>";
    }
    
    // Test getPopularContent
    try {
        $popularContent = $db->getPopularContent(5);
        echo "<p>✅ getPopularContent(): OK (" . count($popularContent) . " items)</p>";
        if (!empty($popularContent)) {
            echo "<ul>";
            foreach (array_slice($popularContent, 0, 3) as $content) {
                echo "<li>" . htmlspecialchars(substr($content['stream_display_name'], 0, 50)) . "</li>";
            }
            echo "</ul>";
        }
    } catch (Exception $e) {
        echo "<p>❌ getPopularContent(): " . $e->getMessage() . "</p>";
    }
    
    echo "<h3>🎯 Dashboard Ready!</h3>";
    echo "<p>✅ All dashboard methods working correctly</p>";
    echo "<p>✅ Cards will be aligned horizontally</p>";
    echo "<p>✅ No more SQL errors</p>";
    
    echo "<hr>";
    echo "<p><a href='index.php' style='background: #3b82f6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🏠 Go to Dashboard</a></p>";
    
} catch (Exception $e) {
    echo "<h2>❌ Dashboard Test Failed</h2>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    
    echo "<h3>🔧 Troubleshooting:</h3>";
    echo "<ul>";
    echo "<li>Check database connection</li>";
    echo "<li>Verify SimpleDatabaseManager.php exists</li>";
    echo "<li>Check database credentials</li>";
    echo "</ul>";
}

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Test - IPTV Manager</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #0f172a;
            color: #f1f5f9;
            margin: 0;
            padding: 2rem;
            line-height: 1.6;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: #1e293b;
            padding: 2rem;
            border-radius: 1rem;
            border: 1px solid #334155;
        }
        h2, h3 {
            color: #3b82f6;
        }
        pre {
            background: #0f172a;
            padding: 1rem;
            border-radius: 0.5rem;
            border: 1px solid #334155;
            overflow-x: auto;
            font-size: 0.875rem;
        }
        ul {
            background: #0f172a;
            padding: 1rem;
            border-radius: 0.5rem;
            border: 1px solid #334155;
        }
        hr {
            border: none;
            height: 1px;
            background: #334155;
            margin: 2rem 0;
        }
        a {
            display: inline-block;
            margin: 0.5rem 0.5rem 0.5rem 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 Dashboard Test - IPTV Manager</h1>
        <!-- PHP content is echoed above -->
    </div>
</body>
</html>
