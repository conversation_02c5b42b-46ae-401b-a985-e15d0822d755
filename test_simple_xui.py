#!/usr/bin/env python3
"""
Test de conexión simple XUI One
===============================

Script simplificado para probar la conexión a la base de datos XUI One.
"""

import asyncio
import sys
import os
import json

# Añadir el directorio actual al path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.settings import Settings
from core.database_manager import DatabaseManager

async def test_simple_xui():
    """Probar conexión simple a XUI One"""
    
    print("🔍 Test Simple de Base de Datos XUI One")
    print("=" * 40)
    
    # Inicializar configuración
    settings = Settings()
    db_manager = DatabaseManager(settings)
    
    try:
        # Probar conexión básica
        print("1. Probando conexión...")
        connection_ok = await db_manager.test_connection()
        if connection_ok:
            print("✅ Conexión exitosa")
        else:
            print("❌ Error en conexión")
            return False
        
        # Consultar tabla streams directamente
        print("\n2. Consultando tabla streams...")
        async with db_manager.get_connection() as conn:
            cursor = conn.cursor(dictionary=True)
            cursor.execute("SELECT COUNT(*) as total FROM streams")
            result = cursor.fetchone()
            total_streams = result['total']
            print(f"📊 Total de streams: {total_streams}")
            
            # Obtener algunos datos de muestra
            cursor.execute("SELECT id, type, stream_display_name, tmdb_id, year, rating FROM streams LIMIT 5")
            streams = cursor.fetchall()
            
            print("\n📱 Streams de ejemplo:")
            for stream in streams:
                print(f"   - ID: {stream['id']}, Tipo: {stream['type']}, Nombre: {stream['stream_display_name']}")
                print(f"     TMDB: {stream['tmdb_id']}, Año: {stream['year']}, Rating: {stream['rating']}")
            
            cursor.close()
        
        # Consultar tabla streams_categories
        print("\n3. Consultando categorías...")
        async with db_manager.get_connection() as conn:
            cursor = conn.cursor(dictionary=True)
            cursor.execute("SELECT COUNT(*) as total FROM streams_categories")
            result = cursor.fetchone()
            total_categories = result['total']
            print(f"📂 Total de categorías: {total_categories}")
            
            # Obtener categorías de ejemplo
            cursor.execute("SELECT id, category_name, category_type FROM streams_categories LIMIT 5")
            categories = cursor.fetchall()
            
            print("\n📂 Categorías de ejemplo:")
            for cat in categories:
                print(f"   - ID: {cat['id']}, Nombre: {cat['category_name']}, Tipo: {cat['category_type']}")
            
            cursor.close()
        
        # Consultar tabla streams_series
        print("\n4. Consultando series...")
        async with db_manager.get_connection() as conn:
            cursor = conn.cursor(dictionary=True)
            cursor.execute("SELECT COUNT(*) as total FROM streams_series")
            result = cursor.fetchone()
            total_series = result['total']
            print(f"📺 Total de series: {total_series}")
            
            if total_series > 0:
                cursor.execute("SELECT id, title, tmdb_id, year, rating FROM streams_series LIMIT 3")
                series = cursor.fetchall()
                
                print("\n📺 Series de ejemplo:")
                for serie in series:
                    print(f"   - ID: {serie['id']}, Título: {serie['title']}")
                    print(f"     TMDB: {serie['tmdb_id']}, Año: {serie['year']}, Rating: {serie['rating']}")
            
            cursor.close()
        
        # Probar una consulta con movie_properties
        print("\n5. Probando movie_properties...")
        async with db_manager.get_connection() as conn:
            cursor = conn.cursor(dictionary=True)
            cursor.execute("""
                SELECT id, stream_display_name, movie_properties 
                FROM streams 
                WHERE type = 2 AND movie_properties IS NOT NULL 
                AND movie_properties != '' 
                LIMIT 1
            """)
            movie_stream = cursor.fetchone()
            
            if movie_stream:
                print(f"🎬 Película con propiedades: {movie_stream['stream_display_name']}")
                
                # Intentar parsear movie_properties
                try:
                    props = json.loads(movie_stream['movie_properties'])
                    print(f"   - Título TMDB: {props.get('name', 'N/A')}")
                    print(f"   - Año: {props.get('year', 'N/A')}")
                    print(f"   - Rating: {props.get('rating', 'N/A')}")
                    print(f"   - Género: {props.get('genre', 'N/A')}")
                    print(f"   - Director: {props.get('director', 'N/A')}")
                except:
                    print("   - Error al parsear JSON de movie_properties")
            else:
                print("❌ No se encontraron películas con movie_properties")
            
            cursor.close()
        
        print("\n✅ Test completado exitosamente")
        return True
        
    except Exception as e:
        print(f"❌ Error durante el test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        await db_manager.close()

def main():
    """Función principal"""
    result = asyncio.run(test_simple_xui())
    
    if result:
        print("\n🎉 La base de datos XUI One está funcionando correctamente")
    else:
        print("\n💥 Hay problemas con la base de datos")
        sys.exit(1)

if __name__ == "__main__":
    main()
