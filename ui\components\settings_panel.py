"""
Panel de configuración
====================

Panel para configurar ajustes de la aplicación.
"""

import tkinter as tk
from tkinter import messagebox, filedialog
import customtkinter as ctk
import logging
from typing import Dict, Any, Callable

class SettingsPanel:
    """Panel de configuración de la aplicación"""
    
    def __init__(self, parent, settings, on_settings_change: Callable[[Dict[str, Any]], None]):
        self.parent = parent
        self.settings = settings
        self.on_settings_change = on_settings_change
        self.logger = logging.getLogger("iptv_manager.settings")
        
        # Variables de configuración
        self.theme_var = ctk.StringVar(value=settings.theme)
        self.db_host_var = ctk.StringVar(value=settings.db_host)
        self.db_port_var = ctk.StringVar(value=str(settings.db_port))
        self.db_name_var = ctk.StringVar(value=settings.db_name)
        self.db_user_var = ctk.StringVar(value=settings.db_user)
        self.db_password_var = ctk.StringVar(value=settings.db_password)
        self.tmdb_api_key_var = ctk.StringVar(value=settings.tmdb_api_key)
        
        # Crear interfaz
        self._create_layout()
        self._create_sections()
    
    def _create_layout(self):
        """Crear layout principal"""
        # Frame principal
        self.main_frame = ctk.CTkScrollableFrame(self.parent)
        self.main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Título
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="Configuración",
            font=("Arial", 24, "bold")
        )
        title_label.pack(pady=(0, 20))
    
    def _create_sections(self):
        """Crear secciones de configuración"""
        # Sección de apariencia
        self._create_appearance_section()
        
        # Sección de base de datos
        self._create_database_section()
        
        # Sección de API
        self._create_api_section()
        
        # Sección de aplicación
        self._create_app_section()
        
        # Botones de acción
        self._create_action_buttons()
    
    def _create_appearance_section(self):
        """Crear sección de apariencia"""
        # Frame de apariencia
        appearance_frame = ctk.CTkFrame(self.main_frame)
        appearance_frame.pack(fill="x", pady=(0, 20))
        
        # Título
        title_label = ctk.CTkLabel(
            appearance_frame,
            text="🎨 Apariencia",
            font=("Arial", 16, "bold")
        )
        title_label.pack(pady=(15, 10))
        
        # Configuración de tema
        theme_frame = ctk.CTkFrame(appearance_frame)
        theme_frame.pack(fill="x", padx=20, pady=(0, 15))
        
        # Etiqueta de tema
        theme_label = ctk.CTkLabel(
            theme_frame,
            text="Tema:",
            font=("Arial", 12)
        )
        theme_label.pack(side="left", padx=10, pady=10)
        
        # Selector de tema
        theme_combo = ctk.CTkComboBox(
            theme_frame,
            values=["dark", "light", "system"],
            variable=self.theme_var,
            width=150
        )
        theme_combo.pack(side="left", padx=10, pady=10)
        
        # Descripción
        theme_desc = ctk.CTkLabel(
            theme_frame,
            text="Selecciona el tema de la aplicación",
            font=("Arial", 10),
            text_color=self.settings.colors["text_secondary"]
        )
        theme_desc.pack(side="left", padx=10, pady=10)
    
    def _create_database_section(self):
        """Crear sección de base de datos"""
        # Frame de base de datos
        db_frame = ctk.CTkFrame(self.main_frame)
        db_frame.pack(fill="x", pady=(0, 20))
        
        # Título
        title_label = ctk.CTkLabel(
            db_frame,
            text="🗄️ Base de Datos",
            font=("Arial", 16, "bold")
        )
        title_label.pack(pady=(15, 10))
        
        # Configuración de conexión
        config_frame = ctk.CTkFrame(db_frame)
        config_frame.pack(fill="x", padx=20, pady=(0, 15))
        
        # Configurar grid
        config_frame.grid_columnconfigure(1, weight=1)
        
        # Campos de configuración
        fields = [
            ("Host:", self.db_host_var, "localhost"),
            ("Puerto:", self.db_port_var, "3306"),
            ("Base de datos:", self.db_name_var, "iptv_xui"),
            ("Usuario:", self.db_user_var, "root"),
            ("Contraseña:", self.db_password_var, "")
        ]
        
        for i, (label, var, placeholder) in enumerate(fields):
            # Etiqueta
            label_widget = ctk.CTkLabel(
                config_frame,
                text=label,
                font=("Arial", 12),
                width=100
            )
            label_widget.grid(row=i, column=0, padx=10, pady=5, sticky="w")
            
            # Campo de entrada
            entry = ctk.CTkEntry(
                config_frame,
                textvariable=var,
                placeholder_text=placeholder,
                show="*" if "contraseña" in label.lower() else None,
                width=200
            )
            entry.grid(row=i, column=1, padx=10, pady=5, sticky="ew")
        
        # Botón de prueba de conexión
        test_button = ctk.CTkButton(
            db_frame,
            text="🔍 Probar Conexión",
            font=("Arial", 12),
            command=self._test_database_connection
        )
        test_button.pack(pady=(0, 15))
    
    def _create_api_section(self):
        """Crear sección de API"""
        # Frame de API
        api_frame = ctk.CTkFrame(self.main_frame)
        api_frame.pack(fill="x", pady=(0, 20))
        
        # Título
        title_label = ctk.CTkLabel(
            api_frame,
            text="🔑 APIs",
            font=("Arial", 16, "bold")
        )
        title_label.pack(pady=(15, 10))
        
        # Configuración de TMDB
        tmdb_frame = ctk.CTkFrame(api_frame)
        tmdb_frame.pack(fill="x", padx=20, pady=(0, 15))
        
        # Etiqueta
        tmdb_label = ctk.CTkLabel(
            tmdb_frame,
            text="API Key TMDB:",
            font=("Arial", 12)
        )
        tmdb_label.pack(side="left", padx=10, pady=10)
        
        # Campo de entrada
        tmdb_entry = ctk.CTkEntry(
            tmdb_frame,
            textvariable=self.tmdb_api_key_var,
            placeholder_text="Ingresa tu API key de TMDB",
            width=300,
            show="*"
        )
        tmdb_entry.pack(side="left", padx=10, pady=10, fill="x", expand=True)
        
        # Botón de prueba
        test_tmdb_button = ctk.CTkButton(
            tmdb_frame,
            text="🔍 Probar",
            width=80,
            command=self._test_tmdb_api
        )
        test_tmdb_button.pack(side="right", padx=10, pady=10)
        
        # Información
        info_label = ctk.CTkLabel(
            api_frame,
            text="Obtén tu API key gratuita en: https://www.themoviedb.org/settings/api",
            font=("Arial", 10),
            text_color=self.settings.colors["text_secondary"]
        )
        info_label.pack(pady=(0, 15))
    
    def _create_app_section(self):
        """Crear sección de aplicación"""
        # Frame de aplicación
        app_frame = ctk.CTkFrame(self.main_frame)
        app_frame.pack(fill="x", pady=(0, 20))
        
        # Título
        title_label = ctk.CTkLabel(
            app_frame,
            text="⚙️ Aplicación",
            font=("Arial", 16, "bold")
        )
        title_label.pack(pady=(15, 10))
        
        # Configuración general
        general_frame = ctk.CTkFrame(app_frame)
        general_frame.pack(fill="x", padx=20, pady=(0, 15))
        
        # Configurar grid
        general_frame.grid_columnconfigure(1, weight=1)
        
        # Configuraciones
        configs = [
            ("Directorio de logs:", self.settings.logs_dir),
            ("Directorio de assets:", self.settings.assets_dir),
            ("Caché habilitado:", "Sí" if self.settings.cache_enabled else "No"),
            ("Timeout (segundos):", self.settings.timeout)
        ]
        
        for i, (label, value) in enumerate(configs):
            # Etiqueta
            label_widget = ctk.CTkLabel(
                general_frame,
                text=label,
                font=("Arial", 12),
                width=150
            )
            label_widget.grid(row=i, column=0, padx=10, pady=5, sticky="w")
            
            # Valor
            value_widget = ctk.CTkLabel(
                general_frame,
                text=str(value),
                font=("Arial", 12),
                text_color=self.settings.colors["text_secondary"]
            )
            value_widget.grid(row=i, column=1, padx=10, pady=5, sticky="w")
        
        # Botones de utilidad
        utilities_frame = ctk.CTkFrame(app_frame)
        utilities_frame.pack(fill="x", padx=20, pady=(0, 15))
        
        # Configurar grid
        utilities_frame.grid_columnconfigure((0, 1, 2), weight=1)
        
        # Botones
        clear_cache_button = ctk.CTkButton(
            utilities_frame,
            text="🗑️ Limpiar Caché",
            command=self._clear_cache
        )
        clear_cache_button.grid(row=0, column=0, padx=10, pady=10, sticky="ew")
        
        backup_button = ctk.CTkButton(
            utilities_frame,
            text="💾 Backup Config",
            command=self._backup_config
        )
        backup_button.grid(row=0, column=1, padx=10, pady=10, sticky="ew")
        
        restore_button = ctk.CTkButton(
            utilities_frame,
            text="📥 Restaurar Config",
            command=self._restore_config
        )
        restore_button.grid(row=0, column=2, padx=10, pady=10, sticky="ew")
    
    def _create_action_buttons(self):
        """Crear botones de acción"""
        # Frame de botones
        buttons_frame = ctk.CTkFrame(self.main_frame)
        buttons_frame.pack(fill="x", pady=(0, 20))
        
        # Configurar grid
        buttons_frame.grid_columnconfigure((0, 1, 2), weight=1)
        
        # Botones
        save_button = ctk.CTkButton(
            buttons_frame,
            text="💾 Guardar",
            font=("Arial", 12),
            fg_color=self.settings.colors["success"],
            command=self._save_settings
        )
        save_button.grid(row=0, column=0, padx=10, pady=15, sticky="ew")
        
        reset_button = ctk.CTkButton(
            buttons_frame,
            text="🔄 Resetear",
            font=("Arial", 12),
            fg_color=self.settings.colors["warning"],
            command=self._reset_settings
        )
        reset_button.grid(row=0, column=1, padx=10, pady=15, sticky="ew")
        
        cancel_button = ctk.CTkButton(
            buttons_frame,
            text="❌ Cancelar",
            font=("Arial", 12),
            fg_color=self.settings.colors["error"],
            command=self._cancel_settings
        )
        cancel_button.grid(row=0, column=2, padx=10, pady=15, sticky="ew")
    
    def _test_database_connection(self):
        """Probar conexión a la base de datos"""
        try:
            # Crear configuración temporal
            temp_config = {
                "host": self.db_host_var.get(),
                "port": int(self.db_port_var.get()),
                "database": self.db_name_var.get(),
                "user": self.db_user_var.get(),
                "password": self.db_password_var.get()
            }
            
            # Intentar conexión
            import mysql.connector
            connection = mysql.connector.connect(**temp_config)
            connection.close()
            
            messagebox.showinfo("Éxito", "Conexión exitosa a la base de datos")
            
        except Exception as e:
            messagebox.showerror("Error", f"Error de conexión: {str(e)}")
    
    def _test_tmdb_api(self):
        """Probar API de TMDB"""
        try:
            api_key = self.tmdb_api_key_var.get()
            
            if not api_key:
                messagebox.showwarning("Advertencia", "Por favor ingresa una API key")
                return
            
            # Realizar petición de prueba
            import requests
            url = f"https://api.themoviedb.org/3/configuration?api_key={api_key}"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                messagebox.showinfo("Éxito", "API key de TMDB válida")
            else:
                messagebox.showerror("Error", f"API key inválida: {response.status_code}")
                
        except Exception as e:
            messagebox.showerror("Error", f"Error al probar API: {str(e)}")
    
    def _clear_cache(self):
        """Limpiar caché"""
        if messagebox.askyesno("Confirmar", "¿Estás seguro de que quieres limpiar el caché?"):
            try:
                # Limpiar caché de la base de datos
                # Aquí se implementaría la lógica de limpieza
                messagebox.showinfo("Éxito", "Caché limpiado correctamente")
            except Exception as e:
                messagebox.showerror("Error", f"Error al limpiar caché: {str(e)}")
    
    def _backup_config(self):
        """Hacer backup de la configuración"""
        try:
            file_path = filedialog.asksaveasfilename(
                title="Guardar backup de configuración",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )
            
            if file_path:
                # Crear backup de configuración
                config_data = {
                    "theme": self.theme_var.get(),
                    "db_host": self.db_host_var.get(),
                    "db_port": self.db_port_var.get(),
                    "db_name": self.db_name_var.get(),
                    "db_user": self.db_user_var.get(),
                    "tmdb_api_key": self.tmdb_api_key_var.get()
                }
                
                import json
                with open(file_path, 'w') as f:
                    json.dump(config_data, f, indent=2)
                
                messagebox.showinfo("Éxito", "Configuración guardada correctamente")
                
        except Exception as e:
            messagebox.showerror("Error", f"Error al guardar backup: {str(e)}")
    
    def _restore_config(self):
        """Restaurar configuración desde backup"""
        try:
            file_path = filedialog.askopenfilename(
                title="Cargar backup de configuración",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )
            
            if file_path:
                import json
                with open(file_path, 'r') as f:
                    config_data = json.load(f)
                
                # Restaurar configuración
                self.theme_var.set(config_data.get("theme", "dark"))
                self.db_host_var.set(config_data.get("db_host", "localhost"))
                self.db_port_var.set(config_data.get("db_port", "3306"))
                self.db_name_var.set(config_data.get("db_name", "iptv_xui"))
                self.db_user_var.set(config_data.get("db_user", "root"))
                self.tmdb_api_key_var.set(config_data.get("tmdb_api_key", ""))
                
                messagebox.showinfo("Éxito", "Configuración restaurada correctamente")
                
        except Exception as e:
            messagebox.showerror("Error", f"Error al restaurar configuración: {str(e)}")
    
    def _save_settings(self):
        """Guardar configuración"""
        try:
            # Validar campos requeridos
            if not self.db_host_var.get():
                messagebox.showwarning("Advertencia", "El host de la base de datos es requerido")
                return
            
            if not self.db_name_var.get():
                messagebox.showwarning("Advertencia", "El nombre de la base de datos es requerido")
                return
            
            if not self.tmdb_api_key_var.get():
                messagebox.showwarning("Advertencia", "La API key de TMDB es requerida")
                return
            
            # Preparar configuración
            new_settings = {
                "theme": self.theme_var.get(),
                "db_host": self.db_host_var.get(),
                "db_port": int(self.db_port_var.get()),
                "db_name": self.db_name_var.get(),
                "db_user": self.db_user_var.get(),
                "db_password": self.db_password_var.get(),
                "tmdb_api_key": self.tmdb_api_key_var.get()
            }
            
            # Aplicar configuración
            self.on_settings_change(new_settings)
            
            messagebox.showinfo("Éxito", "Configuración guardada correctamente")
            
        except ValueError as e:
            messagebox.showerror("Error", f"Error en los valores: {str(e)}")
        except Exception as e:
            messagebox.showerror("Error", f"Error al guardar configuración: {str(e)}")
    
    def _reset_settings(self):
        """Resetear configuración a valores por defecto"""
        if messagebox.askyesno("Confirmar", "¿Estás seguro de que quieres resetear la configuración?"):
            self.theme_var.set("dark")
            self.db_host_var.set("localhost")
            self.db_port_var.set("3306")
            self.db_name_var.set("iptv_xui")
            self.db_user_var.set("root")
            self.db_password_var.set("")
            self.tmdb_api_key_var.set("201066b4b17391d478e55247f43eed64")
            
            messagebox.showinfo("Éxito", "Configuración restablecida a valores por defecto")
    
    def _cancel_settings(self):
        """Cancelar cambios"""
        # Restaurar valores originales
        self.theme_var.set(self.settings.theme)
        self.db_host_var.set(self.settings.db_host)
        self.db_port_var.set(str(self.settings.db_port))
        self.db_name_var.set(self.settings.db_name)
        self.db_user_var.set(self.settings.db_user)
        self.db_password_var.set(self.settings.db_password)
        self.tmdb_api_key_var.set(self.settings.tmdb_api_key)
        
        messagebox.showinfo("Cancelado", "Cambios cancelados")
    
    def destroy(self):
        """Destruir componente"""
        if hasattr(self, 'main_frame'):
            self.main_frame.destroy()
