"TABLE_NAME"	"COLUMN_NAME"	"DATA_TYPE"
"streams"	"id"	"int"
"streams"	"type"	"int"
"streams"	"category_id"	"longtext"
"streams"	"stream_display_name"	"mediumtext"
"streams"	"stream_source"	"mediumtext"
"streams"	"stream_icon"	"mediumtext"
"streams"	"notes"	"mediumtext"
"streams"	"enable_transcode"	"tinyint"
"streams"	"transcode_attributes"	"mediumtext"
"streams"	"custom_ffmpeg"	"mediumtext"
"streams"	"movie_properties"	"mediumtext"
"streams"	"movie_subtitles"	"mediumtext"
"streams"	"read_native"	"tinyint"
"streams"	"target_container"	"text"
"streams"	"stream_all"	"tinyint"
"streams"	"remove_subtitles"	"tinyint"
"streams"	"custom_sid"	"varchar"
"streams"	"epg_api"	"int"
"streams"	"epg_id"	"int"
"streams"	"channel_id"	"varchar"
"streams"	"epg_lang"	"varchar"
"streams"	"order"	"int"
"streams"	"auto_restart"	"mediumtext"
"streams"	"transcode_profile_id"	"int"
"streams"	"gen_timestamps"	"tinyint"
"streams"	"added"	"int"
"streams"	"series_no"	"int"
"streams"	"direct_source"	"tinyint"
"streams"	"tv_archive_duration"	"int"
"streams"	"tv_archive_server_id"	"int"
"streams"	"tv_archive_pid"	"int"
"streams"	"vframes_server_id"	"int"
"streams"	"vframes_pid"	"int"
"streams"	"movie_symlink"	"tinyint"
"streams"	"rtmp_output"	"tinyint"
"streams"	"allow_record"	"tinyint"
"streams"	"probesize_ondemand"	"int"
"streams"	"custom_map"	"mediumtext"
"streams"	"external_push"	"mediumtext"
"streams"	"delay_minutes"	"int"
"streams"	"tmdb_language"	"varchar"
"streams"	"llod"	"tinyint"
"streams"	"year"	"int"
"streams"	"rating"	"float"
"streams"	"plex_uuid"	"varchar"
"streams"	"uuid"	"varchar"
"streams"	"epg_offset"	"int"
"streams"	"updated"	"timestamp"
"streams"	"similar"	"mediumtext"
"streams"	"tmdb_id"	"int"
"streams"	"adaptive_link"	"mediumtext"
"streams"	"title_sync"	"varchar"
"streams"	"fps_restart"	"tinyint"
"streams"	"fps_threshold"	"int"
"streams"	"direct_proxy"	"tinyint"
"streams_arguments"	"id"	"int"
"streams_arguments"	"argument_cat"	"varchar"
"streams_arguments"	"argument_name"	"varchar"
"streams_arguments"	"argument_description"	"mediumtext"
"streams_arguments"	"argument_wprotocol"	"varchar"
"streams_arguments"	"argument_key"	"varchar"
"streams_arguments"	"argument_cmd"	"varchar"
"streams_arguments"	"argument_type"	"varchar"
"streams_arguments"	"argument_default_value"	"varchar"
"streams_categories"	"id"	"int"
"streams_categories"	"category_type"	"varchar"
"streams_categories"	"category_name"	"varchar"
"streams_categories"	"parent_id"	"int"
"streams_categories"	"cat_order"	"int"
"streams_categories"	"is_adult"	"int"
"streams_episodes"	"id"	"int"
"streams_episodes"	"season_num"	"int"
"streams_episodes"	"episode_num"	"int"
"streams_episodes"	"series_id"	"int"
"streams_episodes"	"stream_id"	"int"
"streams_errors"	"id"	"int"
"streams_errors"	"stream_id"	"int"
"streams_errors"	"server_id"	"int"
"streams_errors"	"date"	"int"
"streams_errors"	"error"	"varchar"
"streams_logs"	"id"	"int"
"streams_logs"	"stream_id"	"int"
"streams_logs"	"server_id"	"int"
"streams_logs"	"action"	"varchar"
"streams_logs"	"source"	"varchar"
"streams_logs"	"date"	"int"
"streams_options"	"id"	"int"
"streams_options"	"stream_id"	"int"
"streams_options"	"argument_id"	"int"
"streams_options"	"value"	"text"
"streams_series"	"id"	"int"
"streams_series"	"title"	"varchar"
"streams_series"	"category_id"	"longtext"
"streams_series"	"cover"	"varchar"
"streams_series"	"cover_big"	"varchar"
"streams_series"	"genre"	"varchar"
"streams_series"	"plot"	"mediumtext"
"streams_series"	"cast"	"mediumtext"
"streams_series"	"rating"	"int"
"streams_series"	"director"	"varchar"
"streams_series"	"release_date"	"varchar"
"streams_series"	"last_modified"	"int"
"streams_series"	"tmdb_id"	"int"
"streams_series"	"seasons"	"mediumtext"
"streams_series"	"episode_run_time"	"int"
"streams_series"	"backdrop_path"	"mediumtext"
"streams_series"	"youtube_trailer"	"mediumtext"
"streams_series"	"tmdb_language"	"varchar"
"streams_series"	"year"	"int"
"streams_series"	"plex_uuid"	"varchar"
"streams_series"	"similar"	"mediumtext"
"streams_servers"	"server_stream_id"	"int"
"streams_servers"	"stream_id"	"int"
"streams_servers"	"server_id"	"int"
"streams_servers"	"parent_id"	"int"
"streams_servers"	"pid"	"int"
"streams_servers"	"to_analyze"	"tinyint"
"streams_servers"	"stream_status"	"int"
"streams_servers"	"stream_started"	"int"
"streams_servers"	"stream_info"	"mediumtext"
"streams_servers"	"monitor_pid"	"int"
"streams_servers"	"aes_pid"	"int"
"streams_servers"	"current_source"	"mediumtext"
"streams_servers"	"bitrate"	"int"
"streams_servers"	"progress_info"	"mediumtext"
"streams_servers"	"cc_info"	"mediumtext"
"streams_servers"	"on_demand"	"tinyint"
"streams_servers"	"delay_pid"	"int"
"streams_servers"	"delay_available_at"	"int"
"streams_servers"	"pids_create_channel"	"mediumtext"
"streams_servers"	"cchannel_rsources"	"mediumtext"
"streams_servers"	"updated"	"timestamp"
"streams_servers"	"compatible"	"tinyint"
"streams_servers"	"audio_codec"	"varchar"
"streams_servers"	"video_codec"	"varchar"
"streams_servers"	"resolution"	"int"
"streams_servers"	"ondemand_check"	"int"
"streams_stats"	"id"	"int"
"streams_stats"	"stream_id"	"int"
"streams_stats"	"rank"	"int"
"streams_stats"	"time"	"int"
"streams_stats"	"connections"	"int"
"streams_stats"	"users"	"int"
"streams_stats"	"type"	"varchar"
"streams_stats"	"dateadded"	"timestamp"
"streams_types"	"type_id"	"int"
"streams_types"	"type_name"	"varchar"
"streams_types"	"type_key"	"varchar"
"streams_types"	"type_output"	"varchar"
"streams_types"	"live"	"tinyint"

tmdb key 201066b4b17391d478e55247f43eed64